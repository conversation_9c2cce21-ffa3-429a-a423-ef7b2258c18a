<?php
session_start();
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/vendor/autoload.php';

$paiementId = $_GET['id'] ?? null;

if (!$paiementId) {
    die('ID du paiement requis');
}

try {
    $db = Database::getInstance()->getConnection();
    
    // Récupérer les détails du paiement de vente
    $stmt = $db->prepare("
        SELECT 
            p.*,
            ve.facture_numero,
            ve.date_vente,
            ve.total_montant as vente_total,
            ve.valeur_euro,
            ve.valeur_ar,
            ve.cours_devise,
            c.nom as client_nom,
            c.adresse as client_adresse,
            c.telephone as client_telephone,
            c.email as client_email,
            c.nif as client_nif,
            c.stat as client_stat,
            c.type_client
        FROM operation_caisse p
        LEFT JOIN ventes_entete ve ON p.vente_id = ve.id
        LEFT JOIN clients c ON ve.client_id = c.id
        WHERE p.id = ? AND p.type_operation = 'ENCAISSEMENT_VENTE'
    ");
    $stmt->execute([$paiementId]);
    $paiement = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$paiement) {
        die('Paiement de vente non trouvé');
    }
    
    // Récupérer les détails des produits vendus
    if (!$paiement['vente_id']) {
        die('Aucune vente associée à ce paiement');
    }
    
    $stmt = $db->prepare("
        SELECT 
            vd.*,
            p.nom as produit_nom,
            p.prix_vente,
            d.libelle as depot_libelle
        FROM ventes_details vd
        LEFT JOIN produits p ON vd.produit_id = p.id
        LEFT JOIN depot d ON vd.depot_id = d.id
        WHERE vd.vente_id = ?
    ");
    $stmt->execute([$paiement['vente_id']]);
    $produits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    die('Erreur: ' . $e->getMessage());
}

function getStatutText($statut) {
    switch ($statut) {
        case 'EN_ATTENTE': return 'En Attente';
        case 'VALIDE': return 'Validé';
        case 'ANNULE': return 'Annulé';
        default: return $statut;
    }
}

function getModeText($mode) {
    switch ($mode) {
        case 'CHEQUE': return 'Chèque';
        case 'VIREMENT': return 'Virement';
        case 'ESPECE': return 'Espèce';
        default: return $mode;
    }
}

function getTypeClientText($type) {
    switch ($type) {
        case 'Export': return 'Export';
        case 'Local': return 'Local';
        default: return $type;
    }
}

$date = date('d/m/Y H:i');
$statutText = getStatutText($paiement['statut']);
$modeText = getModeText($paiement['mode_paiement']);
$typeClientText = getTypeClientText($paiement['type_client']);

// Calculer les totaux
$totalQuantite = 0;
$totalMontant = 0;

foreach ($produits as $produit) {
    $quantite = $produit['qte_tonnes'] ?? 0;
    $prixUnitaire = $produit['prix_vente'] ?? 0;
    $montant = $quantite * $prixUnitaire;
    
    $totalQuantite += $quantite;
    $totalMontant += $montant;
}

// Utiliser le montant du paiement comme référence
$montantPaiement = $paiement['montant'] ?? $totalMontant;

// Générer le PDF avec mPDF
$mpdf = new \Mpdf\Mpdf([
    'mode' => 'utf-8',
    'format' => 'A4',
    'margin_left' => 15,
    'margin_right' => 15,
    'margin_top' => 16,
    'margin_bottom' => 16,
    'margin_header' => 9,
    'margin_footer' => 9
]);

$html = '
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; font-size: 12px; }
        .header { text-align: center; margin-bottom: 30px; }
        .company-name { font-size: 18px; font-weight: bold; color: #2c3e50; }
        .document-title { font-size: 16px; font-weight: bold; margin: 20px 0; }
        .info-section { margin-bottom: 20px; }
        .info-row { margin-bottom: 5px; }
        .label { font-weight: bold; display: inline-block; width: 150px; }
        .value { display: inline-block; }
        .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .table th { background-color: #f8f9fa; font-weight: bold; }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .total-section { margin-top: 20px; }
        .total-row { margin-bottom: 5px; }
        .total-label { font-weight: bold; display: inline-block; width: 200px; text-align: right; }
        .total-value { display: inline-block; font-weight: bold; }
        .footer { margin-top: 40px; text-align: center; font-size: 10px; color: #666; }
        .status-badge { padding: 4px 8px; border-radius: 4px; color: white; font-weight: bold; }
        .status-valide { background-color: #28a745; }
        .status-attente { background-color: #ffc107; color: #000; }
        .status-annule { background-color: #dc3545; }
        .currency-section { background-color: #f8f9fa; padding: 10px; margin: 15px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">ERP CACAO</div>
        <div>Système de Gestion des Ventes</div>
        <div class="document-title">REÇU DE PAIEMENT DE VENTE</div>
    </div>

    <div class="info-section">
        <div class="info-row">
            <span class="label">N° Reçu:</span>
            <span class="value">RV-' . str_pad($paiement['id'], 6, '0', STR_PAD_LEFT) . '</span>
        </div>
        <div class="info-row">
            <span class="label">Date d\'émission:</span>
            <span class="value">' . $date . '</span>
        </div>
        <div class="info-row">
            <span class="label">Référence Vente:</span>
            <span class="value">' . ($paiement['facture_numero'] ?: 'Vente #' . $paiement['vente_id']) . '</span>
        </div>
        <div class="info-row">
            <span class="label">Date de Vente:</span>
            <span class="value">' . date('d/m/Y', strtotime($paiement['date_vente'])) . '</span>
        </div>
        <div class="info-row">
            <span class="label">Statut:</span>
            <span class="status-badge status-' . strtolower($paiement['statut']) . '">' . $statutText . '</span>
        </div>
    </div>

    <div class="info-section">
        <h3>Informations Client</h3>
        <div class="info-row">
            <span class="label">Nom:</span>
            <span class="value">' . htmlspecialchars($paiement['client_nom']) . '</span>
        </div>
        <div class="info-row">
            <span class="label">Type:</span>
            <span class="value">' . $typeClientText . '</span>
        </div>';

if ($paiement['client_adresse']) {
    $html .= '
        <div class="info-row">
            <span class="label">Adresse:</span>
            <span class="value">' . htmlspecialchars($paiement['client_adresse']) . '</span>
        </div>';
}

if ($paiement['client_telephone']) {
    $html .= '
        <div class="info-row">
            <span class="label">Téléphone:</span>
            <span class="value">' . htmlspecialchars($paiement['client_telephone']) . '</span>
        </div>';
}

if ($paiement['client_nif']) {
    $html .= '
        <div class="info-row">
            <span class="label">NIF:</span>
            <span class="value">' . htmlspecialchars($paiement['client_nif']) . '</span>
        </div>';
}

if ($paiement['client_stat']) {
    $html .= '
        <div class="info-row">
            <span class="label">STAT:</span>
            <span class="value">' . htmlspecialchars($paiement['client_stat']) . '</span>
        </div>';
}

$html .= '
    </div>

    <div class="info-section">
        <h3>Détails du Paiement</h3>
        <div class="info-row">
            <span class="label">Mode de Paiement:</span>
            <span class="value">' . $modeText . '</span>
        </div>
        <div class="info-row">
            <span class="label">Date de Paiement:</span>
            <span class="value">' . date('d/m/Y', strtotime($paiement['date_paiement'])) . '</span>
        </div>';

if ($paiement['reference_paiement']) {
    $html .= '
        <div class="info-row">
            <span class="label">Référence:</span>
            <span class="value">' . htmlspecialchars($paiement['reference_paiement']) . '</span>
        </div>';
}

$html .= '
        <div class="info-row">
            <span class="label">Montant Payé:</span>
            <span class="value" style="font-weight: bold; font-size: 14px;">' . number_format($montantPaiement, 0, ',', ' ') . ' Ar</span>
        </div>';

if ($paiement['commentaires']) {
    $html .= '
        <div class="info-row">
            <span class="label">Commentaires:</span>
            <span class="value">' . htmlspecialchars($paiement['commentaires']) . '</span>
        </div>';
}

$html .= '
    </div>';

// Section conversion devise si disponible
if ($paiement['cours_devise'] && ($paiement['valeur_euro'] || $paiement['valeur_ar'])) {
    $html .= '
    <div class="currency-section">
        <h3>Conversion Devise</h3>
        <div class="info-row">
            <span class="label">Cours de Change:</span>
            <span class="value">' . number_format($paiement['cours_devise'], 4, ',', ' ') . ' Ar/EUR</span>
        </div>';
    
    if ($paiement['valeur_euro']) {
        $html .= '
        <div class="info-row">
            <span class="label">Valeur en EUR:</span>
            <span class="value">' . number_format($paiement['valeur_euro'], 2, ',', ' ') . ' €</span>
        </div>';
    }
    
    if ($paiement['valeur_ar']) {
        $html .= '
        <div class="info-row">
            <span class="label">Valeur en Ar:</span>
            <span class="value">' . number_format($paiement['valeur_ar'], 0, ',', ' ') . ' Ar</span>
        </div>';
    }
    
    $html .= '
    </div>';
}

$html .= '
    <table class="table">
        <thead>
            <tr>
                <th>Produit</th>
                <th>Dépôt</th>
                <th>Grade</th>
                <th>Qualité</th>
                <th class="text-right">Quantité (T)</th>
                <th class="text-right">Prix Unitaire</th>
                <th class="text-right">Montant</th>
            </tr>
        </thead>
        <tbody>';

foreach ($produits as $produit) {
    $quantite = $produit['qte_tonnes'] ?? 0;
    $prixUnitaire = $produit['prix_vente'] ?? 0;
    $montant = $quantite * $prixUnitaire;
    
    $html .= '
            <tr>
                <td>' . htmlspecialchars($produit['produit_nom']) . '</td>
                <td>' . htmlspecialchars($produit['depot_libelle'] ?? 'N/A') . '</td>
                <td>' . htmlspecialchars($produit['grade'] ?? 'N/A') . '</td>
                <td>' . htmlspecialchars($produit['qualite'] ?? 'N/A') . '</td>
                <td class="text-right">' . number_format($quantite, 3, ',', ' ') . '</td>
                <td class="text-right">' . number_format($prixUnitaire, 0, ',', ' ') . ' Ar</td>
                <td class="text-right">' . number_format($montant, 0, ',', ' ') . ' Ar</td>
            </tr>';
}

$html .= '
        </tbody>
    </table>

    <div class="total-section">
        <div class="total-row">
            <span class="total-label">Total Quantité:</span>
            <span class="total-value">' . number_format($totalQuantite, 3, ',', ' ') . ' T</span>
        </div>
        <div class="total-row">
            <span class="total-label">Montant Total Vente:</span>
            <span class="total-value">' . number_format($totalMontant, 0, ',', ' ') . ' Ar</span>
        </div>
        <div class="total-row" style="border-top: 2px solid #000; padding-top: 5px;">
            <span class="total-label">MONTANT PAYÉ:</span>
            <span class="total-value" style="font-size: 16px;">' . number_format($montantPaiement, 0, ',', ' ') . ' Ar</span>
        </div>
    </div>

    <div class="footer">
        <p>Reçu généré le ' . $date . ' par le système ERP CACAO</p>
        <p>Ce document certifie le paiement de la vente mentionnée ci-dessus.</p>
    </div>
</body>
</html>';

$mpdf->WriteHTML($html);
$mpdf->Output('Recu_Paiement_Vente_' . $paiement['id'] . '.pdf', 'I');
?>

-- ===== CRÉATION DE LA TABLE OPERATION_CAISSE =====
-- Table pour gérer les opérations de caisse (paiements, avances, remboursements)

CREATE TABLE IF NOT EXISTS `operation_caisse` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `achat_id` INT DEFAULT NULL COMMENT 'ID de l\'achat concerné',
  `fournisseur_id` INT DEFAULT NULL COMMENT 'ID du fournisseur',
  `type_operation` ENUM('PAIEMENT_ACHAT', 'AVANCE', 'REMBOURSEMENT', 'AUTRE') NOT NULL,
  `mode_paiement` ENUM('CHEQUE', 'VIREMENT', 'ESPECE') NOT NULL,
  `reference_paiement` VARCHAR(100) DEFAULT NULL COMMENT 'Numéro de chèque ou référence virement',
  `montant` DECIMAL(18,2) NOT NULL,
  `date_paiement` DATE NOT NULL,
  `effectue_par` VARCHAR(255) DEFAULT NULL,
  `commentaires` TEXT DEFAULT NULL,
  `date_creation` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP(),
  `cree_par` VARCHAR(255) DEFAULT NULL,
  `date_derniere_modif` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP(),
  `dernier_modif_par` VARCHAR(255) DEFAULT NULL,
  
  FOREIGN KEY (`achat_id`) REFERENCES `achat_entete`(`id`) ON DELETE SET NULL,
  FOREIGN KEY (`fournisseur_id`) REFERENCES `producteurs`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Index pour optimiser les requêtes
CREATE INDEX `idx_operation_caisse_achat` ON `operation_caisse`(`achat_id`);
CREATE INDEX `idx_operation_caisse_fournisseur` ON `operation_caisse`(`fournisseur_id`);
CREATE INDEX `idx_operation_caisse_date` ON `operation_caisse`(`date_paiement`);
CREATE INDEX `idx_operation_caisse_type` ON `operation_caisse`(`type_operation`);

-- ===== MISE À JOUR DE LA TABLE ACHAT_ENTETE =====
-- Ajouter les colonnes manquantes si elles n'existent pas

ALTER TABLE `achat_entete` 
ADD COLUMN IF NOT EXISTS `mode_paiement` ENUM('CHEQUE', 'VIREMENT', 'ESPECE') DEFAULT NULL AFTER `statut`,
ADD COLUMN IF NOT EXISTS `reference_paiement` VARCHAR(100) DEFAULT NULL AFTER `mode_paiement`;

-- ===== MISE À JOUR DE LA TABLE ACHAT_DETAIL =====
-- Ajouter les colonnes manquantes si elles n'existent pas

ALTER TABLE `achat_detail` 
ADD COLUMN IF NOT EXISTS `lot_numero` VARCHAR(100) DEFAULT NULL AFTER `nombre_sacs`,
ADD COLUMN IF NOT EXISTS `reduction` DECIMAL(12,2) DEFAULT 0.00 AFTER `montant_ht`;

-- ===== VUE POUR LES AVANCES FOURNISSEURS =====
-- Vue pour calculer les avances et remboursements par fournisseur

CREATE OR REPLACE VIEW `v_avances_fournisseurs` AS
SELECT 
    p.id as fournisseur_id,
    p.nom as fournisseur_nom,
    COALESCE(SUM(CASE WHEN oc.type_operation = 'AVANCE' THEN oc.montant ELSE 0 END), 0) as total_avances,
    COALESCE(SUM(CASE WHEN oc.type_operation = 'REMBOURSEMENT' THEN oc.montant ELSE 0 END), 0) as total_remboursements,
    COALESCE(SUM(CASE WHEN oc.type_operation = 'AVANCE' THEN oc.montant ELSE 0 END), 0) - 
    COALESCE(SUM(CASE WHEN oc.type_operation = 'REMBOURSEMENT' THEN oc.montant ELSE 0 END), 0) as solde_avance
FROM producteurs p
LEFT JOIN operation_caisse oc ON p.id = oc.fournisseur_id
GROUP BY p.id, p.nom;

-- ===== VUE POUR LES PAIEMENTS DÉTAILLÉS =====
-- Vue pour afficher les paiements avec toutes les informations

CREATE OR REPLACE VIEW `v_paiements_detaille` AS
SELECT 
    oc.id,
    oc.achat_id,
    ae.reference_achat,
    p.nom as fournisseur_nom,
    p.contact as fournisseur_contact,
    p.site as fournisseur_site,
    p.classement as fournisseur_classement,
    oc.type_operation,
    oc.mode_paiement,
    oc.reference_paiement,
    oc.montant,
    oc.date_paiement,
    oc.effectue_par,
    oc.commentaires,
    oc.date_creation,
    oc.cree_par
FROM operation_caisse oc
LEFT JOIN achat_entete ae ON oc.achat_id = ae.id
LEFT JOIN producteurs p ON ae.fournisseur_id = p.id
WHERE oc.type_operation = 'PAIEMENT_ACHAT'
ORDER BY oc.date_paiement DESC;

-- ===== DONNÉES DE TEST =====
-- Insérer quelques opérations de test

INSERT INTO `operation_caisse` (
    `achat_id`, `fournisseur_id`, `type_operation`, `mode_paiement`, 
    `reference_paiement`, `montant`, `date_paiement`, `effectue_par`, 
    `commentaires`, `cree_par`
) VALUES 
(1, 1, 'PAIEMENT_ACHAT', 'CHEQUE', 'CHQ-001', 500000.00, '2025-01-15', 'admin', 'Paiement achat cacao', 'admin'),
(2, 2, 'PAIEMENT_ACHAT', 'VIREMENT', 'VIR-001', 750000.00, '2025-01-16', 'admin', 'Paiement virement', 'admin'),
(3, 1, 'AVANCE', 'ESPECE', NULL, 100000.00, '2025-01-10', 'admin', 'Avance pour récolte', 'admin'),
(4, 2, 'REMBOURSEMENT', 'CHEQUE', 'CHQ-002', 50000.00, '2025-01-20', 'admin', 'Remboursement avance', 'admin');

-- ===== COMMENTAIRES =====
-- Cette table permet de gérer :
-- 1. Les paiements d'achats (PAIEMENT_ACHAT)
-- 2. Les avances aux fournisseurs (AVANCE)
-- 3. Les remboursements d'avances (REMBOURSEMENT)
-- 4. Autres opérations de caisse (AUTRE)
--
-- Les vues permettent de :
-- 1. Calculer les soldes d'avances par fournisseur
-- 2. Afficher les paiements avec toutes les informations
-- 3. Optimiser les requêtes de reporting

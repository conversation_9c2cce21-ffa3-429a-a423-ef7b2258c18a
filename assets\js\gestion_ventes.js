// ===== GESTION DES VENTES =====

let ventesTable;
let currentVenteId = null;
let venteDetails = [];

$(document).ready(function() {
    // Initialisation
    initDataTables();
    loadVentes();
    loadClients();
    loadProduits();
    loadDepots();
    loadUnites();
    
    // Event handlers
    setupEventHandlers();
    
    // Générer la référence de vente
    generateReferenceVente();
    
    // Initialiser les dates automatiquement
    setDefaultDates();
    
    // Initialiser Select2
    initSelect2();
});

// ===== INITIALISATION =====

function initDataTables() {
    if ($('#tableVentes').length && !$.fn.DataTable.isDataTable('#tableVentes')) {
        ventesTable = $('#tableVentes').DataTable({
            pageLength: 20,
            lengthMenu: [30, 100, 200],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            columnDefs: [
                { orderable: false, targets: [0, 7] }, // Colonnes checkbox et actions
                { className: "text-center", targets: [0, 5] }, // Statut centré
                { className: "text-end", targets: [6] } // Montant aligné à droite
            ]
        });
    }
}

// ===== CHARGEMENT DES DONNÉES =====

function loadVentes() {
    // Vérifier d'abord si les tables existent
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT COUNT(*) as table_exists 
              FROM information_schema.tables 
              WHERE table_schema = DATABASE() 
              AND table_name IN ('ventes_entete', 'ventes_details')`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success && response.data.length > 0 && response.data[0].table_exists == 2) {
            // Les tables existent, charger les ventes
            loadVentesData();
        } else {
            // Les tables n'existent pas, afficher un message
            showAlert('Configuration requise', 
                'Les tables de vente n\'existent pas encore. Veuillez exécuter le script de configuration.', 
                'warning');
            displayVentes([]); // Afficher un tableau vide
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors de la vérification des tables:', error);
        displayVentes([]); // Afficher un tableau vide en cas d'erreur
    });
}

function loadVentesData() {
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                ve.id,
                ve.facture_numero as reference_vente,
                c.nom as client_nom,
                ve.date_vente,
                ve.facture_date,
                ve.statut,
                ve.total_montant,
                ve.valeur_euro,
                ve.valeur_ar
              FROM ventes_entete ve
              LEFT JOIN clients c ON ve.client_id = c.id
              ORDER BY ve.date_creation DESC`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            displayVentes(response.data);
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement des ventes:', error);
        showAlert('Erreur', 'Impossible de charger les ventes', 'error');
    });
}

function displayVentes(ventes) {
    if (ventesTable) {
        ventesTable.clear();
        ventes.forEach(vente => {
            const statutBadge = getStatutBadge(vente.statut);
            const montantFormatted = parseFloat(vente.total_montant || 0).toLocaleString() + ' Ar';
            
            // Générer les boutons selon le statut
            let actionButtons = generateActionButtons(vente);
            
            ventesTable.row.add([
                `<input type="checkbox" value="${vente.id}">`,
                vente.reference_vente || 'N/A',
                vente.client_nom || 'N/A',
                vente.date_vente || 'N/A',
                vente.facture_date || 'N/A',
                statutBadge,
                montantFormatted,
                actionButtons
            ]);
        });
        ventesTable.draw();
        
        // Mettre à jour les statistiques
        updateStatistics(ventes);
    }
}

function displayVentesFiltered(ventes) {
    if (ventesTable) {
        ventesTable.clear();
        ventes.forEach(vente => {
            const statutBadge = getStatutBadge(vente.statut);
            const montantFormatted = parseFloat(vente.total_montant || 0).toLocaleString() + ' Ar';
            
            // Générer les boutons selon le statut
            let actionButtons = generateActionButtons(vente);
            
            ventesTable.row.add([
                `<input type="checkbox" value="${vente.id}">`,
                vente.reference_vente || 'N/A',
                vente.client_nom || 'N/A',
                vente.date_vente || 'N/A',
                vente.facture_date || 'N/A',
                statutBadge,
                montantFormatted,
                actionButtons
            ]);
        });
        ventesTable.draw();
        
        // Ne pas mettre à jour les statistiques lors du filtrage
        // Les statistiques doivent toujours refléter toutes les données
    }
}

function updateStatistics(ventes) {
    // Calculer les statistiques sur toutes les données, pas seulement celles filtrées
    loadAllStatistics();
}

function loadAllStatistics() {
    // Charger toutes les données pour les statistiques (sans filtres)
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_entete',
        sql: `SELECT 
                ve.id,
                ve.statut,
                ve.client_id,
                COALESCE(ve.total_montant, 0) as montant_total
              FROM ventes_entete ve
              ORDER BY ve.date_creation DESC`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            const allVentes = response.data;
            
            const totalVentes = allVentes.length;
            const montantTotal = allVentes.reduce((sum, vente) => sum + parseFloat(vente.montant_total || 0), 0);
            const clients = new Set(allVentes.map(vente => vente.client_id)).size;
            const enAttente = allVentes.filter(vente => vente.statut === 'EN ATTENTE').length;
            const aFacturer = allVentes.filter(vente => vente.statut === 'EN COURS').length;
            const payes = allVentes.filter(vente => vente.statut === 'PAYE').length;
            
            $('#statTotalVentes').text(totalVentes);
            $('#statMontantTotal').text(montantTotal.toLocaleString() + ' Ar');
            $('#statClients').text(clients);
            $('#statEnAttente').text(enAttente);
            $('#statAFacturer').text(aFacturer);
            $('#statPayes').text(payes);
        }
    }).fail(function() {
        console.error('Erreur lors du chargement des statistiques');
    });
}

function generateActionButtons(vente) {
    let buttons = '';

    // Bouton Voir (toujours disponible)
    buttons += `<button class="btn btn-sm btn-outline-info btn-view-vente" data-id="${vente.id}" title="Voir détails">
        <i class="fas fa-eye"></i>
    </button> `;

    // Bouton Modifier (seulement si EN COURS)
    if (vente.statut === 'EN COURS') {
        buttons += `<button class="btn btn-sm btn-outline-warning btn-edit-vente" data-id="${vente.id}" title="Modifier">
            <i class="fas fa-edit"></i>
        </button> `;
    }

    // Bouton Mettre en attente (seulement si EN COURS)
    if (vente.statut === 'EN COURS') {
        buttons += `<button class="btn btn-sm btn-outline-warning btn-pending-vente" data-id="${vente.id}" title="Mettre en attente">
            <i class="fas fa-clock"></i>
        </button> `;
    }

    // Bouton Facturer (seulement si EN ATTENTE)
    if (vente.statut === 'EN ATTENTE') {
        buttons += `<button class="btn btn-sm btn-outline-primary btn-invoice-vente" data-id="${vente.id}" title="Facturer">
            <i class="fas fa-file-invoice"></i>
        </button> `;
    }

    // Bouton Paiement supprimé - utiliser la gestion des paiements de ventes

    // Bouton Annuler (seulement si pas ANNULE et pas PAYE)
    if (vente.statut !== 'ANNULE' && vente.statut !== 'PAYE') {
        buttons += `<button class="btn btn-sm btn-outline-danger btn-cancel-vente" data-id="${vente.id}" title="Annuler">
            <i class="fas fa-times"></i>
        </button> `;
    }

    // Bouton Supprimer (seulement si EN COURS)
    if (vente.statut === 'EN COURS') {
        buttons += `<button class="btn btn-sm btn-outline-danger btn-delete-vente" data-id="${vente.id}" title="Supprimer">
            <i class="fas fa-trash"></i>
        </button> `;
    }

    // Bouton Envoyer par email (toujours disponible)
    buttons += `<button class="btn btn-sm btn-outline-info btn-email-vente" data-id="${vente.id}" title="Envoyer par email">
        <i class="fas fa-envelope"></i>
    </button> `;

    // Bouton Imprimer (toujours disponible)
    buttons += `<button class="btn btn-sm btn-outline-secondary btn-print-vente" data-id="${vente.id}" title="Imprimer">
        <i class="fas fa-print"></i>
    </button>`;

    return buttons;
}

function getStatutBadge(statut) {
    // Utiliser les nouvelles valeurs enum de la DB: 'EN COURS','EN ATTENTE','FACTURE','PAYE','ANNULE'
    const badges = {
        'EN COURS': '<span class="badge bg-info status-badge">En cours</span>',
        'EN ATTENTE': '<span class="badge bg-warning status-badge">En attente</span>',
        'FACTURE': '<span class="badge bg-primary status-badge">Facturé</span>',
        'PAYE': '<span class="badge bg-success status-badge">Payé</span>',
        'ANNULE': '<span class="badge bg-dark status-badge">Annulé</span>'
    };
    return badges[statut] || '<span class="badge bg-secondary status-badge">Inconnu</span>';
}

// ===== CHARGEMENT DES DONNÉES DE RÉFÉRENCE =====

function loadClients() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'clients'
    }).done(function(response) {
        if (response.success) {
            const select = $('#clientVente');
            const filterSelect = $('#filterClient');

            select.empty().append('<option value="">Sélectionner un client</option>');
            filterSelect.empty().append('<option value="">Tous les clients</option>');

            response.data.forEach(client => {
                select.append(`<option value="${client.id}">${client.nom} (${client.type_client})</option>`);
                filterSelect.append(`<option value="${client.id}">${client.nom} (${client.type_client})</option>`);
            });
        }
    });
}

function loadProduits() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'produits'
    }).done(function(response) {
        if (response.success) {
            window.produitsCache = {};
            response.data.forEach(produit => {
                window.produitsCache[produit.id] = produit;
            });
        }
    });
}

function loadDepots() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'depot'
    }).done(function(response) {
        if (response.success) {
            window.depotsCache = {};
            response.data.forEach(depot => {
                window.depotsCache[depot.id] = depot;
            });
        }
    });
}

function loadUnites() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'unites'
    }).done(function(response) {
        if (response.success) {
            window.unitesCache = {};
            response.data.forEach(unite => {
                window.unitesCache[unite.id] = unite;
            });
        }
    });
}

// ===== GESTION DES ÉVÉNEMENTS =====

function setupEventHandlers() {
    // Boutons d'action
    $(document).on('click', '.btn-view-vente', function() {
        const id = $(this).data('id');
        viewVente(id);
    });

    $(document).on('click', '.btn-edit-vente', function() {
        const id = $(this).data('id');
        editVente(id);
    });

    $(document).on('click', '.btn-delete-vente', function() {
        const id = $(this).data('id');
        deleteVente(id);
    });

    // Nouveaux boutons d'action pour les ventes
    $(document).on('click', '.btn-pending-vente', function() {
        const id = $(this).data('id');
        pendingVente(id);
    });

    $(document).on('click', '.btn-invoice-vente', function() {
        const id = $(this).data('id');
        invoiceVente(id);
    });

    // Événement paiement supprimé - utiliser la gestion des paiements de ventes

    $(document).on('click', '.btn-cancel-vente', function() {
        const id = $(this).data('id');
        cancelVente(id);
    });

    $(document).on('click', '.btn-email-vente', function() {
        const id = $(this).data('id');
        sendEmailVente(id);
    });

    $(document).on('click', '.btn-print-vente', function() {
        const id = $(this).data('id');
        printVente(id);
    });

    // Bouton d'annulation multiple
    $('#btnCancelVentes').on('click', function() {
        const selectedIds = getSelectedVenteIds();
        if (selectedIds.length === 0) {
            showAlert('Erreur', 'Veuillez sélectionner au moins une vente', 'warning');
            return;
        }
        cancelVentes(selectedIds);
    });

    // Filtres
    $('#btnApplyFilters').on('click', function() {
        applyFilters();
    });

    $('#btnResetFilters').on('click', function() {
        resetFilters();
    });

    // Application automatique des filtres lors du changement
    $('#filterStatus, #filterDateFrom, #filterDateTo, #filterClient').on('change', function() {
        applyFilters();
    });

    // Sauvegarde de vente
    $('#btnSaveVente').on('click', function() {
        saveVente();
    });

    // Bouton Nouvelle Vente
    $('[data-bs-target="#modalVente"]').click(function() {
        currentVenteId = null;
        resetVenteForm();
        // Mettre à jour le titre du modal et le bouton
        $('#modalVenteTitle').html('<i class="fas fa-chart-line"></i> Nouvelle Vente');
        $('#btnSaveText').text('Enregistrer la Vente');
    });

    // Ajout de produit
    $('#btnAddProduitVente').on('click', function() {
        addProduitVente();
    });

    // Suppression de produit
    $(document).on('click', '.btn-remove-produit', function() {
        const row = $(this).closest('tr');
        const produitSelect = row.find('.produit-select');
        const produitNom = produitSelect.find('option:selected').text() || 'ce produit';

        Swal.fire({
            title: 'Supprimer le produit',
            text: `Êtes-vous sûr de vouloir supprimer ${produitNom} de cette vente ?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6'
        }).then((result) => {
            if (result.isConfirmed) {
                row.remove();
                updateTotals();
                showAlert('Succès', 'Produit supprimé de la vente', 'success');
            }
        });
    });

    // Changement de quantité/prix
    $(document).on('input', '.qte-tonnes, .prix-unitaire', function() {
        updateRowCalculations($(this).closest('tr'));
        updateTotals();
    });

    // Changement de cours de devise
    $('#coursDevise').on('input', function() {
        updateCurrencyConversion();
    });

    // Changement de mode de paiement
    $('#modePaiement').on('change', function() {
        const mode = $(this).val();
        const referenceGroup = $('#referencePaiementGroup');
        const referenceLabel = $('#referencePaiementLabel');

        if (mode === 'CHEQUE') {
            referenceGroup.show();
            referenceLabel.text('Numéro de chèque');
            $('#referencePaiement').attr('placeholder', 'Numéro de chèque');
        } else if (mode === 'VIREMENT') {
            referenceGroup.show();
            referenceLabel.text('Référence virement');
            $('#referencePaiement').attr('placeholder', 'Référence virement');
        } else {
            referenceGroup.hide();
        }
    });

    // Confirmation de paiement supprimée - utiliser gestion_paiements_ventes.php
}

// ===== FONCTIONS UTILITAIRES =====

function generateReferenceVente() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    const reference = `VTE-${year}${month}${day}-${hours}${minutes}`;
    $('#referenceVente').val(reference);
}

function setDefaultDates() {
    const today = new Date().toISOString().split('T')[0];
    $('#dateVente').val(today);
    $('#dateFacture').val(today);
    $('#datePaiement').val(today);
}

function initSelect2() {
    $('#clientVente').select2({
        theme: 'bootstrap-5',
        dropdownParent: $('#modalVente'),
        placeholder: 'Sélectionner un client',
        allowClear: true
    });

    $('#filterClient').select2({
        theme: 'bootstrap-5',
        placeholder: 'Tous les clients',
        allowClear: true
    });
}

function resetVenteForm() {
    $('#formVenteEntete')[0].reset();
    $('#tableDetailsVente tbody').empty();
    venteDetails = [];
    generateReferenceVente();
    setDefaultDates();
    updateTotals();

    // Réinitialiser Select2
    $('#clientVente').val(null).trigger('change');
}

function getSelectedVenteIds() {
    const selectedIds = [];
    $('#tableVentes tbody input[type="checkbox"]:checked').each(function() {
        selectedIds.push($(this).val());
    });
    return selectedIds;
}

function showAlert(title, message, type = 'info') {
    Swal.fire({
        title: title,
        text: message,
        icon: type,
        confirmButtonText: 'OK'
    });
}

function showConfirm(title, message, confirmText = 'Confirmer', cancelText = 'Annuler') {
    return Swal.fire({
        title: title,
        text: message,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: confirmText,
        cancelButtonText: cancelText,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33'
    });
}

// ===== GESTION DES PRODUITS =====

function addProduitVente() {
    if (!window.produitsCache) {
        showAlert('Erreur', 'Les produits ne sont pas encore chargés', 'error');
        return;
    }

    const rowId = 'row_' + Date.now();
    let produitOptions = '<option value="">Sélectionner un produit</option>';
    let depotOptions = '<option value="">Sélectionner un dépôt</option>';

    Object.values(window.produitsCache).forEach(produit => {
        produitOptions += `<option value="${produit.id}" data-prix="${produit.prix_unitaire || 0}">${produit.nom}</option>`;
    });

    // Charger les dépôts
    if (window.depotsCache) {
        Object.values(window.depotsCache).forEach(depot => {
            depotOptions += `<option value="${depot.id}">${depot.libelle}</option>`;
        });
    }

    const newRow = `
        <tr id="${rowId}">
            <td>
                <select class="form-select form-select-sm produit-select" required>
                    ${produitOptions}
                </select>
            </td>
            <td>
                <select class="form-select form-select-sm depot-select" required>
                    ${depotOptions}
                </select>
            </td>
            <td>
                <select class="form-select form-select-sm grade">
                    <option value="">Sélectionner un grade</option>
                    <option value="Standard">Standard</option>
                    <option value="Supérieur">Supérieur</option>
                    <option value="Premium">Premium</option>
                    <option value="Vanille">Vanille</option>
                </select>
            </td>
            <td>
                <select class="form-select form-select-sm qualite">
                    <option value="">Sélectionner une qualité</option>
                    <option value="Bio">Bio</option>
                    <option value="Conventionnel">Conventionnel</option>
                </select>
            </td>
            <td>
                <input type="number" class="form-control form-control-sm qte-tonnes" step="0.001" min="0" placeholder="0.000">
            </td>
            <td>
                <input type="number" class="form-control form-control-sm nbre-lot" min="0" placeholder="Nb lots">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm bl" placeholder="BL">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm conteneur" placeholder="Conteneur">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm seal" placeholder="Seal">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm lots" placeholder="Lots">
            </td>
            <td>
                <input type="text" class="form-control form-control-sm expedition" placeholder="Expédition">
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger btn-remove-produit" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;

    $('#tableDetailsVente tbody').append(newRow);

    // Événement pour le changement de produit
    $(`#${rowId} .produit-select`).on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const prix = selectedOption.data('prix') || 0;
        $(`#${rowId} .prix-unitaire`).val(prix);
        updateRowCalculations($(`#${rowId}`));
        updateTotals();
    });
}

function updateRowCalculations(row) {
    const qte = parseFloat(row.find('.qte-tonnes').val()) || 0;
    const prix = parseFloat(row.find('.prix-unitaire').val()) || 0;
    const montant = qte * prix;

    row.find('.montant-ligne').text(montant.toLocaleString() + ' Ar');
}

function updateTotals() {
    let totalHT = 0;

    $('#tableDetailsVente tbody tr').each(function() {
        const qte = parseFloat($(this).find('.qte-tonnes').val()) || 0;
        const prix = parseFloat($(this).find('.prix-unitaire').val()) || 0;
        totalHT += qte * prix;
    });

    const remise = 0; // Pour l'instant, pas de remise
    const totalTTC = totalHT - remise;

    $('#montantHT').text(totalHT.toLocaleString() + ' Ar');
    $('#montantRemise').text(remise.toLocaleString() + ' Ar');
    $('#montantTTC').text(totalTTC.toLocaleString() + ' Ar');

    updateCurrencyConversion();
}

function updateCurrencyConversion() {
    const totalAr = parseFloat($('#montantTTC').text().replace(/[^\d.-]/g, '')) || 0;
    const cours = parseFloat($('#coursDevise').val()) || 4875.40; // Cours par défaut
    const totalEur = totalAr / cours;

    $('#valeurEUR').text(totalEur.toFixed(2) + ' €');
    $('#valeurAR').text(totalAr.toLocaleString() + ' Ar');
}

// ===== OPÉRATIONS CRUD =====

function saveVente() {
    // Validation du formulaire
    if (!validateVenteForm()) {
        return;
    }

    const venteData = collectVenteData();

    if (currentVenteId) {
        // Modification
        updateVente(currentVenteId, venteData);
    } else {
        // Création
        createVente(venteData);
    }
}

function validateVenteForm() {
    const clientId = $('#clientVente').val();
    if (!clientId) {
        showAlert('Erreur', 'Veuillez sélectionner un client', 'error');
        return false;
    }

    const rows = $('#tableDetailsVente tbody tr');
    if (rows.length === 0) {
        showAlert('Erreur', 'Veuillez ajouter au moins un produit', 'error');
        return false;
    }

    // Vérifier que tous les produits ont au minimum un produit et une quantité
    let isValid = true;
    rows.each(function() {
        const produitId = $(this).find('.produit-select').val();
        const qte = parseFloat($(this).find('.qte-tonnes').val()) || 0;

        if (!produitId || qte <= 0) {
            isValid = false;
            return false;
        }
    });

    if (!isValid) {
        showAlert('Erreur', 'Veuillez remplir tous les champs des produits (produit et quantité > 0)', 'error');
        return false;
    }

    return true;
}

function collectVenteData() {
    const totalAr = parseFloat($('#montantTTC').text().replace(/[^\d.-]/g, '')) || 0;
    const totalRemise = parseFloat($('#montantRemise').text().replace(/[^\d.-]/g, '')) || 0;
    const cours = parseFloat($('#coursDevise').val()) || 4875.40;
    const totalEur = totalAr / cours;

    // Collecter TOUTES les données de l'en-tête selon la structure ventes_entete
    const venteData = {
        client_id: $('#clientVente').val(),
        n_domiciliation: $('#nDomiciliation').val() || null,
        total_montant: totalAr,
        total_remise: totalRemise,
        date_vente: $('#dateVente').val(),
        statut: 'EN COURS', // Utiliser les nouvelles valeurs enum de la DB: 'EN COURS','EN ATTENTE','FACTURE','PAYE','ANNULE'
        valeur_euro: totalEur,
        valeur_ar: totalAr,
        cours_devise: cours,
        dau_numero: $('#dauNumero').val() || null,
        dau_date: $('#dauDate').val() || null,
        facture_numero: $('#numeroFacture').val() || $('#referenceVente').val(),
        facture_date: $('#dateFacture').val(),
        lieux_exportation: $('#lieuExportation').val() || null,
        cree_par: 'system', // Utilisateur qui crée
        details: []
    };

    // Collecter TOUS les détails selon la structure ventes_details
    $('#tableDetailsVente tbody tr').each(function() {
        const detail = {
            produit_id: $(this).find('.produit-select').val(),
            depot_id: $(this).find('.depot-select').val() || 1, // Dépôt par défaut
            expedition: $(this).find('.expedition').val() || null,
            grade: $(this).find('.grade').val() || null,
            qualite: $(this).find('.qualite').val() || null,
            qte_tonnes: parseFloat($(this).find('.qte-tonnes').val()) || 0,
            qte_dernier_stock: 0, // À calculer depuis le stock actuel
            nbre_lot: parseInt($(this).find('.nbre-lot').val()) || null,
            bl: $(this).find('.bl').val() || null,
            conteneur: $(this).find('.conteneur').val() || null,
            seal: $(this).find('.seal').val() || null,
            lots: $(this).find('.lots').val() || null,
            cree_par: 'system' // Utilisateur qui crée
        };
        venteData.details.push(detail);
    });

    return venteData;
}

function createVente(venteData) {
    // Utiliser l'action 'create' comme dans le système d'achats
    $.post('../includes/traitement.php', {
        action: 'create',
        table: 'ventes_entete',
        data: JSON.stringify({
            client_id: venteData.client_id,
            n_domiciliation: venteData.n_domiciliation,
            total_montant: venteData.total_montant,
            total_remise: venteData.total_remise,
            date_vente: venteData.date_vente,
            statut: venteData.statut,
            valeur_euro: venteData.valeur_euro,
            valeur_ar: venteData.valeur_ar,
            cours_devise: venteData.cours_devise,
            dau_numero: venteData.dau_numero,
            dau_date: venteData.dau_date,
            facture_numero: venteData.facture_numero,
            facture_date: venteData.facture_date,
            lieux_exportation: venteData.lieux_exportation,
            cree_par: venteData.cree_par
        })
    }).done(function(response) {
        if (response.success) {
            // Récupérer l'ID de la vente créée
            const venteId = response.id;
            createVenteDetails(venteId, venteData.details);
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de créer la vente', 'error');
    });
}

function createVenteDetails(venteId, details) {
    let detailsCreated = 0;
    const totalDetails = details.length;

    details.forEach(detail => {
        // Récupérer le stock actuel pour qte_dernier_stock
        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'produits_stock',
            sql: `SELECT COALESCE(SUM(quantite), 0) as stock_actuel
                  FROM produits_stock
                  WHERE produit_id = ? AND depot_id = ?`,
            params: JSON.stringify([detail.produit_id, detail.depot_id])
        }).done(function(stockResponse) {
            const stockActuel = stockResponse.success && stockResponse.data.length > 0
                ? parseFloat(stockResponse.data[0].stock_actuel)
                : 0;

            // Utiliser l'action 'create' comme dans le système d'achats
            $.post('../includes/traitement.php', {
                action: 'create',
                table: 'ventes_details',
                data: JSON.stringify({
                    vente_id: venteId,
                    produit_id: detail.produit_id,
                    depot_id: detail.depot_id,
                    expedition: detail.expedition,
                    grade: detail.grade,
                    qualite: detail.qualite,
                    qte_tonnes: detail.qte_tonnes,
                    qte_dernier_stock: stockActuel,
                    nbre_lot: detail.nbre_lot,
                    bl: detail.bl,
                    conteneur: detail.conteneur,
                    seal: detail.seal,
                    lots: detail.lots,
                    cree_par: detail.cree_par
                })
            }).done(function(insertResponse) {
                if (insertResponse.success) {
                    // Créer le mouvement de stock SORTIE (inverse de ENTREE pour achats)
                    createMouvementStockVente(detail, venteId, stockActuel);

                    detailsCreated++;
                    if (detailsCreated === totalDetails) {
                        showAlert('Succès', 'Vente créée avec succès', 'success');
                        loadVentes();
                        $('#modalVente').modal('hide');
                    }
                } else {
                    showAlert('Erreur', insertResponse.message, 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Erreur lors de la création des détails', 'error');
            });
        }).fail(function() {
            showAlert('Erreur', 'Erreur lors de la récupération du stock', 'error');
        });
    });
}

function editVente(id) {
    currentVenteId = id;

    // Charger les données de la vente
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_entete',
        sql: `SELECT * FROM ventes_entete WHERE id = ?`,
        params: JSON.stringify([id])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const vente = response.data[0];

            // Remplir le formulaire
            $('#clientVente').val(vente.client_id).trigger('change');
            $('#dateVente').val(vente.date_vente);
            $('#dateFacture').val(vente.facture_date);
            $('#numeroFacture').val(vente.facture_numero);
            $('#lieuExportation').val(vente.lieu_exportation);
            $('#coursDevise').val(vente.cours_devise);
            $('#referenceVente').val(vente.facture_numero);

            // Charger les détails
            loadVenteDetails(id);

            // Mettre à jour le titre du modal
            $('#modalVenteTitle').html('<i class="fas fa-edit"></i> Modifier la Vente');
            $('#btnSaveText').text('Mettre à jour la Vente');

            $('#modalVente').modal('show');
        } else {
            showAlert('Erreur', 'Vente introuvable', 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de charger la vente', 'error');
    });
}

function loadVenteDetails(venteId) {
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_details',
        sql: `SELECT vd.*, p.nom as produit_nom
              FROM ventes_details vd
              LEFT JOIN produits p ON vd.produit_id = p.id
              WHERE vd.vente_id = ?`,
        params: JSON.stringify([venteId])
    }).done(function(response) {
        if (response.success) {
            $('#tableDetailsVente tbody').empty();

            response.data.forEach(detail => {
                addProduitVente();
                const lastRow = $('#tableDetailsVente tbody tr:last');

                lastRow.find('.produit-select').val(detail.produit_id);
                lastRow.find('.grade').val(detail.grade);
                lastRow.find('.qualite').val(detail.qualite);
                lastRow.find('.qte-tonnes').val(detail.quantite_tonnes);
                lastRow.find('.prix-unitaire').val(detail.prix_unitaire);
                lastRow.find('.date-expedition').val(detail.date_expedition);

                updateRowCalculations(lastRow);
            });

            updateTotals();
        }
    });
}

// ===== GESTION DES MOUVEMENTS DE STOCK =====

function createMouvementStockVente(detail, venteId, stockAvant) {
    // Calculer la conversion pour l'affichage (même logique que les achats)
    let conversionInfo = '';
    const quantiteKg = detail.qte_tonnes * 1000; // Convertir tonnes en kg

    // Calculer les sacs (1 sac = 67 kg par défaut)
    const kgParSac = 67;
    const sacs = Math.floor(quantiteKg / kgParSac);
    const resteKg = quantiteKg % kgParSac;

    if (sacs > 0) {
        conversionInfo = ` (${sacs} sac`;
        if (sacs > 1) conversionInfo += 's';
        if (resteKg > 0) {
            conversionInfo += ` + ${resteKg.toFixed(2)} kg`;
        }
        conversionInfo += ')';
    }

    const mouvementData = {
        produit_id: detail.produit_id,
        depot_id: detail.depot_id || 1,
        type_mouvement: 'SORTIE', // SORTIE pour les ventes (inverse de ENTREE pour achats)
        quantite: quantiteKg, // Quantité en kg
        unite_id: 1, // Unité kg par défaut
        lot_numero: detail.lots || `VTE-${venteId}`,
        reference_document: `VTE-${venteId}`,
        motif: `Vente ${venteId} - ${quantiteKg} kg${conversionInfo}`,
        date_mouvement: new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0],
        // stock_avant: stockAvant,
        // stock_apres: Math.max(0, stockAvant - quantiteKg)
    };

    console.log('Création mouvement de stock vente:', mouvementData);

    $.post('../includes/traitement.php', {
        action: 'create',
        table: 'mouvements_stock',
        data: JSON.stringify(mouvementData)
    }).done(function(response) {
        if (response.success) {
            console.log('Mouvement de stock vente créé avec succès');
            // Mettre à jour le stock du produit
            updateStockAfterVente(detail.produit_id, detail.depot_id, quantiteKg);
        } else {
            console.error('Erreur lors de la création du mouvement de stock vente:', response.message);
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur AJAX lors de la création du mouvement de stock vente:', error);
    });
}

function updateStockAfterVente(produitId, depotId, quantiteKg) {
    // Récupérer le stock actuel
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits_stock',
        sql: `SELECT id, quantite FROM produits_stock
              WHERE produit_id = ? AND depot_id = ?`,
        params: JSON.stringify([produitId, depotId])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const stock = response.data[0];
            const nouvelleQuantite = Math.max(0, parseFloat(stock.quantite) - quantiteKg);

            // Mettre à jour le stock (cette partie utilise déjà la bonne action 'update')
            $.post('../includes/traitement.php', {
                action: 'update',
                table: 'produits_stock',
                id: stock.id,
                data: JSON.stringify({ quantite: nouvelleQuantite })
            }).done(function(updateResponse) {
                if (updateResponse.success) {
                    console.log(`Stock mis à jour: ${stock.quantite} -> ${nouvelleQuantite} kg`);
                } else {
                    console.error('Erreur lors de la mise à jour du stock:', updateResponse.message);
                }
            });
        } else {
            console.warn('Stock non trouvé pour le produit', produitId, 'dans le dépôt', depotId);
        }
    });
}

// ===== WORKFLOW DES VENTES =====

function pendingVente(id) {
    showConfirm(
        'Mettre en attente',
        'Êtes-vous sûr de vouloir mettre cette vente en attente ?',
        'Oui, mettre en attente'
    ).then((result) => {
        if (result.isConfirmed) {
            updateVenteStatus(id, 'EN ATTENTE', 'Vente mise en attente avec succès');
        }
    });
}

function invoiceVente(id) {
    showConfirm(
        'Facturer la vente',
        'Êtes-vous sûr de vouloir facturer cette vente ?',
        'Oui, facturer'
    ).then((result) => {
        if (result.isConfirmed) {
            updateVenteStatus(id, 'FACTURE', 'Vente facturée avec succès');
        }
    });
}

function cancelVente(id) {
    showConfirm(
        'Annuler la vente',
        'Êtes-vous sûr de vouloir annuler cette vente ? Cette action est irréversible.',
        'Oui, annuler'
    ).then((result) => {
        if (result.isConfirmed) {
            updateVenteStatus(id, 'ANNULE', 'Vente annulée avec succès');
        }
    });
}

function updateVenteStatus(id, newStatus, successMessage) {
    // Utiliser l'action 'update' comme dans le système d'achats
    $.post('../includes/traitement.php', {
        action: 'update',
        table: 'ventes_entete',
        id: id,
        data: JSON.stringify({
            statut: newStatus,
            dernier_modif_par: 'system'
        })
    }).done(function(response) {
        if (response.success) {
            showAlert('Succès', successMessage, 'success');
            loadVentes();
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de mettre à jour le statut', 'error');
    });
}

// ===== GESTION DES PAIEMENTS SUPPRIMÉE =====
// Utiliser la page gestion_paiements_ventes.php pour les paiements

// ===== FILTRES =====

function applyFilters() {
    const status = $('#filterStatus').val();
    const dateFrom = $('#filterDateFrom').val();
    const dateTo = $('#filterDateTo').val();
    const clientId = $('#filterClient').val();

    let sql = `SELECT
                ve.id,
                ve.facture_numero as reference_vente,
                c.nom as client_nom,
                ve.date_vente,
                ve.facture_date,
                ve.statut,
                ve.total_montant,
                ve.valeur_euro,
                ve.valeur_ar
              FROM ventes_entete ve
              LEFT JOIN clients c ON ve.client_id = c.id
              WHERE 1=1`;

    const params = [];

    if (status) {
        sql += ` AND ve.statut = ?`;
        params.push(status);
    }

    if (dateFrom) {
        sql += ` AND ve.date_vente >= ?`;
        params.push(dateFrom);
    }

    if (dateTo) {
        sql += ` AND ve.date_vente <= ?`;
        params.push(dateTo);
    }

    if (clientId) {
        sql += ` AND ve.client_id = ?`;
        params.push(clientId);
    }

    sql += ` ORDER BY ve.date_creation DESC`;

    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_entete',
        sql: sql,
        params: JSON.stringify(params)
    }).done(function(response) {
        if (response.success) {
            displayVentesFiltered(response.data);
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible d\'appliquer les filtres', 'error');
    });
}

function resetFilters() {
    $('#filterStatus').val('');
    $('#filterDateFrom').val('');
    $('#filterDateTo').val('');
    $('#filterClient').val('').trigger('change');
    loadVentes();
}

// ===== AUTRES FONCTIONS =====

function viewVente(id) {
    // Charger les données de la vente avec TOUS les détails
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'ventes_entete',
        sql: `SELECT
                ve.*,
                c.nom as client_nom,
                c.adresse as client_adresse,
                c.telephone as client_telephone,
                c.email as client_email,
                c.type_client
              FROM ventes_entete ve
              LEFT JOIN clients c ON ve.client_id = c.id
              WHERE ve.id = ?`,
        params: JSON.stringify([id])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const vente = response.data[0];

            // Charger les détails de la vente avec TOUTES les colonnes
            $.post('../includes/traitement.php', {
                action: 'execute_sql',
                table: 'ventes_details',
                sql: `SELECT
                        vd.*,
                        p.nom as produit_nom,
                        d.libelle as depot_nom
                      FROM ventes_details vd
                      LEFT JOIN produits p ON vd.produit_id = p.id
                      LEFT JOIN depot d ON vd.depot_id = d.id
                      WHERE vd.vente_id = ?
                      ORDER BY vd.id`,
                params: JSON.stringify([id])
            }).done(function(detailsResponse) {
                if (detailsResponse.success) {
                    displayVenteDetails(vente, detailsResponse.data);
                } else {
                    showAlert('Erreur', 'Impossible de charger les détails', 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Impossible de charger les détails', 'error');
            });
        } else {
            showAlert('Erreur', 'Vente non trouvée', 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de charger la vente', 'error');
    });
}

function displayVenteDetails(vente, details) {
    const statutBadge = getStatutBadge(vente.statut);
    const montantTotal = details.reduce((sum, detail) => sum + parseFloat(detail.qte_tonnes || 0) * parseFloat(detail.prix_unitaire || 0), 0);

    let content = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Informations Générales</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Référence Facture:</strong></td><td>${vente.facture_numero || 'N/A'}</td></tr>
                            <tr><td><strong>Client:</strong></td><td>${vente.client_nom || 'N/A'}</td></tr>
                            <tr><td><strong>Type Client:</strong></td><td>${vente.type_client || 'N/A'}</td></tr>
                            <tr><td><strong>Date Vente:</strong></td><td>${vente.date_vente || 'N/A'}</td></tr>
                            <tr><td><strong>Date Facture:</strong></td><td>${vente.facture_date || 'N/A'}</td></tr>
                            <tr><td><strong>Statut:</strong></td><td>${statutBadge}</td></tr>
                            <tr><td><strong>N° Domiciliation:</strong></td><td>${vente.n_domiciliation || 'N/A'}</td></tr>
                            <tr><td><strong>DAU N°:</strong></td><td>${vente.dau_numero || 'N/A'}</td></tr>
                            <tr><td><strong>DAU Date:</strong></td><td>${vente.dau_date || 'N/A'}</td></tr>
                            <tr><td><strong>Lieu Exportation:</strong></td><td>${vente.lieux_exportation || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Informations Financières</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Total Montant:</strong></td><td>${parseFloat(vente.total_montant || 0).toLocaleString()} Ar</td></tr>
                            <tr><td><strong>Total Remise:</strong></td><td>${parseFloat(vente.total_remise || 0).toLocaleString()} Ar</td></tr>
                            <tr><td><strong>Valeur EUR:</strong></td><td>${parseFloat(vente.valeur_euro || 0).toFixed(2)} €</td></tr>
                            <tr><td><strong>Valeur AR:</strong></td><td>${parseFloat(vente.valeur_ar || 0).toLocaleString()} Ar</td></tr>
                            <tr><td><strong>Cours Devise:</strong></td><td>${parseFloat(vente.cours_devise || 0).toFixed(4)}</td></tr>
                            <tr><td><strong>Créé le:</strong></td><td>${vente.date_creation || 'N/A'}</td></tr>
                            <tr><td><strong>Créé par:</strong></td><td>${vente.cree_par || 'N/A'}</td></tr>
                            <tr><td><strong>Dernière modif:</strong></td><td>${vente.date_derniere_modif || 'N/A'}</td></tr>
                            <tr><td><strong>Modifié par:</strong></td><td>${vente.dernier_modif_par || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Informations Client</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Nom:</strong></td><td>${vente.client_nom || 'N/A'}</td></tr>
                            <tr><td><strong>Adresse:</strong></td><td>${vente.client_adresse || 'N/A'}</td></tr>
                            <tr><td><strong>Téléphone:</strong></td><td>${vente.client_telephone || 'N/A'}</td></tr>
                            <tr><td><strong>Email:</strong></td><td>${vente.client_email || 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Résumé</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr><td><strong>Nombre de produits:</strong></td><td>${details.length}</td></tr>
                            <tr><td><strong>Quantité totale:</strong></td><td>${details.reduce((sum, d) => sum + parseFloat(d.qte_tonnes || 0), 0).toFixed(3)} T</td></tr>
                            <tr><td><strong>Nombre de lots:</strong></td><td>${details.reduce((sum, d) => sum + parseInt(d.nbre_lot || 0), 0)}</td></tr>
                        </table>

                        ${vente.statut === 'FACTURE' ? `
                        <div class="mt-3">
                            <button class="btn btn-success btn-sm" onclick="window.open('gestion_paiements_ventes.php', '_blank')" title="Aller à la gestion des paiements">
                                <i class="fas fa-credit-card"></i> Gérer les Paiements
                            </button>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Détails des Produits</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>Produit</th>
                                        <th>Dépôt</th>
                                        <th>Grade</th>
                                        <th>Qualité</th>
                                        <th>Qté (T)</th>
                                        <th>Stock Dernier</th>
                                        <th>Nb Lots</th>
                                        <th>BL</th>
                                        <th>Conteneur</th>
                                        <th>Seal</th>
                                        <th>Lots</th>
                                        <th>Expédition</th>
                                    </tr>
                                </thead>
                                <tbody>
    `;

    details.forEach(detail => {
        content += `
            <tr>
                <td>${detail.produit_nom || 'N/A'}</td>
                <td>${detail.depot_nom || 'N/A'}</td>
                <td>${detail.grade || 'N/A'}</td>
                <td>${detail.qualite || 'N/A'}</td>
                <td>${parseFloat(detail.qte_tonnes || 0).toFixed(3)}</td>
                <td>${parseFloat(detail.qte_dernier_stock || 0).toFixed(3)}</td>
                <td>${detail.nbre_lot || 0}</td>
                <td>${detail.bl || 'N/A'}</td>
                <td>${detail.conteneur || 'N/A'}</td>
                <td>${detail.seal || 'N/A'}</td>
                <td>${detail.lots || 'N/A'}</td>
                <td>${detail.expedition || 'N/A'}</td>
            </tr>
        `;
    });

    content += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    `;

    $('#detailsContent').html(content);
    $('#modalDetailsVente').modal('show');
}

function deleteVente(id) {
    showConfirm(
        'Supprimer la vente',
        'Êtes-vous sûr de vouloir supprimer cette vente ? Cette action est irréversible.',
        'Oui, supprimer'
    ).then((result) => {
        if (result.isConfirmed) {
            // Utiliser l'action 'delete' pour supprimer l'en-tête (les détails seront supprimés par CASCADE)
            $.post('../includes/traitement.php', {
                action: 'delete',
                table: 'ventes_entete',
                id: id
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', 'Vente supprimée avec succès', 'success');
                    loadVentes();
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Impossible de supprimer la vente', 'error');
            });
        }
    });
}

function cancelVentes(ids) {
    showConfirm(
        'Annuler les ventes',
        `Êtes-vous sûr de vouloir annuler ${ids.length} vente(s) sélectionnée(s) ?`,
        'Oui, annuler'
    ).then((result) => {
        if (result.isConfirmed) {
            // Mettre à jour chaque vente individuellement comme dans le système d'achats
            let completed = 0;
            ids.forEach(id => {
                $.post('../includes/traitement.php', {
                    action: 'update',
                    table: 'ventes_entete',
                    id: id,
                    data: JSON.stringify({
                        statut: 'ANNULE',
                        dernier_modif_par: 'system'
                    })
                }).done(function(response) {
                    completed++;
                    if (completed === ids.length) {
                        showAlert('Succès', `${ids.length} vente(s) annulée(s) avec succès`, 'success');
                        loadVentes();
                        // Décocher toutes les cases
                        $('#tableVentes tbody input[type="checkbox"]').prop('checked', false);
                        $('#selectAll').prop('checked', false);
                    }
                }).fail(function() {
                    completed++;
                    if (completed === ids.length) {
                        showAlert('Erreur', 'Erreur lors de l\'annulation', 'error');
                    }
                });
            });
        }
    });
}

function sendEmailVente(id) {
    showAlert('Information', 'Fonctionnalité d\'envoi par email en cours de développement', 'info');
}

function printVente(id) {
    // Ouvrir une nouvelle fenêtre pour l'impression
    const printUrl = `../reports/print_vente.php?id=${id}`;
    window.open(printUrl, '_blank', 'width=800,height=600');
}

// ===== GESTION DU CHECKBOX "TOUT SÉLECTIONNER" =====

$(document).on('change', '#selectAll', function() {
    const isChecked = $(this).is(':checked');
    $('#tableVentes tbody input[type="checkbox"]').prop('checked', isChecked);
});

$(document).on('change', '#tableVentes tbody input[type="checkbox"]', function() {
    const totalCheckboxes = $('#tableVentes tbody input[type="checkbox"]').length;
    const checkedCheckboxes = $('#tableVentes tbody input[type="checkbox"]:checked').length;

    $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
});

// ===== INITIALISATION AU CHARGEMENT =====

$(document).ready(function() {
    // Toutes les initialisations sont déjà dans la fonction principale
    console.log('Gestion des ventes initialisée');
});

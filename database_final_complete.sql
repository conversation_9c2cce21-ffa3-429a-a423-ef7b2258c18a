-- ===================================================================
-- BASE DE DONNÉES COMPLÈTE ERP CACAO - VERSION FINALE
-- ===================================================================
-- Ce fichier contient la structure complète de la base de données
-- avec toutes les tables, vues, triggers et données nécessaires
-- pour le système ERP de gestion du cacao.
-- 
-- Date de création: 2025-10-06
-- Version: 1.0 Final
-- ===================================================================

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- ===================================================================
-- SUPPRESSION DES TABLES EXISTANTES (si nécessaire)
-- ===================================================================

DROP TABLE IF EXISTS `mouvements_stock`;
DROP TABLE IF EXISTS `operation_caisse`;
DROP TABLE IF EXISTS `achat_detail`;
DROP TABLE IF EXISTS `achat_entete`;
DROP TABLE IF EXISTS `produits_stock`;
DROP TABLE IF EXISTS `user_permissions`;
DROP TABLE IF EXISTS `role_permissions`;
DROP TABLE IF EXISTS `users`;
DROP TABLE IF EXISTS `roles`;
DROP TABLE IF EXISTS `permissions`;
DROP TABLE IF EXISTS `ventes_details`;
DROP TABLE IF EXISTS `ventes_entete`;
DROP TABLE IF EXISTS `production_mensuelle`;
DROP TABLE IF EXISTS `production_globale`;
DROP TABLE IF EXISTS `producteurs`;
DROP TABLE IF EXISTS `fournisseurs`;
DROP TABLE IF EXISTS `cooperatives`;
DROP TABLE IF EXISTS `communes`;
DROP TABLE IF EXISTS `clients`;
DROP TABLE IF EXISTS `produits`;
DROP TABLE IF EXISTS `presentation`;
DROP TABLE IF EXISTS `forme`;
DROP TABLE IF EXISTS `depot`;
DROP TABLE IF EXISTS `categorie`;
DROP TABLE IF EXISTS `unites`;

-- ===================================================================
-- CRÉATION DES TABLES DE BASE
-- ===================================================================

-- Table unites
CREATE TABLE `unites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `libelle` varchar(100) NOT NULL,
  `est_actif` tinyint(1) NOT NULL DEFAULT 1,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `libelle` (`libelle`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table depot
CREATE TABLE `depot` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `libelle` varchar(255) NOT NULL,
  `est_actif` tinyint(1) NOT NULL DEFAULT 1,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `libelle` (`libelle`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table forme
CREATE TABLE `forme` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `libelle` varchar(100) NOT NULL,
  `famille_forme` varchar(255) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `libelle` (`libelle`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table presentation
CREATE TABLE `presentation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `libelle` varchar(100) NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `libelle` (`libelle`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table categorie
CREATE TABLE `categorie` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `libelle` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `classement` enum('PRODUITS','FOURNITURES') NOT NULL DEFAULT 'PRODUITS',
  `est_actif` tinyint(1) NOT NULL DEFAULT 1,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `libelle` (`libelle`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table communes
CREATE TABLE `communes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) NOT NULL,
  `code_postal` varchar(10) DEFAULT NULL,
  `region` varchar(255) DEFAULT 'DIANA',
  `pays` varchar(100) DEFAULT 'Madagascar',
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_nom_commune` (`nom`),
  KEY `idx_region` (`region`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table cooperatives
CREATE TABLE `cooperatives` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) NOT NULL,
  `adresse` text DEFAULT NULL,
  `telephone` varchar(30) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nom` (`nom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table producteurs
CREATE TABLE `producteurs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code_producteur` varchar(50) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `code_leads` varchar(50) DEFAULT NULL,
  `genre` enum('M','F','A') DEFAULT NULL,
  `cin` varchar(50) DEFAULT NULL,
  `contact` varchar(255) DEFAULT NULL,
  `adresse` text DEFAULT NULL,
  `telephone` varchar(30) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `nif` varchar(100) DEFAULT NULL,
  `stat` varchar(100) DEFAULT NULL,
  `date_naissance` date DEFAULT NULL,
  `annee_adhesion_coop` year(4) DEFAULT NULL,
  `cotisation_droit_adhesion` decimal(12,2) DEFAULT NULL,
  `site` varchar(255) DEFAULT NULL,
  `classement` enum('LEAD FARMER','PETIT PLANTEUR','FREELANCE') NOT NULL DEFAULT 'PETIT PLANTEUR',
  `situation` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code_producteur` (`code_producteur`),
  UNIQUE KEY `cin` (`cin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table fournisseurs
CREATE TABLE `fournisseurs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) NOT NULL,
  `contact` varchar(30) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `adresse` text DEFAULT NULL,
  `nif` varchar(50) DEFAULT NULL,
  `stat` varchar(50) DEFAULT NULL,
  `est_actif` tinyint(1) NOT NULL DEFAULT 1,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `nif` (`nif`),
  UNIQUE KEY `stat` (`stat`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table clients
CREATE TABLE `clients` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) NOT NULL,
  `adresse` text DEFAULT NULL,
  `telephone` varchar(30) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `nif` varchar(100) DEFAULT NULL,
  `stat` varchar(100) DEFAULT NULL,
  `type_client` enum('Export','Local') NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `nif` (`nif`),
  UNIQUE KEY `stat` (`stat`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table produits
CREATE TABLE `produits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) NOT NULL,
  `type` enum('Standard','Superieur') NOT NULL,
  `certification` enum('Bio','Conventionnel') NOT NULL,
  `categorie_id` int(11) NOT NULL,
  `classe` enum('PRODUITS','FOURNITURES') NOT NULL,
  `unite_vente_id` int(11) NOT NULL,
  `unite_achat_id` int(11) NOT NULL,
  `unite_stock_id` int(11) NOT NULL,
  `presentation_id` int(11) DEFAULT NULL,
  `forme_id` int(11) DEFAULT NULL,
  `qte_presentation` int(11) DEFAULT 1,
  `qte_forme` int(11) DEFAULT 67,
  `prix_vente` decimal(12,2) NOT NULL,
  `marge_beneficiaire_pct` decimal(5,2) NOT NULL DEFAULT 0.00,
  `prix_achat` decimal(12,2) NOT NULL DEFAULT 0.00,
  `stock_min` int(11) NOT NULL DEFAULT 0,
  `stock_max` int(11) NOT NULL DEFAULT 0,
  `image_url` text DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_nom_produit` (`nom`),
  KEY `fk_produits_categorie` (`categorie_id`),
  KEY `fk_produits_unite_vente` (`unite_vente_id`),
  KEY `fk_produits_unite_achat` (`unite_achat_id`),
  KEY `fk_produits_unite_stock` (`unite_stock_id`),
  KEY `fk_produits_presentation` (`presentation_id`),
  KEY `fk_produits_forme` (`forme_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table achat_entete
CREATE TABLE `achat_entete` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reference_achat` varchar(50) NOT NULL,
  `fournisseur_id` int(11) NOT NULL,
  `date_achat` date DEFAULT NULL,
  `date_livraison` date DEFAULT NULL,
  `statut` enum('SAISIE','LIVRE','CONTROLE','A_PAYER','PAYE','ANNULE') NOT NULL DEFAULT 'SAISIE',
  `frais_transport` decimal(12,2) DEFAULT 0.00,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `reference_achat` (`reference_achat`),
  KEY `idx_fournisseur` (`fournisseur_id`),
  KEY `idx_statut` (`statut`),
  KEY `idx_date_achat` (`date_achat`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table achat_detail
CREATE TABLE `achat_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `achat_entete_id` int(11) NOT NULL,
  `produit_id` int(11) NOT NULL,
  `depot_id` int(11) DEFAULT NULL,
  `petit_planteur_id` int(11) DEFAULT NULL,
  `unite_achat_id` int(11) NOT NULL,
  `qte_brute_saisie` decimal(10,2) DEFAULT 0.00,
  `qte_nette_controlee` decimal(10,2) DEFAULT 0.00,
  `prix_unitaire_net` decimal(12,2) DEFAULT 0.00,
  `montant_ht` decimal(12,2) DEFAULT 0.00,
  `reduction` decimal(12,2) DEFAULT 0.00,
  `stock_avant_entree` decimal(10,2) DEFAULT 0.00,
  `lot_numero` varchar(100) DEFAULT NULL,
  `nombre_sacs` decimal(10,2) DEFAULT 0.00,
  `ecart_controle` decimal(10,2) DEFAULT 0.00,
  `producteurs_json` text DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_achat_entete` (`achat_entete_id`),
  KEY `idx_produit` (`produit_id`),
  KEY `idx_depot` (`depot_id`),
  KEY `idx_planteur` (`petit_planteur_id`),
  KEY `idx_unite` (`unite_achat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table produits_stock
CREATE TABLE `produits_stock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `produit_id` int(11) NOT NULL,
  `depot_id` int(11) NOT NULL,
  `quantite` decimal(10,2) NOT NULL DEFAULT 0.00,
  `unite_stock_id` int(11) NOT NULL,
  `lot_numero` varchar(100) DEFAULT NULL,
  `date_entree` datetime DEFAULT current_timestamp(),
  `date_expiration` date DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_produit_depot` (`produit_id`, `depot_id`),
  KEY `idx_lot` (`lot_numero`),
  KEY `idx_date_entree` (`date_entree`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table mouvements_stock
CREATE TABLE `mouvements_stock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `produit_id` int(11) NOT NULL,
  `depot_id` int(11) NOT NULL,
  `type_mouvement` enum('ENTREE','SORTIE','AJUSTEMENT','TRANSFERT') NOT NULL,
  `quantite` decimal(10,2) NOT NULL,
  `unite_id` int(11) NOT NULL,
  `reference_document` varchar(100) DEFAULT NULL,
  `lot_numero` varchar(100) DEFAULT NULL,
  `motif` text DEFAULT NULL,
  `date_mouvement` datetime NOT NULL DEFAULT current_timestamp(),
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_produit_depot` (`produit_id`, `depot_id`),
  KEY `idx_type_mouvement` (`type_mouvement`),
  KEY `idx_date_mouvement` (`date_mouvement`),
  KEY `idx_reference` (`reference_document`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table operation_caisse
CREATE TABLE `operation_caisse` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `achat_id` int(11) DEFAULT NULL,
  `fournisseur_id` int(11) DEFAULT NULL,
  `type_operation` enum('PAIEMENT_ACHAT','AVANCE','REMBOURSEMENT','AUTRE') NOT NULL,
  `mode_paiement` enum('CHEQUE','VIREMENT','ESPECE') NOT NULL,
  `reference_paiement` varchar(100) DEFAULT NULL,
  `montant` decimal(18,2) NOT NULL,
  `date_paiement` date NOT NULL,
  `effectue_par` varchar(255) DEFAULT NULL,
  `commentaires` text DEFAULT NULL,
  `statut` enum('EN_ATTENTE','VALIDE','ANNULE') NOT NULL DEFAULT 'EN_ATTENTE',
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_operation_caisse_achat` (`achat_id`),
  KEY `idx_operation_caisse_fournisseur` (`fournisseur_id`),
  KEY `idx_operation_caisse_date` (`date_paiement`),
  KEY `idx_operation_caisse_type` (`type_operation`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table production_globale
CREATE TABLE `production_globale` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `producteur_id` int(11) NOT NULL,
  `annee` year(4) NOT NULL,
  `nombre_pieds` int(11) DEFAULT NULL,
  `surface_totale_production_ha` decimal(10,2) DEFAULT NULL,
  `volume_production_estimee_t` decimal(10,2) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_prod_unique_annuelle` (`producteur_id`,`annee`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table production_mensuelle
CREATE TABLE `production_mensuelle` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `producteur_id` int(11) NOT NULL,
  `annee` year(4) NOT NULL,
  `mois` enum('JAN','FEV','MAR','AVR','MAI','JUIN','JUIL','AOUT','SEPT','OCT','NOV','DEC') NOT NULL,
  `type_production` enum('SEC','FRAICHE') NOT NULL,
  `volume_produit_kg` decimal(10,2) NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_prod_unique_mensuelle` (`producteur_id`,`annee`,`mois`,`type_production`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table ventes_entete
CREATE TABLE `ventes_entete` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `client_id` int(11) NOT NULL,
  `n_domiciliation` varchar(255) DEFAULT NULL,
  `total_montant` decimal(18,2) DEFAULT NULL,
  `total_remise` decimal(18,2) DEFAULT NULL,
  `date_vente` date DEFAULT NULL,
  `statut` enum('Annuler','Encours','Facturer','En attente') NOT NULL DEFAULT 'Encours',
  `valeur_euro` decimal(18,2) DEFAULT NULL,
  `valeur_ar` decimal(18,2) DEFAULT NULL,
  `cours_devise` decimal(10,4) DEFAULT NULL,
  `dau_numero` varchar(100) DEFAULT NULL,
  `dau_date` date DEFAULT NULL,
  `facture_numero` varchar(100) DEFAULT NULL,
  `facture_date` date DEFAULT NULL,
  `lieux_exportation` varchar(255) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_client` (`client_id`),
  KEY `idx_date_vente` (`date_vente`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table ventes_details
CREATE TABLE `ventes_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `vente_id` int(11) NOT NULL,
  `produit_id` int(11) DEFAULT NULL,
  `depot_id` int(11) DEFAULT NULL,
  `expedition` varchar(100) DEFAULT NULL,
  `grade` varchar(50) DEFAULT NULL,
  `qualite` varchar(100) DEFAULT NULL,
  `qte_tonnes` decimal(10,2) DEFAULT NULL,
  `qte_dernier_stock` decimal(10,2) DEFAULT NULL,
  `nbre_lot` int(11) DEFAULT NULL,
  `bl` varchar(100) DEFAULT NULL,
  `conteneur` varchar(100) DEFAULT NULL,
  `seal` varchar(100) DEFAULT NULL,
  `lots` varchar(255) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_details_vente` (`vente_id`),
  KEY `fk_details_produit` (`produit_id`),
  KEY `fk_details_depot` (`depot_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table roles
CREATE TABLE `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table permissions
CREATE TABLE `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `category` varchar(50) NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table role_permissions
CREATE TABLE `role_permissions` (
  `role_id` int(11) NOT NULL,
  `permission` varchar(191) NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`role_id`,`permission`),
  KEY `permission` (`permission`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- Table users
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role_id` int(11) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table user_permissions
CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted` tinyint(1) DEFAULT 1,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_permission` (`user_id`,`permission_id`),
  KEY `permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================================================
-- CONTRAINTES DE CLÉS ÉTRANGÈRES
-- ===================================================================

-- Contraintes pour la table produits
ALTER TABLE `produits`
  ADD CONSTRAINT `fk_produits_categorie` FOREIGN KEY (`categorie_id`) REFERENCES `categorie` (`id`),
  ADD CONSTRAINT `fk_produits_forme` FOREIGN KEY (`forme_id`) REFERENCES `forme` (`id`),
  ADD CONSTRAINT `fk_produits_presentation` FOREIGN KEY (`presentation_id`) REFERENCES `presentation` (`id`),
  ADD CONSTRAINT `fk_produits_unite_achat` FOREIGN KEY (`unite_achat_id`) REFERENCES `unites` (`id`),
  ADD CONSTRAINT `fk_produits_unite_stock` FOREIGN KEY (`unite_stock_id`) REFERENCES `unites` (`id`),
  ADD CONSTRAINT `fk_produits_unite_vente` FOREIGN KEY (`unite_vente_id`) REFERENCES `unites` (`id`);

-- Contraintes pour la table achat_entete
ALTER TABLE `achat_entete`
  ADD CONSTRAINT `fk_achat_fournisseur` FOREIGN KEY (`fournisseur_id`) REFERENCES `producteurs` (`id`) ON DELETE RESTRICT;

-- Contraintes pour la table achat_detail
ALTER TABLE `achat_detail`
  ADD CONSTRAINT `fk_detail_achat_entete` FOREIGN KEY (`achat_entete_id`) REFERENCES `achat_entete` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_detail_produit` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE RESTRICT,
  ADD CONSTRAINT `fk_detail_depot` FOREIGN KEY (`depot_id`) REFERENCES `depot` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_detail_planteur` FOREIGN KEY (`petit_planteur_id`) REFERENCES `producteurs` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_detail_unite` FOREIGN KEY (`unite_achat_id`) REFERENCES `unites` (`id`) ON DELETE RESTRICT;

-- Contraintes pour la table produits_stock
ALTER TABLE `produits_stock`
  ADD CONSTRAINT `fk_stock_produit` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_stock_depot` FOREIGN KEY (`depot_id`) REFERENCES `depot` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_stock_unite` FOREIGN KEY (`unite_stock_id`) REFERENCES `unites` (`id`) ON DELETE RESTRICT;

-- Contraintes pour la table mouvements_stock
ALTER TABLE `mouvements_stock`
  ADD CONSTRAINT `fk_mouvement_produit` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_mouvement_depot` FOREIGN KEY (`depot_id`) REFERENCES `depot` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_mouvement_unite` FOREIGN KEY (`unite_id`) REFERENCES `unites` (`id`) ON DELETE RESTRICT;

-- Contraintes pour la table operation_caisse
ALTER TABLE `operation_caisse`
  ADD CONSTRAINT `fk_operation_achat` FOREIGN KEY (`achat_id`) REFERENCES `achat_entete` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_operation_fournisseur` FOREIGN KEY (`fournisseur_id`) REFERENCES `producteurs` (`id`) ON DELETE SET NULL;

-- Contraintes pour la table production_globale
ALTER TABLE `production_globale`
  ADD CONSTRAINT `fk_prod_globale_producteur` FOREIGN KEY (`producteur_id`) REFERENCES `producteurs` (`id`) ON DELETE CASCADE;

-- Contraintes pour la table production_mensuelle
ALTER TABLE `production_mensuelle`
  ADD CONSTRAINT `fk_prod_mensuelle_producteur` FOREIGN KEY (`producteur_id`) REFERENCES `producteurs` (`id`) ON DELETE CASCADE;

-- Contraintes pour la table ventes_entete
ALTER TABLE `ventes_entete`
  ADD CONSTRAINT `fk_ventes_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`);

-- Contraintes pour la table ventes_details
ALTER TABLE `ventes_details`
  ADD CONSTRAINT `fk_details_vente` FOREIGN KEY (`vente_id`) REFERENCES `ventes_entete` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_details_produit` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_details_depot` FOREIGN KEY (`depot_id`) REFERENCES `depot` (`id`) ON DELETE SET NULL;

-- Contraintes pour la table users
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`);

-- Contraintes pour la table role_permissions
ALTER TABLE `role_permissions`
  ADD CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

-- Contraintes pour la table user_permissions
ALTER TABLE `user_permissions`
  ADD CONSTRAINT `user_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

-- ===================================================================
-- INSERTION DES DONNÉES DE BASE
-- ===================================================================

-- Données pour la table unites
INSERT INTO `unites` (`id`, `libelle`, `est_actif`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'POUDRE', 1, NOW(), 'system', NOW(), 'system'),
(2, 'SAC', 1, NOW(), 'system', NOW(), 'system'),
(3, 'KG', 1, NOW(), 'system', NOW(), 'system'),
(6, 'TONNE', 1, NOW(), 'system', NOW(), 'system'),
(7, 'BOITE', 1, NOW(), 'system', NOW(), 'system'),
(8, 'UNITE', 1, NOW(), 'system', NOW(), 'system');

-- Données pour la table depot
INSERT INTO `depot` (`id`, `libelle`, `est_actif`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'MAGASIN N°01', 1, NOW(), 'system', NOW(), 'system'),
(2, 'MAGASIN N°02', 1, NOW(), 'system', NOW(), 'system'),
(3, 'MAGASIN N°03', 1, NOW(), 'system', NOW(), 'system');

-- Données pour la table forme
INSERT INTO `forme` (`id`, `libelle`, `famille_forme`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(79, 'KG', 'Poids', NOW(), 'system', NOW(), 'system');

-- Données pour la table presentation
INSERT INTO `presentation` (`id`, `libelle`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(18, 'SAC', NOW(), 'system', NOW(), 'system');

-- Données pour la table categorie
INSERT INTO `categorie` (`id`, `libelle`, `description`, `classement`, `est_actif`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(2, 'MATIERE PREMIERE', NULL, 'PRODUITS', 1, NOW(), 'system', NOW(), 'system'),
(3, 'BIO', NULL, 'PRODUITS', 1, NOW(), 'system', NOW(), 'system'),
(4, 'CONVENTIONEL', NULL, 'PRODUITS', 1, NOW(), 'system', NOW(), 'system'),
(5, 'BIO - FT', NULL, 'PRODUITS', 1, NOW(), 'system', NOW(), 'system'),
(6, 'CONSOMABLE', NULL, 'FOURNITURES', 1, NOW(), 'system', NOW(), 'system');

-- Données pour la table communes
INSERT INTO `communes` (`id`, `nom`, `code_postal`, `region`, `pays`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'ANTSAKOAMANONDRO', '206', 'DIANA', 'Madagascar', NOW(), 'system', NOW(), 'system'),
(2, 'ANKATAFA VAOVAO', '206', 'DIANA', 'Madagascar', NOW(), 'system', NOW(), 'system'),
(3, 'AMBODIVANIO', '206', 'DIANA', 'Madagascar', NOW(), 'system', NOW(), 'system');

-- Données pour la table cooperatives
INSERT INTO `cooperatives` (`id`, `nom`, `adresse`, `telephone`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'Coopérative Sambirano Mateza', '20', NULL, NOW(), 'system', NOW(), 'system'),
(2, 'Coopérative Bio Sambirano', '20', NULL, NOW(), 'system', NOW(), 'system'),
(3, 'Coopérative Cacao et Vanille Sambirano', '20', NULL, NOW(), 'system', NOW(), 'system');

-- Données pour la table producteurs
INSERT INTO `producteurs` (`id`, `code_producteur`, `nom`, `code_leads`, `genre`, `cin`, `contact`, `adresse`, `telephone`, `email`, `nif`, `stat`, `date_naissance`, `annee_adhesion_coop`, `cotisation_droit_adhesion`, `site`, `classement`, `situation`, `image`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'BS/ATSK/001', 'ABDOUL Sylvain JOMA', 'LF 01/BS/CSM/ATSK', 'M', '719011012960', '0346740932/0325504361', 'Antsiranana, Madagascar', '0346740932', '<EMAIL>', NULL, NULL, '1978-08-06', '2016', 29.00, '1', 'LEAD FARMER', '5', NULL, NOW(), 'system', NOW(), 'system'),
(2, 'BS/ATSK/002', 'Aly Bruno', NULL, 'M', '719341004080', NULL, 'Ambanja, Madagascar', '0342563989', '<EMAIL>', NULL, NULL, NULL, '2016', 29.00, '1', 'PETIT PLANTEUR', '5', NULL, NOW(), 'system', NOW(), 'system'),
(3, 'BS/ATSK/003', 'Assany Tohalibo (1/3)', NULL, 'M', '719131002872', NULL, NULL, NULL, NULL, NULL, NULL, '1968-03-03', '2016', 29000.00, '1', 'PETIT PLANTEUR', '5', NULL, NOW(), 'system', NOW(), 'system'),
(4, 'BS/ATSK/004', 'AUGUISTIN Sabotsy', NULL, 'M', '719111000127', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2016', 29.00, '1', 'PETIT PLANTEUR', '5', NULL, NOW(), 'system', NOW(), 'system');

-- Données pour la table fournisseurs
INSERT INTO `fournisseurs` (`id`, `nom`, `contact`, `email`, `adresse`, `nif`, `stat`, `est_actif`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'HOUSSENI', '032', '<EMAIL>', 'AMBANJA', 'KI52639874526', '4152639875652', 1, NOW(), 'system', NOW(), 'system'),
(3, 'IMPORTER', '032', '<EMAIL>', 'CENTRE VILLE', 'UJ85969748585', '15975369369654', 1, NOW(), 'system', NOW(), 'system'),
(4, 'AKBAR ALY', '0342563989', '<EMAIL>', '41 Morarano', '41526398789', 'Stat4152636', 1, NOW(), 'system', NOW(), 'system');

-- Données pour la table clients
INSERT INTO `clients` (`id`, `nom`, `adresse`, `telephone`, `email`, `nif`, `stat`, `type_client`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'SAL IBRAHIM Tanambao', 'Antsiranana', '032284687524', '<EMAIL>', NULL, NULL, 'Export', NOW(), 'system', NOW(), 'system'),
(2, 'Test', 'Test', '032000', '<EMAIL>', '079736', '5373773', 'Export', NOW(), 'system', NOW(), 'system'),
(3, 'Felchlin ', NULL, NULL, NULL, NULL, NULL, 'Export', NOW(), 'system', NOW(), 'system'),
(4, 'Barry calbaut', 'Amsterdam', NULL, NULL, NULL, NULL, 'Export', NOW(), 'system', NOW(), 'system'),
(5, 'RAYA Dayeuhkolot', 'Inde', NULL, NULL, NULL, NULL, 'Export', NOW(), 'system', NOW(), 'system'),
(6, 'TRADIN', 'AMSTERDAM', NULL, NULL, NULL, NULL, 'Export', NOW(), 'system', NOW(), 'system'),
(7, 'ICAM SPA', 'Inde', NULL, NULL, NULL, NULL, 'Export', NOW(), 'system', NOW(), 'system');

-- Données pour la table produits
INSERT INTO `produits` (`id`, `nom`, `type`, `certification`, `categorie_id`, `classe`, `unite_vente_id`, `unite_achat_id`, `unite_stock_id`, `presentation_id`, `forme_id`, `qte_presentation`, `qte_forme`, `prix_vente`, `marge_beneficiaire_pct`, `prix_achat`, `stock_min`, `stock_max`, `image_url`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(5, 'CACAO BIO SUPERIEUR', 'Superieur', 'Bio', 3, 'PRODUITS', 2, 3, 2, 18, 79, 1, 67, 0.00, 0.00, 36000.00, 10000, 50000, NULL, NOW(), 'system', NOW(), 'system'),
(6, 'CACAO BIO STANDARD', 'Standard', 'Bio', 5, 'PRODUITS', 2, 3, 2, 18, 79, 1, 67, 0.00, 0.00, 40000.00, 0, 0, NULL, NOW(), 'system', NOW(), 'system'),
(7, 'CACAO CONVENTIONEL', 'Standard', 'Bio', 4, 'PRODUITS', 2, 3, 2, 18, 79, 1, 67, 0.00, 0.00, 38000.00, 0, 0, NULL, NOW(), 'system', NOW(), 'system'),
(8, 'STYLO', 'Standard', 'Bio', 6, 'FOURNITURES', 8, 7, 8, NULL, NULL, 1, 50, 1000.00, 0.00, 15000.00, 30, 500, NULL, NOW(), 'system', NOW(), 'system'),
(9, 'CAHIER', 'Standard', 'Bio', 6, 'FOURNITURES', 8, 7, 8, NULL, NULL, 1, 50, 1000.00, 0.00, 15000.00, 30, 500, NULL, NOW(), 'system', NOW(), 'system');

-- Données pour la table roles
INSERT INTO `roles` (`id`, `name`, `description`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'admin', 'Administrateur système - Accès complet', NOW(), 'system', NOW(), 'system'),
(2, 'denis', 'Denis - Gestion des achats et stocks', NOW(), 'system', NOW(), 'system'),
(3, 'joassin', 'Joassin - Validation des achats', NOW(), 'system', NOW(), 'system'),
(4, 'guy', 'Guy - Comptabilité et paiements', NOW(), 'system', NOW(), 'system'),
(5, 'manager', 'Manager', NOW(), 'system', NOW(), 'system'),
(6, 'vendeur', 'Vendeur', NOW(), 'system', NOW(), 'system'),
(7, 'caissier', 'Caissier', NOW(), 'system', NOW(), 'system'),
(8, 'production', 'Production', NOW(), 'system', NOW(), 'system'),
(9, 'utilisateur', 'Utilisateur standard', NOW(), 'system', NOW(), 'system');

-- Données pour la table permissions
INSERT INTO `permissions` (`id`, `name`, `description`, `category`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'menu.achats.view', 'Voir le menu Achats', 'Menu', NOW(), 'system', NOW(), 'system'),
(2, 'menu.stocks.view', 'Voir le menu Stocks', 'Menu', NOW(), 'system', NOW(), 'system'),
(3, 'menu.comptabilite.view', 'Voir le menu Comptabilité', 'Menu', NOW(), 'system', NOW(), 'system'),
(4, 'menu.utilisateurs.view', 'Voir le menu Utilisateurs', 'Menu', NOW(), 'system', NOW(), 'system'),
(5, 'menu.rapports.view', 'Voir le menu Rapports', 'Menu', NOW(), 'system', NOW(), 'system'),
(6, 'menu.parametres.view', 'Voir le menu Paramètres', 'Menu', NOW(), 'system', NOW(), 'system'),
(7, 'menu.lead_farmers.view', 'Voir le menu Lead Farmers', 'Menu', NOW(), 'system', NOW(), 'system'),
(8, 'menu.planteurs.view', 'Voir le menu Planteurs', 'Menu', NOW(), 'system', NOW(), 'system'),
(9, 'achats.create', 'Créer un achat', 'Achats', NOW(), 'system', NOW(), 'system'),
(10, 'achats.view', 'Voir les achats', 'Achats', NOW(), 'system', NOW(), 'system'),
(11, 'achats.update', 'Modifier un achat', 'Achats', NOW(), 'system', NOW(), 'system'),
(12, 'achats.validate', 'Valider un achat', 'Achats', NOW(), 'system', NOW(), 'system'),
(13, 'achats.pay', 'Marquer un achat comme payé', 'Achats', NOW(), 'system', NOW(), 'system'),
(14, 'achats.delete', 'Supprimer un achat', 'Achats', NOW(), 'system', NOW(), 'system'),
(15, 'achats.export', 'Exporter les achats', 'Achats', NOW(), 'system', NOW(), 'system'),
(16, 'achats.view_details', 'Voir les détails d\'un achat', 'Achats', NOW(), 'system', NOW(), 'system'),
(17, 'stocks.view', 'Voir les stocks', 'Stocks', NOW(), 'system', NOW(), 'system'),
(18, 'stocks.inventory', 'Effectuer un inventaire', 'Stocks', NOW(), 'system', NOW(), 'system'),
(19, 'stocks.adjustment', 'Faire un ajustement de stock', 'Stocks', NOW(), 'system', NOW(), 'system'),
(20, 'stocks.transfer', 'Effectuer un transfert', 'Stocks', NOW(), 'system', NOW(), 'system'),
(21, 'stocks.view_history', 'Voir l\'historique des mouvements', 'Stocks', NOW(), 'system', NOW(), 'system'),
(22, 'comptabilite.journal_caisse', 'Gérer le journal de caisse', 'Comptabilité', NOW(), 'system', NOW(), 'system'),
(23, 'comptabilite.paiement_achat', 'Gérer les paiements d\'achats', 'Comptabilité', NOW(), 'system', NOW(), 'system'),
(24, 'comptabilite.solde_initial', 'Gérer les soldes initiaux', 'Comptabilité', NOW(), 'system', NOW(), 'system'),
(25, 'comptabilite.rapports', 'Voir les rapports financiers', 'Comptabilité', NOW(), 'system', NOW(), 'system'),
(26, 'utilisateurs.view', 'Voir les utilisateurs', 'Utilisateurs', NOW(), 'system', NOW(), 'system'),
(27, 'utilisateurs.create', 'Créer un utilisateur', 'Utilisateurs', NOW(), 'system', NOW(), 'system'),
(28, 'utilisateurs.update', 'Modifier un utilisateur', 'Utilisateurs', NOW(), 'system', NOW(), 'system'),
(29, 'utilisateurs.delete', 'Supprimer un utilisateur', 'Utilisateurs', NOW(), 'system', NOW(), 'system'),
(30, 'utilisateurs.permissions', 'Gérer les permissions des utilisateurs', 'Utilisateurs', NOW(), 'system', NOW(), 'system'),
(31, 'utilisateurs.roles', 'Gérer les rôles', 'Utilisateurs', NOW(), 'system', NOW(), 'system'),
(32, 'lead_farmers.create', 'Créer un Lead Farmer', 'Lead Farmers', NOW(), 'system', NOW(), 'system'),
(33, 'lead_farmers.view', 'Voir les Lead Farmers', 'Lead Farmers', NOW(), 'system', NOW(), 'system'),
(34, 'lead_farmers.update', 'Modifier un Lead Farmer', 'Lead Farmers', NOW(), 'system', NOW(), 'system'),
(35, 'lead_farmers.delete', 'Supprimer un Lead Farmer', 'Lead Farmers', NOW(), 'system', NOW(), 'system'),
(36, 'lead_farmers.export', 'Exporter les Lead Farmers', 'Lead Farmers', NOW(), 'system', NOW(), 'system'),
(37, 'planteurs.create', 'Créer un Planteur', 'Planteurs', NOW(), 'system', NOW(), 'system'),
(38, 'planteurs.view', 'Voir les Planteurs', 'Planteurs', NOW(), 'system', NOW(), 'system'),
(39, 'planteurs.update', 'Modifier un Planteur', 'Planteurs', NOW(), 'system', NOW(), 'system'),
(40, 'planteurs.delete', 'Supprimer un Planteur', 'Planteurs', NOW(), 'system', NOW(), 'system'),
(41, 'planteurs.export', 'Exporter les Planteurs', 'Planteurs', NOW(), 'system', NOW(), 'system'),
(42, 'parametres.view', 'Voir les paramètres', 'Paramètres', NOW(), 'system', NOW(), 'system'),
(43, 'parametres.create', 'Créer des paramètres', 'Paramètres', NOW(), 'system', NOW(), 'system'),
(44, 'parametres.update', 'Modifier des paramètres', 'Paramètres', NOW(), 'system', NOW(), 'system'),
(45, 'parametres.delete', 'Supprimer des paramètres', 'Paramètres', NOW(), 'system', NOW(), 'system');

-- Données pour la table role_permissions
INSERT INTO `role_permissions` (`role_id`, `permission`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, '*', NOW(), 'system', NOW(), 'system'),
(5, 'clients.create', NOW(), 'system', NOW(), 'system'),
(5, 'clients.delete', NOW(), 'system', NOW(), 'system'),
(5, 'clients.export', NOW(), 'system', NOW(), 'system'),
(5, 'clients.update', NOW(), 'system', NOW(), 'system'),
(5, 'entree_detail.create', NOW(), 'system', NOW(), 'system'),
(5, 'entree_detail.delete', NOW(), 'system', NOW(), 'system'),
(5, 'entree_detail.update', NOW(), 'system', NOW(), 'system'),
(5, 'entree_entete.create', NOW(), 'system', NOW(), 'system'),
(5, 'entree_entete.delete', NOW(), 'system', NOW(), 'system'),
(5, 'entree_entete.pay', NOW(), 'system', NOW(), 'system'),
(5, 'entree_entete.update', NOW(), 'system', NOW(), 'system'),
(5, 'entree_entete.validate', NOW(), 'system', NOW(), 'system'),
(5, 'entree_entete.view', NOW(), 'system', NOW(), 'system'),
(5, 'menu.achats.view', NOW(), 'system', NOW(), 'system'),
(5, 'menu.clients.view', NOW(), 'system', NOW(), 'system'),
(5, 'menu.dashboard.view', NOW(), 'system', NOW(), 'system'),
(5, 'menu.produits.view', NOW(), 'system', NOW(), 'system'),
(5, 'menu.stocks.view', NOW(), 'system', NOW(), 'system'),
(5, 'mouvement_stock.create', NOW(), 'system', NOW(), 'system'),
(5, 'mouvement_stock.view', NOW(), 'system', NOW(), 'system'),
(5, 'page.achats_entete.view', NOW(), 'system', NOW(), 'system'),
(5, 'page.clients.view', NOW(), 'system', NOW(), 'system'),
(5, 'page.dashboard.view', NOW(), 'system', NOW(), 'system'),
(5, 'page.gestion_produits.view', NOW(), 'system', NOW(), 'system'),
(5, 'page.gestion_stock.view', NOW(), 'system', NOW(), 'system'),
(5, 'produit.create', NOW(), 'system', NOW(), 'system'),
(5, 'produit.delete', NOW(), 'system', NOW(), 'system'),
(5, 'produit.update', NOW(), 'system', NOW(), 'system'),
(5, 'produit.view', NOW(), 'system', NOW(), 'system'),
(5, 'stock_detail.adjustment', NOW(), 'system', NOW(), 'system'),
(5, 'stock_detail.view', NOW(), 'system', NOW(), 'system'),
(7, 'commandes.payer', NOW(), 'system', NOW(), 'system'),
(8, 'matiere.*', NOW(), 'system', NOW(), 'system'),
(8, 'production.*', NOW(), 'system', NOW(), 'system'),
(9, 'menu.dashboard.view', NOW(), 'system', NOW(), 'system'),
(9, 'page.dashboard.view', NOW(), 'system', NOW(), 'system');

-- Données pour la table users
INSERT INTO `users` (`id`, `username`, `email`, `full_name`, `password`, `role_id`, `phone`, `status`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', 'Administrateur', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 1, NULL, 1, NULL, NOW(), NOW()),
(2, 'denis', '<EMAIL>', 'Denis', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 2, NULL, 1, NULL, NOW(), NOW()),
(3, 'joassin', '<EMAIL>', 'Joassin', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 3, NULL, 1, NULL, NOW(), NOW()),
(4, 'guy', '<EMAIL>', 'Guy', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 4, NULL, 1, NULL, NOW(), NOW());

-- Données pour la table production_globale
INSERT INTO `production_globale` (`id`, `producteur_id`, `annee`, `nombre_pieds`, `surface_totale_production_ha`, `volume_production_estimee_t`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 1, '2025', 550, 0.88, 1.00, NOW(), 'system', NOW(), 'system'),
(2, 2, '2025', 500, 0.80, 1.00, NOW(), 'system', NOW(), 'system');

-- ===================================================================
-- VUES POUR LE SYSTÈME
-- ===================================================================

-- Vue pour le stock actuel par produit
CREATE OR REPLACE VIEW `v_stock_actuel` AS
SELECT 
    p.id as produit_id,
    p.nom as produit_nom,
    d.id as depot_id,
    d.libelle as depot_nom,
    u.libelle as unite_stock,
    COALESCE(SUM(
        CASE 
            WHEN m.type_mouvement = 'ENTREE' THEN m.quantite
            WHEN m.type_mouvement = 'SORTIE' THEN -m.quantite
            ELSE 0
        END
    ), 0) as stock_actuel,
    p.stock_min,
    p.stock_max,
    CASE 
        WHEN COALESCE(SUM(
            CASE 
                WHEN m.type_mouvement = 'ENTREE' THEN m.quantite
                WHEN m.type_mouvement = 'SORTIE' THEN -m.quantite
                ELSE 0
            END
        ), 0) <= p.stock_min THEN 'STOCK_FAIBLE'
        WHEN COALESCE(SUM(
            CASE 
                WHEN m.type_mouvement = 'ENTREE' THEN m.quantite
                WHEN m.type_mouvement = 'SORTIE' THEN -m.quantite
                ELSE 0
            END
        ), 0) >= p.stock_max THEN 'STOCK_ELEVE'
        ELSE 'STOCK_NORMAL'
    END as statut_stock
FROM produits p
CROSS JOIN depot d
LEFT JOIN mouvements_stock m ON p.id = m.produit_id AND d.id = m.depot_id
LEFT JOIN unites u ON p.unite_stock_id = u.id
WHERE d.est_actif = 1
GROUP BY p.id, d.id, p.nom, d.libelle, u.libelle, p.stock_min, p.stock_max;

-- Vue pour l'historique des mouvements
CREATE OR REPLACE VIEW `v_historique_mouvements` AS
SELECT 
    m.id,
    m.date_mouvement,
    p.nom as produit_nom,
    d.libelle as depot_nom,
    m.type_mouvement,
    m.quantite,
    u.libelle as unite,
    m.lot_numero,
    m.cree_par
FROM mouvements_stock m
JOIN produits p ON m.produit_id = p.id
JOIN depot d ON m.depot_id = d.id
JOIN unites u ON m.unite_id = u.id
ORDER BY m.date_mouvement DESC;

-- Vue pour les paiements détaillés
CREATE OR REPLACE VIEW `v_paiements_detaille` AS
SELECT 
    oc.id,
    oc.achat_id,
    ae.reference_achat,
    p.nom as fournisseur_nom,
    p.contact as fournisseur_contact,
    p.site as fournisseur_site,
    p.classement as fournisseur_classement,
    oc.type_operation,
    oc.mode_paiement,
    oc.reference_paiement,
    oc.montant,
    oc.date_paiement,
    oc.effectue_par,
    oc.commentaires,
    oc.statut,
    oc.date_creation,
    oc.cree_par
FROM operation_caisse oc
LEFT JOIN achat_entete ae ON oc.achat_id = ae.id
LEFT JOIN producteurs p ON ae.fournisseur_id = p.id
WHERE oc.type_operation = 'PAIEMENT_ACHAT'
ORDER BY oc.date_paiement DESC;

-- Vue pour les avances fournisseurs
CREATE OR REPLACE VIEW `v_avances_fournisseurs` AS
SELECT 
    p.id as fournisseur_id,
    p.nom as fournisseur_nom,
    COALESCE(SUM(CASE WHEN oc.type_operation = 'AVANCE' THEN oc.montant ELSE 0 END), 0) as total_avances,
    COALESCE(SUM(CASE WHEN oc.type_operation = 'REMBOURSEMENT' THEN oc.montant ELSE 0 END), 0) as total_remboursements,
    COALESCE(SUM(CASE WHEN oc.type_operation = 'AVANCE' THEN oc.montant ELSE 0 END), 0) - 
    COALESCE(SUM(CASE WHEN oc.type_operation = 'REMBOURSEMENT' THEN oc.montant ELSE 0 END), 0) as solde_avance
FROM producteurs p
LEFT JOIN operation_caisse oc ON p.id = oc.fournisseur_id
GROUP BY p.id, p.nom;

-- Vue pour le stock total par produit
CREATE OR REPLACE VIEW `v_stock_total_produits` AS
SELECT 
    p.id as produit_id,
    p.nom as produit_nom,
    COALESCE(SUM(ps.quantite), 0) as stock_total,
    p.unite_stock_id,
    u.libelle as unite_stock_libelle
FROM produits p
LEFT JOIN produits_stock ps ON p.id = ps.produit_id
LEFT JOIN unites u ON p.unite_stock_id = u.id
GROUP BY p.id, p.nom, p.unite_stock_id, u.libelle;

-- ===================================================================
-- TRIGGERS POUR LA GESTION AUTOMATIQUE DES MOUVEMENTS
-- ===================================================================

-- Trigger pour INSERT dans achat_detail
DELIMITER $$
CREATE TRIGGER `tr_achat_detail_insert` 
AFTER INSERT ON `achat_detail`
FOR EACH ROW
BEGIN
    -- Déclarer toutes les variables au début
    DECLARE unite_stock_id INT;
    DECLARE ref_achat VARCHAR(100);
    
    -- Récupérer l'unité de stock du produit
    SELECT unite_stock_id INTO unite_stock_id FROM produits WHERE id = NEW.produit_id;
    
    -- Récupérer la référence de l'achat
    SELECT reference_achat INTO ref_achat FROM achat_entete WHERE id = NEW.achat_entete_id;
    
    -- Insérer le mouvement d'entrée
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        reference_document, lot_numero, motif, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'ENTREE', NEW.qte_nette_controlee,
        unite_stock_id, ref_achat, NEW.lot_numero, 
        CONCAT('Validation achat ', ref_achat, ' - ', NEW.qte_nette_controlee, ' kg'), 
        NEW.date_creation, NEW.cree_par
    );
END$$
DELIMITER ;

-- Trigger pour UPDATE dans achat_detail
DELIMITER $$
CREATE TRIGGER `tr_achat_detail_update` 
AFTER UPDATE ON `achat_detail`
FOR EACH ROW
BEGIN
    -- Déclarer toutes les variables au début
    DECLARE unite_stock_id INT;
    DECLARE ref_achat VARCHAR(100);
    
    -- Supprimer l'ancien mouvement
    DELETE FROM mouvements_stock 
    WHERE reference_document = (SELECT reference_achat FROM achat_entete WHERE id = NEW.achat_entete_id)
    AND produit_id = NEW.produit_id AND depot_id = NEW.depot_id;
    
    -- Récupérer l'unité de stock du produit
    SELECT unite_stock_id INTO unite_stock_id FROM produits WHERE id = NEW.produit_id;
    
    -- Récupérer la référence de l'achat
    SELECT reference_achat INTO ref_achat FROM achat_entete WHERE id = NEW.achat_entete_id;
    
    -- Insérer le nouveau mouvement d'entrée
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        reference_document, lot_numero, motif, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'ENTREE', NEW.qte_nette_controlee,
        unite_stock_id, ref_achat, NEW.lot_numero, 
        CONCAT('Validation achat ', ref_achat, ' - ', NEW.qte_nette_controlee, ' kg'), 
        NEW.date_creation, NEW.cree_par
    );
END$$
DELIMITER ;

-- Trigger pour DELETE dans achat_detail
DELIMITER $$
CREATE TRIGGER `tr_achat_detail_delete` 
AFTER DELETE ON `achat_detail`
FOR EACH ROW
BEGIN
    -- Supprimer le mouvement correspondant
    DELETE FROM mouvements_stock 
    WHERE reference_document = (SELECT reference_achat FROM achat_entete WHERE id = OLD.achat_entete_id)
    AND produit_id = OLD.produit_id AND depot_id = OLD.depot_id;
END$$
DELIMITER ;

-- ===================================================================
-- FINALISATION
-- ===================================================================

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

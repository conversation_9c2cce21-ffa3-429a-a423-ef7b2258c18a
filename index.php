<?php
  if (session_status() === PHP_SESSION_NONE) session_start();
  require_once  './config/config.php';
  require_once CONFIG_PATH . '/database.php';
  require_once INCLUDES_PATH . '/auth.php';
  require_once INCLUDES_PATH . '/rbac.php';

  // Vérification de l'authentification
  $auth = new Auth();
  $auth->checkAuth();


  $currentUser = $_SESSION['username'];
?>
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Sambirano-SA</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
  <link rel="stylesheet" href="assets/css/index.css">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
  <script src="assets/js/iframe-fix.js"></script>
</head>
<body>

<div class="container-fluid p-0">
  <div class="tab-bar" id="tabList">
    <!-- App launcher -->
    <div class="dropdown">
      <div class="app-launcher" data-bs-toggle="dropdown">
        <i class="fa fa-th"></i>
      </div>
        <ul class="dropdown-menu">
         <li><a class="dropdown-item app-menu-item" href="#" data-tab-id="app1"  data-tab-url="./pages/gestion_achats.php"  data-tab-title="Achats" data-tab-icon='<i class="fa-solid fa-shopping-cart me-1"></i>'>📦 Achats matière première</a></li>
         <li><a class="dropdown-item app-menu-item" href="#" data-tab-id="app7"  data-tab-url="./pages/gestion_ventes.php"  data-tab-title="Ventes" data-tab-icon='<i class="fa-solid fa-shopping-cart me-1"></i>'>📦 Ventes matière première</a></li>
         <li><a class="dropdown-item app-menu-item" href="#" data-tab-id="app2"  data-tab-url="./pages/gestion_paiements.php"  data-tab-title="Paiements matière première" data-tab-icon='<i class="fa-solid fa-warehouse me-1"></i>'>📊 Paiements matière première</a></li>
         <li><a class="dropdown-item app-menu-item" href="#" data-tab-id="app8"  data-tab-url="./pages/gestion_paiements_ventes.php"  data-tab-title="Paiements matière première" data-tab-icon='<i class="fa-solid fa-warehouse me-1"></i>'>📊 Paiements ventes</a></li>
         <li><a class="dropdown-item app-menu-item" href="#" data-tab-id="app3"  data-tab-url="./pages/gestion_produits.php"  data-tab-title="Matière première" data-tab-icon='<i class="fa-solid fa-box me-1"></i>'>📦 Matière première</a></li>
         <li><a class="dropdown-item app-menu-item" href="#" data-tab-id="app6"  data-tab-url="./pages/gestion_producteurs.php"  data-tab-title="Producteurs" data-tab-icon='<i class="fa-solid fa-users me-1"></i>'>👥 Producteurs</a></li>
         <li><a class="dropdown-item app-menu-item" href="#" data-tab-id="app4"  data-tab-url="https://www.google.com/search/howsearchworks/?utm_source=about.google&utm_medium=referral&utm_campaign=productspage"  data-tab-title="Rapports" data-tab-icon='<i class="fa-solid fa-chart-line me-1"></i>'>📈 Rapports</a></li>
         <li><a class="dropdown-item app-menu-item" href="#" data-tab-id="app5"  data-tab-url="https://about.google/products/?tab=rh"  data-tab-title="Utilisateurs" data-tab-icon='<i class="fa-solid fa-users me-1"></i>'>👥 Utilisateurs</a></li>
        </ul>
    </div>
 
    <!-- User Menu -->
    <div class="dropdown user-menu">
      <i class="fa fa-user-circle" data-bs-toggle="dropdown"></i>
      <ul class="dropdown-menu dropdown-menu-end">
        <li><a class="dropdown-item" href="#">Profil</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item text-danger" href="#">Logout</a></li>
      </ul>
    </div>
  </div>

  <!-- Contenu des tabs -->
  <div class="tab-content" id="tabContent"> 
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>

// REMPLACEZ VOTRE SCRIPT EXISTANT PAR CELUI-CI

// Fonction générique pour ouvrir ou activer un onglet
function openTab(tabId, tabTitle, iconHtml, url) { // url ajouté
    let existingTab = $(`.custom-tab[data-id="${tabId}"]`);
    if (existingTab.length > 0) {
        existingTab.click();
        return;
    }

    $(".custom-tab").removeClass("active");
    $(".tab-pane").removeClass("active");

    // HTML pour le nouvel onglet (on ajoute une classe au bouton refresh)
    let newTabHtml = `
        <div class="custom-tab active" data-id="${tabId}">
            ${iconHtml || '<i class="fa-solid fa-window-maximize me-1"></i>'}
            ${tabTitle}
            <div class="dropdown tab-actions">
                <button class="btn dropdown-toggle" data-bs-toggle="dropdown" data-bs-container="body"></button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item refresh-tab-btn" href="#">Refresh Tab</a></li>
                    <li><a class="dropdown-item" href="#">Close Others</a></li>
                </ul>
            </div>
            <span class="close">&times;</span>
        </div>`;

    // MODIFICATION : Le contenu est maintenant un iframe
    let newPaneHtml = `
        <div class="tab-pane active" data-id="${tabId}">
            <iframe src="${url}"></iframe>
        </div>`;

    $('.user-menu').before(newTabHtml);
    $('#tabContent').append(newPaneHtml);
}

$(function(){
  // Activer un onglet
  $(document).on("click", ".custom-tab", function(e){
    if($(e.target).hasClass("close") || $(e.target).closest(".dropdown").length) return;
    $(".custom-tab").removeClass("active");
    $(this).addClass("active");
    let id = $(this).data("id");
    $(".tab-pane").removeClass("active");
    $('.tab-pane[data-id="'+id+'"]').addClass("active");
  });

  // Fermer un onglet
  $(document).on("click", ".custom-tab .close", function(){
    let parent = $(this).closest(".custom-tab");
    let id = parent.data("id");
    parent.remove();
    $('.tab-pane[data-id="'+id+'"]').remove();
    if(!$(".custom-tab.active").length && $(".custom-tab").length){
      $(".custom-tab").first().addClass("active");
      let firstId = $(".custom-tab").first().data("id");
      $('.tab-pane[data-id="'+firstId+'"]').addClass("active");
    }
  });

  // Drag & Drop
  $("#tabList").sortable({
    axis: "x",
    items: ".custom-tab"
  });

    // AJOUTEZ CE CODE DANS VOTRE BLOC SCRIPT EXISTANT
    // Écouteur pour les clics sur les items du menu
    $(document).on('click', '.app-menu-item', function(e) {
        e.preventDefault();
        let tabId = $(this).data('tab-id');
        let tabTitle = $(this).data('tab-title');
        let tabIcon = $(this).data('tab-icon');
        let tabUrl = $(this).data('tab-url'); // On récupère l'URL
        
        openTab(tabId, tabTitle, tabIcon, tabUrl); // On passe l'URL à la fonction
    });

    // NOUVEAU : Logique pour le bouton "Refresh Tab"
    $(document).on('click', '.refresh-tab-btn', function(e) {
        e.preventDefault();
        // 1. Trouver l'onglet parent pour obtenir son ID
        let parentTab = $(this).closest('.custom-tab');
        let tabId = parentTab.data('id');

        // 2. Trouver l'iframe correspondant dans le tab-content
        let targetIframe = $(`.tab-pane[data-id="${tabId}"]`).find('iframe');

        // 3. Recharger l'iframe en réaffectant sa source
        if (targetIframe.length > 0) {
            targetIframe.attr('src', targetIframe.attr('src'));
        }
    });

 

});
</script>

</body>
</html>
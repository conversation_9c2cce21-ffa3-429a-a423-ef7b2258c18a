// Variables globales
let produitsTable, unitesTable, categoriesTable, depotsTable;
let currentProduitId = null;

 

function initDataTables() {
    try {
        // Table Produits
        // On initialise la table SEULEMENT si elle n'est pas déjà une DataTable.
        if (!$.fn.DataTable.isDataTable('#tableProduits')) {
            produitsTable = $('#tableProduits').DataTable({
                pageLength: 20,
                lengthMenu: [30, 100, 200],
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                },
                        columnDefs: [
                            { orderable: false, targets: [0, 7] }, // Colonnes checkbox et actions
                            { className: "text-center", targets: [6] }, // Colonne unités et conversion centrée
                            { className: "text-end", targets: [4] } // Prix aligné à droite
                        ]
            });
        }

        // Tables modals - On applique la même logique
        if ($('#unitesTable').length && !$.fn.DataTable.isDataTable('#unitesTable')) {
            unitesTable = $('#unitesTable').DataTable({
                paging: false,
                searching: false,
                info: false,
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                }
            });
        }

        if ($('#categoriesTable').length && !$.fn.DataTable.isDataTable('#categoriesTable')) {
            categoriesTable = $('#categoriesTable').DataTable({
                paging: false,
                searching: false,
                info: false,
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                }
            });
        }

        if ($('#depotsTable').length && !$.fn.DataTable.isDataTable('#depotsTable')) {
            depotsTable = $('#depotsTable').DataTable({
                paging: false,
                searching: false,
                info: false,
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                }
            });
        }
    } catch (error) {
        console.error('Erreur lors de l\'initialisation des DataTables:', error);
    }
}



$(document).ready(function () {
    // Initialisation des DataTables
    initDataTables();
    
    // Chargement des données
    loadCategoriesCache();
    loadUnitesCache();
    loadProduits();
    loadUnites();
    loadCategories();
    loadDepots();
    
    // Charger les selects du formulaire produit
    loadCategoriesInSelect();
    loadUnitesInSelects();
    loadPresentationsInSelect();
    loadFormesInSelect();
    
    // Charger l'historique des mouvements
    loadHistoriqueMouvements();
    
    // Charger les données d'ajustement
    loadAjustementData();

    // Event handlers
    setupEventHandlers();
    
    // Fonctionnalités de sélection multiple
    setupSelectionHandlers();
});

// Fonction pour réinitialiser complètement les DataTables
function reinitDataTables() {
    destroyDataTables();
    initDataTables();
}

 

function destroyDataTables() {
    try {
        // Détruire les DataTables si elles existent
        if ($('#tableProduits').length && $.fn.DataTable.isDataTable('#tableProduits')) {
            $('#tableProduits').DataTable().destroy();
        }
        if ($('#unitesTable').length && $.fn.DataTable.isDataTable('#unitesTable')) {
            $('#unitesTable').DataTable().destroy();
        }
        if ($('#categoriesTable').length && $.fn.DataTable.isDataTable('#categoriesTable')) {
            $('#categoriesTable').DataTable().destroy();
        }
        if ($('#depotsTable').length && $.fn.DataTable.isDataTable('#depotsTable')) {
            $('#depotsTable').DataTable().destroy();
        }
    } catch (error) {
        console.error('Erreur lors de la destruction des DataTables:', error);
    }
}

function setupEventHandlers() {
    // Bouton Nouveau Produit
    $('[data-bs-target="#modalProduit"]').click(function() {
        currentProduitId = null;
        clearProduitForm();
    });

    // Sauvegarde Produit
    $('.modal-footer .btn-outline-primary').click(function() {
        if ($(this).text().includes('Sauver')) {
            saveProduit();
        }
    });

    // Boutons CRUD Unités
    $("#btnAddUnite").click(() => addUnite());
    $(document).on('click', '.btnDeleteUnite', function() {
        const id = $(this).data('id');
        deleteUnite(id);
    });
    $(document).on('click', '.btnEditUnite', function() {
        const id = $(this).data('id');
        editUnite(id);
    });

    // Boutons CRUD Catégories
    $("#btnAddCategorie").click(() => addCategorie());
    $(document).on('click', '.btnDeleteCategorie', function() {
        const id = $(this).data('id');
        deleteCategorie(id);
    });
    $(document).on('click', '.btnEditCategorie', function() {
        const id = $(this).data('id');
        editCategorie(id);
    });

    // Boutons CRUD Dépôts
    $("#btnAddDepot").click(() => addDepot());
    $(document).on('click', '.btnDeleteDepot', function() {
        const id = $(this).data('id');
        deleteDepot(id);
    });
    $(document).on('click', '.btnEditDepot', function() {
        const id = $(this).data('id');
        editDepot(id);
    });

    // Actions Produits
    $(document).on('click', '.btn-view-produit', function() {
        const id = $(this).data('id');
        viewProduit(id);
    });
    $(document).on('click', '.btn-edit-produit', function() {
        const id = $(this).data('id');
        editProduit(id);
    });
    $(document).on('click', '.btn-delete-produit', function() {
        const id = $(this).data('id');
        deleteProduit(id);
    });
    $(document).on('click', '.btn-clone-produit', function() {
        const id = $(this).data('id');
        cloneProduit(id);
    });
    
    // Boutons de sélection multiple
    $('#btnCloneSelected').on('click', function() {
        const selectedIds = getSelectedProductIds();
        if (selectedIds.length === 0) {
            showAlert('Erreur', 'Veuillez sélectionner au moins un produit', 'warning');
            return;
        }
        if (selectedIds.length > 1) {
            showAlert('Erreur', 'Veuillez sélectionner un seul produit à cloner', 'warning');
            return;
        }
        cloneProduit(selectedIds[0]);
    });
    
    $('#btnDeleteSelected').on('click', function() {
        const selectedIds = getSelectedProductIds();
        if (selectedIds.length === 0) {
            showAlert('Erreur', 'Veuillez sélectionner au moins un produit', 'warning');
            return;
        }
        deleteMultipleProduits(selectedIds);
    });
}

// ===== FONCTIONS PRODUITS =====
function loadProduits() {
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                p.*,
                c.libelle as categorie_libelle,
                uv.libelle as unite_vente_libelle,
                ua.libelle as unite_achat_libelle,
                us.libelle as unite_stock_libelle,
                pr.libelle as presentation_libelle,
                f.libelle as forme_libelle,
                COALESCE(SUM(ps.quantite), 0) as stock_total
              FROM produits p
              LEFT JOIN categorie c ON p.categorie_id = c.id
              LEFT JOIN unites uv ON p.unite_vente_id = uv.id
              LEFT JOIN unites ua ON p.unite_achat_id = ua.id
              LEFT JOIN unites us ON p.unite_stock_id = us.id
              LEFT JOIN presentation pr ON p.presentation_id = pr.id
              LEFT JOIN forme f ON p.forme_id = f.id
              LEFT JOIN produits_stock ps ON p.id = ps.produit_id
              GROUP BY p.id
              ORDER BY p.nom`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            displayProduits(response.data);
        } else {
            showAlert('Erreur', response.message, 'danger');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement des produits:', error);
        showAlert('Erreur', 'Impossible de charger les produits', 'danger');
    });
}

function displayProduits(produits) {
    produitsTable.clear();
    produits.forEach(produit => {
        // Utiliser le stock de la base de données
        const stock = parseFloat(produit.stock_total) || 0;
        const uniteStock = produit.unite_stock_libelle || 'KG';
        const presentation = produit.presentation_libelle;
        const qtePresentation = produit.qte_presentation || 1;
        const forme = produit.forme_libelle;
        const qteForme = produit.qte_forme || 67;
        const stockFormatted = formatStockQuantity(stock, uniteStock, presentation, qtePresentation, forme, qteForme);
        const prixText = `${parseFloat(produit.prix_vente).toLocaleString()} Ar`;
        
        // Créer la colonne compacte des unités et conversion
        const unitesConversion = createUnitesConversionColumn(produit);
        
        // Déterminer la couleur de la ligne selon les seuils
        const stockMin = parseFloat(produit.stock_min) || 0;
        const stockMax = parseFloat(produit.stock_max) || 0;
        let rowClass = '';
        
        if (stock > 0) {
            if (stock <= stockMin) {
                rowClass = 'table-warning'; // Orange pour seuil min atteint
            } else if (stock >= stockMax && stockMax > 0) {
                rowClass = 'table-success'; // Vert clair pour seuil max atteint
            }
        }
        
        const row = produitsTable.row.add([
            `<input type="checkbox" value="${produit.id}">`,
            produit.nom,
            produit.type,
            produit.certification,
            prixText,
            stockFormatted,
            unitesConversion,
            `<button class="btn btn-sm btn-outline-info btn-view-produit" data-id="${produit.id}" title="Voir détails">
                <i class="fas fa-eye"></i>
            </button> ` +
            `<button class="btn btn-sm btn-outline-warning btn-edit-produit" data-id="${produit.id}" title="Modifier">
                <i class="fas fa-edit"></i>
            </button> ` +
            `<button class="btn btn-sm btn-outline-success btn-clone-produit" data-id="${produit.id}" title="Cloner">
                <i class="fas fa-copy"></i>
            </button> ` +
            `<button class="btn btn-sm btn-outline-danger btn-delete-produit" data-id="${produit.id}" title="Supprimer">
                <i class="fas fa-trash"></i>
            </button>`
        ]);
        
        // Appliquer la classe de couleur à la ligne
        if (rowClass) {
            $(row.node()).addClass(rowClass);
        }
    });
    produitsTable.draw();
}

function saveProduit() {
    // Validation des données
    const nom = $('#modalProduit input[placeholder="Nom"]').val().trim();
    const categorieId = $('#categorieSelect').val();
    const type = $('#modalProduit select').eq(1).val();
    const certification = $('#modalProduit select').eq(2).val();
    const prixVente = parseFloat($('#modalProduit input[placeholder="Prix Vente"]').val()) || 0;
    const prixAchat = parseFloat($('#modalProduit input[placeholder="Prix Achat"]').val()) || 0;
    const stockMin = parseInt($('#modalProduit input[placeholder="Seuil Min"]').val()) || 0;
    const stockMax = parseInt($('#modalProduit input[placeholder="Seuil Max"]').val()) || 0;
    const uniteVenteId = $('#uniteVenteSelect').val();
    const uniteAchatId = $('#uniteAchatSelect').val();
    const uniteStockId = $('#uniteStockSelect').val();
    const presentationId = $('#presentationSelect').val();
    const formeId = $('#formeSelect').val();
    const qtePresentation = parseInt($('#qtePresentation').val()) || 0;
    const qteForme = parseInt($('#qteForme').val()) || 0;

    // Validation
    if (!nom) {
        showAlert('Erreur', 'Le nom du produit est requis', 'error');
        return;
    }
    if (!categorieId) {
        showAlert('Erreur', 'Veuillez sélectionner une catégorie', 'error');
        return;
    }
    if (!uniteVenteId) {
        showAlert('Erreur', 'Veuillez sélectionner une unité de vente', 'error');
        return;
    }
    if (!uniteAchatId) {
        showAlert('Erreur', 'Veuillez sélectionner une unité d\'achat', 'error');
        return;
    }
    if (!uniteStockId) {
        showAlert('Erreur', 'Veuillez sélectionner une unité de stock', 'error');
        return;
    }
    if (!presentationId) {
        showAlert('Erreur', 'Veuillez sélectionner une présentation', 'error');
        return;
    }
    if (!formeId) {
        showAlert('Erreur', 'Veuillez sélectionner une forme', 'error');
        return;
    }
    if (qtePresentation <= 0) {
        showAlert('Erreur', 'La quantité de présentation doit être supérieure à 0', 'error');
        return;
    }
    if (qteForme <= 0) {
        showAlert('Erreur', 'La quantité de forme doit être supérieure à 0', 'error');
        return;
    }
    if (prixVente <= 0) {
        showAlert('Erreur', 'Le prix de vente doit être supérieur à 0', 'error');
        return;
    }

    const formData = {
        nom: nom,
        categorie_id: parseInt(categorieId),
        type: type,
        certification: certification,
        prix_vente: prixVente,
        prix_achat: prixAchat,
        stock_min: stockMin,
        stock_max: stockMax,
        unite_vente_id: parseInt(uniteVenteId),
        unite_achat_id: parseInt(uniteAchatId),
        unite_stock_id: parseInt(uniteStockId),
        presentation_id: parseInt(presentationId),
        forme_id: parseInt(formeId),
        qte_presentation: qtePresentation,
        qte_forme: qteForme,
        classe: 'PRODUITS'
    };

    // Détermination de l’action
    const action = currentProduitId ? 'update' : 'create';

    // Construction dynamique des données envoyées
    const dataToSend = {
        action: action,
        table: 'produits',
        data: JSON.stringify(formData)
    };

    // Ajouter l'ID seulement si c’est une mise à jour
    if (currentProduitId) {
        dataToSend.id = currentProduitId;
    }

    $.ajax({
        url: '../includes/traitement.php',
        type: 'POST',
        dataType: 'json',
        data: dataToSend,
        beforeSend: function() {
            // Exemple : désactiver le bouton pendant l’envoi
            $('#btnSaveProduit').prop('disabled', true).text('Enregistrement...');
        },
        success: function(response) {
            if (response.success) {
                showAlert('Succès', response.message, 'success');
                $('#modalProduit').modal('hide');
                loadProduits();
            } else {
                showAlert('Erreur', response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Erreur AJAX :', error);
            showAlert('Erreur', 'Impossible de sauvegarder le produit', 'error');
        },
        complete: function() {
            // Réactiver le bouton après traitement
            $('#btnSaveProduit').prop('disabled', false).text('Enregistrer');
        }
    });
}

function editProduit(id) {
    currentProduitId = id;
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'produits',
        data: JSON.stringify({ id: id })
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const produit = response.data[0];
            
            // Charger les catégories et unités si pas encore fait
            if ($('#categorieSelect option').length <= 1) {
                loadCategoriesInSelect();
            }
            if ($('#uniteVenteSelect option').length <= 1) {
                loadUnitesInSelects();
            }
            
            // Remplir le formulaire
            $('#modalProduit input[placeholder="Nom"]').val(produit.nom);
            $('#categorieSelect').val(produit.categorie_id);
            $('#modalProduit select').eq(1).val(produit.type);
            $('#modalProduit select').eq(2).val(produit.certification);
            $('#modalProduit input[placeholder="Prix Vente"]').val(produit.prix_vente);
            $('#modalProduit input[placeholder="Prix Achat"]').val(produit.prix_achat);
            $('#modalProduit input[placeholder="Seuil Min"]').val(produit.stock_min);
            $('#modalProduit input[placeholder="Seuil Max"]').val(produit.stock_max);
            $('#uniteVenteSelect').val(produit.unite_vente_id);
            $('#uniteAchatSelect').val(produit.unite_achat_id);
            $('#uniteStockSelect').val(produit.unite_stock_id);
            $('#presentationSelect').val(produit.presentation_id);
            $('#formeSelect').val(produit.forme_id);
            $('#qtePresentation').val(produit.qte_presentation);
            $('#qteForme').val(produit.qte_forme);
            $('#modalProduit').modal('show');
        } else {
            showAlert('Erreur', response.message || 'Produit non trouvé', 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement du produit:', error);
        showAlert('Erreur', 'Impossible de charger les données du produit', 'error');
    });
}

function deleteProduit(id) {
    showConfirm('Confirmer la suppression', 'Êtes-vous sûr de vouloir supprimer ce produit ?', 'Oui, supprimer', 'Annuler')
    .then((result) => {
        if (result.isConfirmed) {
            $.post('../includes/traitement.php', {
                action: 'delete',
                table: 'produits',
                id: id
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', response.message, 'success');
                    loadProduits();
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function(xhr, status, error) {
                console.error('Erreur lors de la suppression:', error);
                showAlert('Erreur', 'Impossible de supprimer le produit', 'error');
            });
        }
    });
}

function viewProduit(id) {
    // Charger les détails du stock par dépôt depuis la vue v_stock_actuel
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                depot_nom as depot,
                stock_actuel as quantite,
                unite_stock,
                statut_stock,
                lot_numero,
                date_entree,
                date_expiration
              FROM v_stock_actuel 
              WHERE produit_id = ? AND stock_actuel > 0
              ORDER BY depot_nom, lot_numero`,
        params: JSON.stringify([id])
    }).done(function(response) {
        if (response.success) {
            displayStockDetails(response.data);
            $('#modalView').modal('show');
        } else {
            showAlert('Erreur', response.message || 'Impossible de charger les détails du stock', 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement des détails:', error);
        showAlert('Erreur', 'Impossible de charger les détails du stock', 'error');
    });
}

function displayStockDetails(stockData) {
    const tbody = $('#modalView tbody');
    tbody.empty();
    if (stockData.length === 0) {
        tbody.append('<tr><td colspan="6" class="text-center text-muted">Aucun stock disponible pour ce produit</td></tr>');
    } else {
        stockData.forEach(item => {
            // Appliquer la conversion pour l'affichage
            const quantiteFormatted = formatStockQuantity(item.quantite, item.unite_stock);
            
            // Déterminer la classe CSS selon le statut du stock
            let statusClass = '';
            let statusText = '';
            switch(item.statut_stock) {
                case 'STOCK_FAIBLE':
                    statusClass = 'text-danger';
                    statusText = 'Stock Faible';
                    break;
                case 'STOCK_ELEVE':
                    statusClass = 'text-success';
                    statusText = 'Stock Élevé';
                    break;
                default:
                    statusClass = 'text-primary';
                    statusText = 'Stock Normal';
            }
            
            tbody.append(`
                <tr>
                    <td>${item.depot}</td>
                    <td>${item.lot_numero || 'N/A'}</td>
                    <td class="text-end">${quantiteFormatted}</td>
                    <td>${item.date_entree ? item.date_entree.split(' ')[0] : 'N/A'}</td>
                    <td>${item.date_expiration || 'N/A'}</td>
                    <td class="text-center">
                        <span class="badge ${statusClass.replace('text-', 'bg-')}">${statusText}</span>
                    </td>
                </tr>
            `);
        });
    }
}

function clearProduitForm() {
    $('#modalProduit form')[0].reset();
    $('#categorieSelect').val(''); // Réinitialiser le select de catégorie
    $('#uniteVenteSelect').val(''); // Réinitialiser les selects d'unités
    $('#uniteAchatSelect').val('');
    $('#uniteStockSelect').val('');
    $('#presentationSelect').val(''); // Réinitialiser les selects de présentation et forme
    $('#formeSelect').val('');
    currentProduitId = null; // Réinitialiser l'ID du produit en cours
}

// ===== FONCTIONS UNITÉS =====
function loadUnites() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'unites'
    }).done(function(response) {
        if (response.success) {
            displayUnites(response.data);
        } else {
            showAlert('Erreur', response.message, 'danger');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement des unités:', error);
        showAlert('Erreur', 'Impossible de charger les unités', 'danger');
    });
}

function displayUnites(unites) {
    if (unitesTable) {
        unitesTable.clear();
        unites.forEach(unite => {
        unitesTable.row.add([
            unite.libelle,
            unite.est_actif ? '✔' : '✗',
            `<button class="btn btn-sm btn-warning btnEditUnite" data-id="${unite.id}" title="Modifier">
                <i class="fas fa-edit"></i>
            </button> ` +
            `<button class="btn btn-sm btn-danger btnDeleteUnite" data-id="${unite.id}" title="Supprimer">
                <i class="fas fa-trash"></i>
            </button>`
        ]);
        });
        unitesTable.draw();
    }
}

function addUnite() {
    // Fermer le modal des unités avant d'ouvrir SweetAlert2
    $('#modalUnites').modal('hide');
    
    // Attendre que le modal soit fermé avant d'ouvrir SweetAlert2
    setTimeout(() => {
        Swal.fire({
            title: 'Nouvelle unité',
            input: 'text',
            inputLabel: 'Libellé de l\'unité',
            inputPlaceholder: 'Entrez le libellé',
            showCancelButton: true,
            confirmButtonText: 'Ajouter',
            cancelButtonText: 'Annuler',
            inputValidator: (value) => {
                if (!value) {
                    return 'Le libellé est requis !';
                }
            }
        }).then((result) => {
        if (result.isConfirmed) {
            $.post('../includes/traitement.php', {
                action: 'create',
                table: 'unites',
                data: JSON.stringify({ libelle: result.value, est_actif: 1 })
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', response.message, 'success');
                    loadUnites();
                    // Rouvrir le modal des unités après succès
                    setTimeout(() => {
                        $('#modalUnites').modal('show');
                    }, 1000);
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function(xhr, status, error) {
                console.error('Erreur lors de l\'ajout:', error);
                showAlert('Erreur', 'Impossible d\'ajouter l\'unité', 'error');
            });
        }
        });
    }, 300); // Attendre 300ms pour que le modal soit complètement fermé
}

function editUnite(id) {
    // Fermer le modal des unités avant d'ouvrir SweetAlert2
    $('#modalUnites').modal('hide');
    
    // Attendre que le modal soit fermé avant d'ouvrir SweetAlert2
    setTimeout(() => {
        Swal.fire({
            title: 'Modifier l\'unité',
            input: 'text',
            inputLabel: 'Nouveau libellé',
            inputPlaceholder: 'Entrez le nouveau libellé',
            showCancelButton: true,
            confirmButtonText: 'Modifier',
            cancelButtonText: 'Annuler',
            inputValidator: (value) => {
                if (!value) {
                    return 'Le libellé est requis !';
                }
            }
        }).then((result) => {
        if (result.isConfirmed) {
            $.post('../includes/traitement.php', {
                action: 'update',
                table: 'unites',
                id: id,
                data: JSON.stringify({ libelle: result.value })
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', response.message, 'success');
                    loadUnites();
                    // Rouvrir le modal des unités après succès
                    setTimeout(() => {
                        $('#modalUnites').modal('show');
                    }, 1000);
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function(xhr, status, error) {
                console.error('Erreur lors de la modification:', error);
                showAlert('Erreur', 'Impossible de modifier l\'unité', 'error');
            });
        }
        });
    }, 300); // Attendre 300ms pour que le modal soit complètement fermé
}

function deleteUnite(id) {
    // Fermer le modal des unités avant d'ouvrir SweetAlert2
    $('#modalUnites').modal('hide');
    
    // Attendre que le modal soit fermé avant d'ouvrir SweetAlert2
    setTimeout(() => {
        showConfirm('Confirmer la suppression', 'Êtes-vous sûr de vouloir supprimer cette unité ?', 'Oui, supprimer', 'Annuler')
        .then((result) => {
        if (result.isConfirmed) {
            $.post('../includes/traitement.php', {
                action: 'delete',
                table: 'unites',
                id: id
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', response.message, 'success');
                    loadUnites();
                    // Rouvrir le modal des unités après succès
                    setTimeout(() => {
                        $('#modalUnites').modal('show');
                    }, 1000);
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function(xhr, status, error) {
                console.error('Erreur lors de la suppression:', error);
                showAlert('Erreur', 'Impossible de supprimer l\'unité', 'error');
            });
        }
        });
    }, 300); // Attendre 300ms pour que le modal soit complètement fermé
}

// ===== FONCTIONS CATÉGORIES =====
function loadCategories() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'categorie'
    }).done(function(response) {
        if (response.success) {
            displayCategories(response.data);
        } else {
            showAlert('Erreur', response.message, 'danger');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement des catégories:', error);
        showAlert('Erreur', 'Impossible de charger les catégories', 'danger');
    });
}

function displayCategories(categories) {
    if (categoriesTable) {
        categoriesTable.clear();
        categories.forEach(categorie => {
        categoriesTable.row.add([
            categorie.libelle,
            categorie.classement,
            categorie.est_actif ? '✔' : '✗',
            `<button class="btn btn-sm btn-warning btnEditCategorie" data-id="${categorie.id}" title="Modifier">
                <i class="fas fa-edit"></i>
            </button> ` +
            `<button class="btn btn-sm btn-danger btnDeleteCategorie" data-id="${categorie.id}" title="Supprimer">
                <i class="fas fa-trash"></i>
            </button>`
        ]);
        });
        categoriesTable.draw();
    }
}

function addCategorie() {
    // Fermer le modal des catégories avant d'ouvrir SweetAlert2
    $('#modalCategories').modal('hide');
    
    // Attendre que le modal soit fermé avant d'ouvrir SweetAlert2
    setTimeout(() => {
        Swal.fire({
            title: 'Nouvelle catégorie',
            html: `
                <div class="mb-3">
                    <label class="form-label">Libellé de la catégorie</label>
                    <input type="text" id="libelle" class="form-control" placeholder="Entrez le libellé">
                </div>
                <div class="mb-3">
                    <label class="form-label">Classement</label>
                    <select id="classement" class="form-select">
                        <option value="PRODUITS">PRODUITS</option>
                        <option value="FOURNITURES">FOURNITURES</option>
                    </select>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Ajouter',
            cancelButtonText: 'Annuler',
            preConfirm: () => {
                const libelle = document.getElementById('libelle').value;
                const classement = document.getElementById('classement').value;
                if (!libelle) {
                    Swal.showValidationMessage('Le libellé est requis !');
                    return false;
                }
                return { libelle, classement };
            }
        }).then((result) => {
        if (result.isConfirmed) {
            $.post('../includes/traitement.php', {
                action: 'create',
                table: 'categorie',
                data: JSON.stringify({ 
                    libelle: result.value.libelle, 
                    classement: result.value.classement,
                    est_actif: 1 
                })
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', response.message, 'success');
                    loadCategories();
                    // Rouvrir le modal des catégories après succès
                    setTimeout(() => {
                        $('#modalCategories').modal('show');
                    }, 1000);
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function(xhr, status, error) {
                console.error('Erreur lors de l\'ajout:', error);
                showAlert('Erreur', 'Impossible d\'ajouter la catégorie', 'error');
            });
        }
        });
    }, 300); // Attendre 300ms pour que le modal soit complètement fermé
}

function editCategorie(id) {
    // Fermer le modal des catégories avant d'ouvrir SweetAlert2
    $('#modalCategories').modal('hide');
    
    // Attendre que le modal soit fermé avant d'ouvrir SweetAlert2
    setTimeout(() => {
        Swal.fire({
            title: 'Modifier la catégorie',
            input: 'text',
            inputLabel: 'Nouveau libellé',
            inputPlaceholder: 'Entrez le nouveau libellé',
            showCancelButton: true,
            confirmButtonText: 'Modifier',
            cancelButtonText: 'Annuler',
            inputValidator: (value) => {
                if (!value) {
                    return 'Le libellé est requis !';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'update',
                    table: 'categorie',
                    id: id,
                    data: JSON.stringify({ libelle: result.value })
                }).done(function(response) {
                    if (response.success) {
                        showAlert('Succès', response.message, 'success');
                        loadCategories();
                        // Rouvrir le modal des catégories après succès
                        setTimeout(() => {
                            $('#modalCategories').modal('show');
                        }, 1000);
                    } else {
                        showAlert('Erreur', response.message, 'danger');
                    }
                });
            }
        });
    }, 300); // Attendre 300ms pour que le modal soit complètement fermé
}

function deleteCategorie(id) {
    // Fermer le modal des catégories avant d'ouvrir SweetAlert2
    $('#modalCategories').modal('hide');
    
    // Attendre que le modal soit fermé avant d'ouvrir SweetAlert2
    setTimeout(() => {
        showConfirm('Confirmer la suppression', 'Êtes-vous sûr de vouloir supprimer cette catégorie ?', 'Oui, supprimer', 'Annuler')
        .then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'delete',
                    table: 'categorie',
                    id: id
                }).done(function(response) {
                    if (response.success) {
                        showAlert('Succès', response.message, 'success');
                        loadCategories();
                        // Rouvrir le modal des catégories après succès
                        setTimeout(() => {
                            $('#modalCategories').modal('show');
                        }, 1000);
                    } else {
                        showAlert('Erreur', response.message, 'danger');
                    }
                });
            }
        });
    }, 300); // Attendre 300ms pour que le modal soit complètement fermé
}

// ===== FONCTIONS DÉPÔTS =====
function loadDepots() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'depot'
    }).done(function(response) {
        if (response.success) {
            displayDepots(response.data);
        } else {
            showAlert('Erreur', response.message, 'danger');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement des dépôts:', error);
        showAlert('Erreur', 'Impossible de charger les dépôts', 'danger');
    });
}

function displayDepots(depots) {
    if (depotsTable) {
        depotsTable.clear();
        depots.forEach(depot => {
        depotsTable.row.add([
            depot.libelle,
            depot.est_actif ? '✔' : '✗',
            `<button class="btn btn-sm btn-warning btnEditDepot" data-id="${depot.id}" title="Modifier">
                <i class="fas fa-edit"></i>
            </button> ` +
            `<button class="btn btn-sm btn-danger btnDeleteDepot" data-id="${depot.id}" title="Supprimer">
                <i class="fas fa-trash"></i>
            </button>`
        ]);
        });
        depotsTable.draw();
    }
}

function addDepot() {
    // Fermer le modal des dépôts avant d'ouvrir SweetAlert2
    $('#modalDepots').modal('hide');
    
    // Attendre que le modal soit fermé avant d'ouvrir SweetAlert2
    setTimeout(() => {
        Swal.fire({
            title: 'Nouveau dépôt',
            input: 'text',
            inputLabel: 'Libellé du dépôt',
            inputPlaceholder: 'Entrez le libellé du dépôt',
            showCancelButton: true,
            confirmButtonText: 'Ajouter',
            cancelButtonText: 'Annuler',
            inputValidator: (value) => {
                if (!value) {
                    return 'Le libellé est requis !';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'create',
                    table: 'depot',
                    data: JSON.stringify({ libelle: result.value, est_actif: 1 })
                }).done(function(response) {
                    if (response.success) {
                        showAlert('Succès', response.message, 'success');
                        loadDepots();
                        // Rouvrir le modal des dépôts après succès
                        setTimeout(() => {
                            $('#modalDepots').modal('show');
                        }, 1000);
                    } else {
                        showAlert('Erreur', response.message, 'danger');
                    }
                });
            }
        });
    }, 300); // Attendre 300ms pour que le modal soit complètement fermé
}

function editDepot(id) {
    // Fermer le modal des dépôts avant d'ouvrir SweetAlert2
    $('#modalDepots').modal('hide');
    
    // Attendre que le modal soit fermé avant d'ouvrir SweetAlert2
    setTimeout(() => {
        Swal.fire({
            title: 'Modifier le dépôt',
            input: 'text',
            inputLabel: 'Nouveau libellé',
            inputPlaceholder: 'Entrez le nouveau libellé',
            showCancelButton: true,
            confirmButtonText: 'Modifier',
            cancelButtonText: 'Annuler',
            inputValidator: (value) => {
                if (!value) {
                    return 'Le libellé est requis !';
                }
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'update',
                    table: 'depot',
                    id: id,
                    data: JSON.stringify({ libelle: result.value })
                }).done(function(response) {
                    if (response.success) {
                        showAlert('Succès', response.message, 'success');
                        loadDepots();
                        // Rouvrir le modal des dépôts après succès
                        setTimeout(() => {
                            $('#modalDepots').modal('show');
                        }, 1000);
                    } else {
                        showAlert('Erreur', response.message, 'danger');
                    }
                });
            }
        });
    }, 300); // Attendre 300ms pour que le modal soit complètement fermé
}

function deleteDepot(id) {
    // Fermer le modal des dépôts avant d'ouvrir SweetAlert2
    $('#modalDepots').modal('hide');
    
    // Attendre que le modal soit fermé avant d'ouvrir SweetAlert2
    setTimeout(() => {
        showConfirm('Confirmer la suppression', 'Êtes-vous sûr de vouloir supprimer ce dépôt ?', 'Oui, supprimer', 'Annuler')
        .then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'delete',
                    table: 'depot',
                    id: id
                }).done(function(response) {
                    if (response.success) {
                        showAlert('Succès', response.message, 'success');
                        loadDepots();
                        // Rouvrir le modal des dépôts après succès
                        setTimeout(() => {
                            $('#modalDepots').modal('show');
                        }, 1000);
                    } else {
                        showAlert('Erreur', response.message, 'danger');
                    }
                });
            }
        });
    }, 300); // Attendre 300ms pour que le modal soit complètement fermé
}

// ===== FONCTIONS UTILITAIRES =====
let categoriesCache = {};
let unitesCache = {};

function getStockProduit(produitId) {
    // Cette fonction sera mise à jour pour utiliser les mouvements de stock réels
    // Pour l'instant, retourne une valeur de test
    return Math.floor(Math.random() * 100) + 10; // Valeur de test
}

function loadStockProduit(produitId) {
    // Charger le stock réel depuis la vue v_stock_actuel
    return $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                produit_id,
                depot_nom,
                stock_actuel,
                unite_stock,
                statut_stock
              FROM v_stock_actuel 
              WHERE produit_id = ?`,
        params: JSON.stringify([produitId])
    });
}

function getCategorieLibelle(categorieId) {
    if (categoriesCache[categorieId]) {
        return categoriesCache[categorieId];
    }
    return 'N/A';
}

function getUniteLibelle(uniteId) {
    if (unitesCache[uniteId]) {
        return unitesCache[uniteId];
    }
    return 'N/A';
}

function getPresentationLibelle(presentationId) {
    if (window.presentationsCache && window.presentationsCache[presentationId]) {
        return window.presentationsCache[presentationId];
    }
    return 'N/A';
}

function getFormeLibelle(formeId) {
    if (window.formesCache && window.formesCache[formeId]) {
        return window.formesCache[formeId];
    }
    return 'N/A';
}

// ===== FONCTIONS DE CONVERSION DE QUANTITÉS =====

/**
 * Crée une colonne compacte avec toutes les informations d'unités et de conversion
 * @param {object} produit - Objet produit avec toutes ses propriétés
 * @returns {string} HTML de la colonne compacte
 */
function createUnitesConversionColumn(produit) {
    const uniteVente = produit.unite_vente_libelle || 'N/A';
    const uniteAchat = produit.unite_achat_libelle || 'N/A';
    const uniteStock = produit.unite_stock_libelle || 'N/A';
    const presentation = produit.presentation_libelle || 'N/A';
    const qtePresentation = produit.qte_presentation || 1;
    const forme = produit.forme_libelle || 'N/A';
    const qteForme = produit.qte_forme || 67;
    
    let html = '<div class="">';
    
    // Unités de base
    html += `<div class="mb-1">
                <span class="badge bg-primary me-2">Vente: ${uniteVente}</span><br>
                <span class="badge bg-secondary me-2">Achat: ${uniteAchat}</span><br>
                <span class="badge bg-info me-2">Stock: ${uniteStock}</span><br>
            </div>`;
    
    // Conversion si les données sont disponibles
    if (presentation !== 'N/A' && forme !== 'N/A') {
        html += `<div class="text-muted">
                    <small>
                        
                         <span class="badge bg-success me-2">Present. ${qtePresentation}<strong>${presentation}</strong> → Forme :<strong>${qteForme}</strong>: ${forme}</span> 
                    </small>
                </div>`;
    }
    
    html += '</div>';
    
    return html;
}

/**
 * Convertit les kg fraîche en kg sec selon la règle : 3 kg fraîche → 1 kg sec
 * @param {number} kgFrais - Quantité en kg fraîche
 * @returns {number} Quantité en kg sec
 */
function convertFraisToSec(kgFrais) {
    return Math.round((kgFrais / 3) * 100) / 100; // Arrondi à 2 décimales
}

/**
 * Convertit les kg sec en kg fraîche selon la règle : 1 kg sec → 3 kg fraîche
 * @param {number} kgSec - Quantité en kg sec
 * @returns {number} Quantité en kg fraîche
 */
function convertSecToFrais(kgSec) {
    return Math.round((kgSec * 3) * 100) / 100; // Arrondi à 2 décimales
}

/**
 * Calcule le nombre de sacs nécessaires pour une quantité donnée
 * @param {number} kgSec - Quantité en kg sec
 * @param {number} kgParSac - Poids par sac (par défaut 65 kg)
 * @returns {object} {sacsComplets: number, reste: number, total: number}
 */
function calculateSacs(kgSec, kgParSac = 65) {
    const sacsComplets = Math.floor(kgSec / kgParSac);
    const reste = Math.round((kgSec % kgParSac) * 100) / 100;
    const total = Math.ceil(kgSec / kgParSac);
    
    return {
        sacsComplets: sacsComplets,
        reste: reste,
        total: total
    };
}

/**
 * Formate l'affichage des quantités avec conversion
 * @param {number} stockKg - Stock en kg (brut/fraîche)
 * @param {string} uniteStock - Unité de stock du produit
 * @returns {string} Affichage formaté
 */
function formatStockQuantity(stockKg, uniteStock, presentation = null, qtePresentation = 1, forme = null, qteForme = 67) {
    if (!stockKg || stockKg === 0) return `0 ${uniteStock || 'kg'}`;
    
    // Si l'unité de stock est KG et qu'on a des informations de présentation/forme
    if (uniteStock && uniteStock.toLowerCase().includes('kg') && presentation && forme) {
        // Appliquer la conversion selon la règle : 1 sac = 67 kg
        const kgParSac = qteForme || 67; // Quantité de forme (kg par sac)
        const sacs = Math.floor(stockKg / kgParSac);
        const resteKg = stockKg % kgParSac;
        
        let display = `${stockKg} kg`;
        if (sacs > 0) {
            display += ` (${sacs} sac`;
            if (sacs > 1) display += 's';
            if (resteKg > 0) {
                display += ` + ${resteKg.toFixed(2)} kg`;
            }
            display += ')';
        }
        return display;
    }
    
    // Si l'unité de stock est SAC, convertir en kg
    if (uniteStock && uniteStock.toLowerCase().includes('sac')) {
        const kgParSac = qteForme || 67;
        const totalKg = stockKg * kgParSac;
        return `${stockKg} sac (${totalKg} kg)`;
    }
    
    return `${stockKg} ${uniteStock || 'kg'}`;
}

/**
 * Calcule le poids net à partir du poids brut selon la règle 3 kg fraîche = 1 kg sec
 * @param {number} poidsBrut - Poids brut en kg fraîche
 * @returns {number} Poids net en kg sec
 */
function calculatePoidsNetFromBrut(poidsBrut) {
    return Math.round((poidsBrut / 3) * 100) / 100; // Arrondi à 2 décimales
}

/**
 * Calcule le poids brut à partir du nombre de sacs (1 sac = 67 kg)
 * @param {number} nbrSacs - Nombre de sacs
 * @returns {number} Poids brut en kg
 */
function calculatePoidsBrutFromSacs(nbrSacs) {
    return nbrSacs * 67;
}

// ===== FONCTIONS POUR L'HISTORIQUE DES MOUVEMENTS =====

function loadHistoriqueMouvements() {
    // Charger l'historique des mouvements pour l'affichage dans la colonne de droite
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                ms.id,
                p.nom as produit_nom,
                d.libelle as depot_nom,
                ms.type_mouvement,
                ms.quantite,
                u.libelle as unite_nom,
                ms.lot_numero,
                ms.cree_par,
                ms.date_mouvement,
                ms.motif
              FROM mouvements_stock ms
              LEFT JOIN produits p ON ms.produit_id = p.id
              LEFT JOIN depot d ON ms.depot_id = d.id
              LEFT JOIN unites u ON ms.unite_id = u.id
              ORDER BY ms.date_mouvement DESC 
              LIMIT 10`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            displayHistoriqueMouvements(response.data);
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement de l\'historique:', error);
    });
}

function displayHistoriqueMouvements(mouvements) {
    const tbody = $('.card:contains("Historique des mouvements") tbody');
    tbody.empty();
    
    if (mouvements.length === 0) {
        tbody.append('<tr><td colspan="5" class="text-center">Aucun mouvement récent</td></tr>');
        return;
    }
    
    mouvements.forEach(mouvement => {
        const typeIcon = mouvement.type_mouvement === 'ENTREE' ? 'fas fa-arrow-down' : 'fas fa-arrow-up';
        const typeClass = mouvement.type_mouvement === 'ENTREE' ? 'text-success' : 'text-danger';
        const quantiteFormatted = `${mouvement.quantite} ${mouvement.unite_nom || 'kg'}`;
        const dateFormatted = mouvement.date_mouvement ? mouvement.date_mouvement.split(' ')[0] : 'N/A';
        const produitNom = mouvement.produit_nom || 'Produit inconnu';
        const lotNumero = mouvement.lot_numero || 'N/A';
        
        tbody.append(`
            <tr>
                <td>${dateFormatted}</td>
                <td>
                    <strong>${produitNom}</strong><br/>
                    <small class="text-muted">${lotNumero}</small>
                </td>
                <td><span class="${typeClass}"><i class="${typeIcon}"></i> ${quantiteFormatted}</span></td>
                <td>${mouvement.depot_nom || 'N/A'}</td>
                <td>${mouvement.cree_par || 'N/A'}</td>
            </tr>
        `);
    });
}

// ===== FONCTIONNALITÉS DE SÉLECTION MULTIPLE =====

function setupSelectionHandlers() {
    // Gestion de la sélection multiple
    $(document).on('change', '#tableProduits input[type="checkbox"]', function() {
        updateSelectionButtons();
    });
    
    // Bouton cloner
    $('.btn:contains("Cloner")').on('click', function() {
        const selectedIds = getSelectedProductIds();
        if (selectedIds.length === 0) {
            showAlert('Erreur', 'Veuillez sélectionner au moins un produit à cloner', 'warning');
            return;
        }
        if (selectedIds.length > 1) {
            showAlert('Erreur', 'Veuillez sélectionner un seul produit à cloner', 'warning');
            return;
        }
        cloneProduit(selectedIds[0]);
    });
    
    // Bouton supprimer multiple
    $('.btn:contains("Supprimer")').on('click', function() {
        const selectedIds = getSelectedProductIds();
        if (selectedIds.length === 0) {
            showAlert('Erreur', 'Veuillez sélectionner au moins un produit à supprimer', 'warning');
            return;
        }
        deleteMultipleProduits(selectedIds);
    });
    
    // Bouton ajustement
    $('#btnSaveAjustement').on('click', function() {
        saveAjustement();
    });
}

function getSelectedProductIds() {
    const selectedIds = [];
    $('#tableProduits input[type="checkbox"]:checked').each(function() {
        const row = $(this).closest('tr');
        const id = row.find('button[data-id]').first().data('id');
        if (id) selectedIds.push(id);
    });
    return selectedIds;
}

function updateSelectionButtons() {
    const selectedCount = getSelectedProductIds().length;
    const cloneBtn = $('.btn:contains("Cloner")');
    const deleteBtn = $('.btn:contains("Supprimer")');
    
    if (selectedCount > 0) {
        cloneBtn.prop('disabled', false).removeClass('disabled');
        deleteBtn.prop('disabled', false).removeClass('disabled');
        
        if (selectedCount === 1) {
            cloneBtn.html('<i class="fas fa-copy"></i> Cloner');
        } else {
            cloneBtn.html('<i class="fas fa-copy"></i> Cloner (1 seul)');
        }
        
        deleteBtn.html(`<i class="fas fa-trash"></i> Supprimer (${selectedCount})`);
    } else {
        cloneBtn.prop('disabled', true).addClass('disabled');
        deleteBtn.prop('disabled', true).addClass('disabled');
        cloneBtn.html('<i class="fas fa-copy"></i> Cloner');
        deleteBtn.html('<i class="fas fa-trash"></i> Supprimer');
    }
}

function cloneProduit(id) {
    showConfirm('Cloner le produit', 'Voulez-vous cloner ce produit ?', 'Oui, cloner', 'Annuler')
        .then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'read',
                    table: 'produits',
                    data: JSON.stringify({ id: id })
                }).done(function(response) {
                    if (response.success && response.data.length > 0) {
                        const produit = response.data[0];
                        // Pré-remplir le formulaire avec les données du produit
                        $('#modalProduit input[placeholder="Nom"]').val(produit.nom + ' (Copie)');
                        $('#categorieSelect').val(produit.categorie_id);
                        $('#modalProduit select').eq(1).val(produit.type);
                        $('#modalProduit select').eq(2).val(produit.certification);
                        $('#modalProduit input[placeholder="Prix Vente"]').val(produit.prix_vente);
                        $('#modalProduit input[placeholder="Prix Achat"]').val(produit.prix_achat);
                        $('#modalProduit input[placeholder="Seuil Min"]').val(produit.stock_min);
                        $('#modalProduit input[placeholder="Seuil Max"]').val(produit.stock_max);
                        $('#uniteVenteSelect').val(produit.unite_vente_id);
                        $('#uniteAchatSelect').val(produit.unite_achat_id);
                        $('#uniteStockSelect').val(produit.unite_stock_id);
                        $('#presentationSelect').val(produit.presentation_id);
                        $('#formeSelect').val(produit.forme_id);
                        $('#qtePresentation').val(produit.qte_presentation);
                        $('#qteForme').val(produit.qte_forme);
                        
                        // Charger les selects si nécessaire
                        if ($('#categorieSelect option').length <= 1) { loadCategoriesInSelect(); }
                        if ($('#uniteVenteSelect option').length <= 1) { loadUnitesInSelects(); }
                        if ($('#presentationSelect option').length <= 1) { loadPresentationsInSelect(); }
                        if ($('#formeSelect option').length <= 1) { loadFormesInSelect(); }
                        
                        // Réinitialiser l'ID pour créer un nouveau produit
                        currentProduitId = null;
                        
                        $('#modalProduit').modal('show');
                        //showAlert('Succès', 'Produit chargé pour clonage', 'success');
                    } else {
                        showAlert('Erreur', 'Produit non trouvé', 'error');
                    }
                }).fail(function(xhr, status, error) {
                    console.error('Erreur lors du clonage:', error);
                    showAlert('Erreur', 'Impossible de charger le produit', 'error');
                });
            }
        });
}

function deleteMultipleProduits(ids) {
    showConfirm('Supprimer les produits', `Voulez-vous supprimer ${ids.length} produit(s) sélectionné(s) ?`, 'Oui, supprimer', 'Annuler')
        .then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'delete_bulk',
                    table: 'produits',
                    ids: JSON.stringify(ids)
                }).done(function(response) {
                    if (response.success) {
                        showAlert('Succès', response.message, 'success');
                        loadProduits();
                        updateSelectionButtons();
                    } else {
                        showAlert('Erreur', response.message, 'error');
                    }
                }).fail(function(xhr, status, error) {
                    console.error('Erreur lors de la suppression:', error);
                    showAlert('Erreur', 'Impossible de supprimer les produits', 'error');
                });
            }
        });
}

// ===== FONCTIONS D'AJUSTEMENT DE STOCK =====

function loadAjustementData() {
    // Charger les produits
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'produits'
    }).done(function(response) {
        if (response.success) {
            const select = $('#ajustementProduit');
            select.empty().append('<option value="">Sélectionner un produit</option>');
            response.data.forEach(produit => {
                select.append(`<option value="${produit.id}">${produit.nom}</option>`);
            });
        }
    });
    
    // Charger les dépôts
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'depot'
    }).done(function(response) {
        if (response.success) {
            const select = $('#ajustementDepot');
            select.empty().append('<option value="">Sélectionner un dépôt</option>');
            response.data.forEach(depot => {
                select.append(`<option value="${depot.id}">${depot.libelle}</option>`);
            });
        }
    });
    
    // Charger les unités
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'unites'
    }).done(function(response) {
        if (response.success) {
            const select = $('#ajustementUnite');
            select.empty().append('<option value="">Sélectionner une unité</option>');
            response.data.forEach(unite => {
                select.append(`<option value="${unite.id}">${unite.libelle}</option>`);
            });
        }
    });
}

function saveAjustement() {
    const produitId = $('#ajustementProduit').val();
    const depotId = $('#ajustementDepot').val();
    const type = $('#ajustementType').val();
    const quantite = parseFloat($('#ajustementQuantite').val());
    const uniteId = $('#ajustementUnite').val();
    const motif = $('#ajustementMotif').val();
    
    if (!produitId || !depotId || !type || !quantite || !uniteId) {
        showAlert('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');
        return;
    }
    
    const data = {
        produit_id: parseInt(produitId),
        depot_id: parseInt(depotId),
        type_mouvement: type,
        quantite: quantite,
        unite_id: parseInt(uniteId),
        source_type: 'AJUSTEMENT',
        source_reference: 'AJUST-' + Date.now(),
        motif: motif
    };
    
    $.post('../includes/traitement.php', {
        action: 'create',
        table: 'mouvements_stock',
        data: JSON.stringify(data)
    }).done(function(response) {
        if (response.success) {
            showAlert('Succès', 'Ajustement de stock enregistré', 'success');
            $('#modalAjustement').modal('hide');
            $('#formAjustement')[0].reset();
            loadProduits();
            loadHistoriqueMouvements();
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors de l\'ajustement:', error);
        showAlert('Erreur', 'Impossible d\'enregistrer l\'ajustement', 'error');
    });
}

function loadCategoriesCache() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'categorie'
    }).done(function(response) {
        if (response.success) {
            response.data.forEach(categorie => {
                categoriesCache[categorie.id] = categorie.libelle;
            });
            // Charger les catégories dans le select
            loadCategoriesInSelect();
        }
    });
}

function loadCategoriesInSelect() {
    const select = $('#categorieSelect');
    select.empty();
    select.append('<option value="">Sélectionner une catégorie</option>');
    
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'categorie'
    }).done(function(response) {
        if (response.success) {
            response.data.forEach(categorie => {
                select.append(`<option value="${categorie.id}">${categorie.libelle}</option>`);
            });
        }
    });
}

function loadUnitesInSelects() {
    const selects = ['#uniteVenteSelect', '#uniteAchatSelect', '#uniteStockSelect'];
    
    selects.forEach(selector => {
        const select = $(selector);
        select.empty();
        select.append('<option value="">Sélectionner une unité</option>');
    });
    
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'unites'
    }).done(function(response) {
        if (response.success) {
            response.data.forEach(unite => {
                selects.forEach(selector => {
                    $(selector).append(`<option value="${unite.id}">${unite.libelle}</option>`);
                });
            });
        }
    });
}

function loadUnitesCache() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'unites'
    }).done(function(response) {
        if (response.success) {
            response.data.forEach(unite => {
                unitesCache[unite.id] = unite.libelle;
            });
        }
    });
}

function loadPresentationsInSelect() {
    const select = $('#presentationSelect');
    select.empty();
    select.append('<option value="">Sélectionner une présentation</option>');
    
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'presentation'
    }).done(function(response) {
        if (response.success) {
            window.presentationsCache = {};
            response.data.forEach(presentation => {
                window.presentationsCache[presentation.id] = presentation.libelle;
                select.append(`<option value="${presentation.id}">${presentation.libelle}</option>`);
            });
        }
    });
}

function loadFormesInSelect() {
    const select = $('#formeSelect');
    select.empty();
    select.append('<option value="">Sélectionner une forme</option>');
    
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'forme'
    }).done(function(response) {
        if (response.success) {
            window.formesCache = {};
            response.data.forEach(forme => {
                window.formesCache[forme.id] = forme.libelle;
                select.append(`<option value="${forme.id}">${forme.libelle}</option>`);
            });
        }
    });
}

function showAlert(title, message, type) {
    // Utiliser SweetAlert2 au lieu des alertes Bootstrap
    const iconMap = {
        'success': 'success',
        'error': 'error',
        'warning': 'warning',
        'info': 'info',
        'danger': 'error'
    };
    
    Swal.fire({
        title: title,
        text: message,
        icon: iconMap[type] || 'info',
        confirmButtonText: 'OK',
        timer: type === 'success' ? 3000 : null,
        timerProgressBar: type === 'success'
    });
}

function showConfirm(title, text, confirmButtonText = 'Oui', cancelButtonText = 'Non') {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText
    });
}

-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1
-- <PERSON><PERSON><PERSON><PERSON> le : jeu. 02 oct. 2025 à 08:58
-- Version du serveur : 10.4.32-MariaDB
-- Version de PHP : 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `erp_cacao`
--

-- --------------------------------------------------------

--
-- Structure de la table `categorie`
--

CREATE TABLE `categorie` (
  `id` int(11) NOT NULL,
  `libelle` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `classement` enum('PRODUITS','FOURNITURES') NOT NULL DEFAULT 'PRODUITS',
  `est_actif` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1 pour actif, 0 pour inactif',
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `categorie`
--

INSERT INTO `categorie` (`id`, `libelle`, `description`, `classement`, `est_actif`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(2, 'MATIERE PREMIERE', NULL, 'PRODUITS', 1, '2025-02-01 07:39:43', NULL, '2025-10-02 01:27:16', NULL),
(3, 'BIO', NULL, 'PRODUITS', 1, '2025-03-17 21:23:44', NULL, '2025-10-02 01:27:16', NULL),
(4, 'CONVENTIONEL', NULL, 'PRODUITS', 1, '2025-03-17 21:23:53', NULL, '2025-10-02 01:27:16', NULL),
(5, 'BIO - FT', NULL, 'PRODUITS', 1, '2025-03-17 21:24:01', NULL, '2025-10-02 01:27:16', NULL),
(6, 'CONSOMABLE', NULL, 'FOURNITURES', 1, '2025-03-20 04:00:26', NULL, '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `clients`
--

CREATE TABLE `clients` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `adresse` text DEFAULT NULL,
  `telephone` varchar(30) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `nif` varchar(100) DEFAULT NULL,
  `stat` varchar(100) DEFAULT NULL,
  `type_client` enum('Export','Local') NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `clients`
--

INSERT INTO `clients` (`id`, `nom`, `adresse`, `telephone`, `email`, `nif`, `stat`, `type_client`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'SAL IBRAHIM Tanambao', 'Antsiranana', '032284687524', '<EMAIL>', NULL, NULL, 'Export', '2025-02-01 04:25:31', 'admin', '2025-10-02 01:27:16', 'admin'),
(2, 'Test', 'Test', '032000', '<EMAIL>', '079736', '5373773', 'Export', '2025-05-23 00:56:28', 'admin', '2025-10-02 01:27:16', NULL),
(3, 'Felchlin ', NULL, NULL, NULL, NULL, NULL, 'Export', '2025-05-23 07:53:30', NULL, '2025-10-02 01:27:16', NULL),
(4, 'Barry calbaut', 'Amsterdam', NULL, NULL, NULL, NULL, 'Export', '2025-06-27 07:06:34', NULL, '2025-10-02 01:27:16', NULL),
(5, 'RAYA Dayeuhkolot', 'Inde', NULL, NULL, NULL, NULL, 'Export', '2025-06-30 06:15:27', NULL, '2025-10-02 01:27:16', NULL),
(6, 'TRADIN', 'AMSTERDAM', NULL, NULL, NULL, NULL, 'Export', '2025-06-30 06:27:16', NULL, '2025-10-02 01:27:16', NULL),
(7, 'ICAM SPA', 'Inde', NULL, NULL, NULL, NULL, 'Export', '2025-06-30 06:54:04', NULL, '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `communes`
--

CREATE TABLE `communes` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `code_postal` varchar(10) DEFAULT NULL,
  `region` varchar(255) DEFAULT NULL,
  `pays` varchar(100) DEFAULT 'Madagascar'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `communes`
--

INSERT INTO `communes` (`id`, `nom`, `code_postal`, `region`, `pays`) VALUES
(1, 'ANTSAKOAMANONDRO', '206', 'DIANA', 'MADAGASCAR'),
(2, 'ANKATAFA VAOVAO', '206', 'DIANA', 'MADAGASCAR'),
(3, 'AMBODIVANIO', '206', 'DIANA', 'MADAGASCAR');

-- --------------------------------------------------------

--
-- Structure de la table `cooperatives`
--

CREATE TABLE `cooperatives` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `adresse` text DEFAULT NULL,
  `telephone` varchar(30) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `cooperatives`
--

INSERT INTO `cooperatives` (`id`, `nom`, `adresse`, `telephone`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'Coopérative Sambirano Mateza', '20', NULL, '2025-02-01 09:05:43', 'admin', '2025-10-02 01:27:16', 'admin'),
(2, 'Coopérative Bio Sambirano', '20', NULL, '2025-03-17 22:21:58', NULL, '2025-10-02 01:27:16', NULL),
(3, 'Coopérative Cacao et Vanille Sambirano', '20', NULL, '2025-03-25 08:57:06', NULL, '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `depot`
--

CREATE TABLE `depot` (
  `id` int(11) NOT NULL,
  `libelle` varchar(255) NOT NULL,
  `est_actif` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1 pour actif, 0 pour inactif'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `depot`
--

INSERT INTO `depot` (`id`, `libelle`, `est_actif`) VALUES
(1, 'MAGASIN N°01', 1),
(2, 'MAGASIN N°02', 1),
(3, 'MAGASIN N°03', 1);

-- --------------------------------------------------------

--
-- Structure de la table `forme`
--

CREATE TABLE `forme` (
  `id` int(11) NOT NULL,
  `libelle` varchar(100) NOT NULL,
  `famille_forme` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `forme`
--

INSERT INTO `forme` (`id`, `libelle`, `famille_forme`) VALUES
(79, 'KG', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `fournisseurs`
--

CREATE TABLE `fournisseurs` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `contact` varchar(30) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `adresse` text DEFAULT NULL,
  `nif` varchar(50) DEFAULT NULL,
  `stat` varchar(50) DEFAULT NULL,
  `est_actif` tinyint(1) NOT NULL DEFAULT 1,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par_id` int(11) DEFAULT NULL COMMENT 'Devrait être une clé étrangère vers une table `utilisateurs`',
  `date_derniere_modif` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `dernier_modif_par_id` int(11) DEFAULT NULL COMMENT 'Devrait être une clé étrangère vers une table `utilisateurs`'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `fournisseurs`
--

INSERT INTO `fournisseurs` (`id`, `nom`, `contact`, `email`, `adresse`, `nif`, `stat`, `est_actif`, `date_creation`, `cree_par_id`, `date_derniere_modif`, `dernier_modif_par_id`) VALUES
(1, 'HOUSSENI', '032', '<EMAIL>', 'AMBANJA', 'KI52639874526', '4152639875652', 1, '2025-03-20 00:00:00', 1, NULL, NULL),
(3, 'IMPORTER', '032', '<EMAIL>', 'CENTRE VILLE', 'UJ85969748585', '15975369369654', 1, '2025-03-20 00:00:00', 1, NULL, NULL),
(4, 'AKBAR ALY', '0342563989', '<EMAIL>', '41 Morarano', '41526398789', 'Stat4152636', 1, '0000-00-00 00:00:00', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `permissions`
--

CREATE TABLE `permissions` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `category` varchar(50) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `description`, `category`, `created_at`) VALUES
(1, 'menu.achats.view', 'Voir le menu Achats', 'Menu', '2025-09-28 06:48:13'),
(2, 'menu.stocks.view', 'Voir le menu Stocks', 'Menu', '2025-09-28 06:48:13'),
(3, 'menu.comptabilite.view', 'Voir le menu Comptabilité', 'Menu', '2025-09-28 06:48:13'),
(4, 'menu.utilisateurs.view', 'Voir le menu Utilisateurs', 'Menu', '2025-09-28 06:48:13'),
(5, 'menu.rapports.view', 'Voir le menu Rapports', 'Menu', '2025-09-28 06:48:13'),
(6, 'menu.parametres.view', 'Voir le menu Paramètres', 'Menu', '2025-09-28 06:48:13'),
(7, 'menu.lead_farmers.view', 'Voir le menu Lead Farmers', 'Menu', '2025-09-28 06:48:13'),
(8, 'menu.planteurs.view', 'Voir le menu Planteurs', 'Menu', '2025-09-28 06:48:13'),
(9, 'achats.create', 'Créer un achat', 'Achats', '2025-09-28 06:48:13'),
(10, 'achats.view', 'Voir les achats', 'Achats', '2025-09-28 06:48:13'),
(11, 'achats.update', 'Modifier un achat', 'Achats', '2025-09-28 06:48:13'),
(12, 'achats.validate', 'Valider un achat', 'Achats', '2025-09-28 06:48:13'),
(13, 'achats.pay', 'Marquer un achat comme payé', 'Achats', '2025-09-28 06:48:13'),
(14, 'achats.delete', 'Supprimer un achat', 'Achats', '2025-09-28 06:48:13'),
(15, 'achats.export', 'Exporter les achats', 'Achats', '2025-09-28 06:48:13'),
(16, 'achats.view_details', 'Voir les détails d\'un achat', 'Achats', '2025-09-28 06:48:13'),
(17, 'stocks.view', 'Voir les stocks', 'Stocks', '2025-09-28 06:48:13'),
(18, 'stocks.inventory', 'Effectuer un inventaire', 'Stocks', '2025-09-28 06:48:13'),
(19, 'stocks.adjustment', 'Faire un ajustement de stock', 'Stocks', '2025-09-28 06:48:13'),
(20, 'stocks.transfer', 'Effectuer un transfert', 'Stocks', '2025-09-28 06:48:13'),
(21, 'stocks.view_history', 'Voir l\'historique des mouvements', 'Stocks', '2025-09-28 06:48:13'),
(22, 'comptabilite.journal_caisse', 'Gérer le journal de caisse', 'Comptabilité', '2025-09-28 06:48:13'),
(23, 'comptabilite.paiement_achat', 'Gérer les paiements d\'achats', 'Comptabilité', '2025-09-28 06:48:13'),
(24, 'comptabilite.solde_initial', 'Gérer les soldes initiaux', 'Comptabilité', '2025-09-28 06:48:13'),
(25, 'comptabilite.rapports', 'Voir les rapports financiers', 'Comptabilité', '2025-09-28 06:48:13'),
(26, 'utilisateurs.view', 'Voir les utilisateurs', 'Utilisateurs', '2025-09-28 06:48:13'),
(27, 'utilisateurs.create', 'Créer un utilisateur', 'Utilisateurs', '2025-09-28 06:48:13'),
(28, 'utilisateurs.update', 'Modifier un utilisateur', 'Utilisateurs', '2025-09-28 06:48:13'),
(29, 'utilisateurs.delete', 'Supprimer un utilisateur', 'Utilisateurs', '2025-09-28 06:48:13'),
(30, 'utilisateurs.permissions', 'Gérer les permissions des utilisateurs', 'Utilisateurs', '2025-09-28 06:48:13'),
(31, 'utilisateurs.roles', 'Gérer les rôles', 'Utilisateurs', '2025-09-28 06:48:13'),
(32, 'lead_farmers.create', 'Créer un Lead Farmer', 'Lead Farmers', '2025-09-28 06:48:13'),
(33, 'lead_farmers.view', 'Voir les Lead Farmers', 'Lead Farmers', '2025-09-28 06:48:13'),
(34, 'lead_farmers.update', 'Modifier un Lead Farmer', 'Lead Farmers', '2025-09-28 06:48:13'),
(35, 'lead_farmers.delete', 'Supprimer un Lead Farmer', 'Lead Farmers', '2025-09-28 06:48:13'),
(36, 'lead_farmers.export', 'Exporter les Lead Farmers', 'Lead Farmers', '2025-09-28 06:48:13'),
(37, 'planteurs.create', 'Créer un Planteur', 'Planteurs', '2025-09-28 06:48:13'),
(38, 'planteurs.view', 'Voir les Planteurs', 'Planteurs', '2025-09-28 06:48:13'),
(39, 'planteurs.update', 'Modifier un Planteur', 'Planteurs', '2025-09-28 06:48:13'),
(40, 'planteurs.delete', 'Supprimer un Planteur', 'Planteurs', '2025-09-28 06:48:13'),
(41, 'planteurs.export', 'Exporter les Planteurs', 'Planteurs', '2025-09-28 06:48:13'),
(42, 'parametres.view', 'Voir les paramètres', 'Paramètres', '2025-09-28 06:48:13'),
(43, 'parametres.create', 'Créer des paramètres', 'Paramètres', '2025-09-28 06:48:13'),
(44, 'parametres.update', 'Modifier des paramètres', 'Paramètres', '2025-09-28 06:48:13'),
(45, 'parametres.delete', 'Supprimer des paramètres', 'Paramètres', '2025-09-28 06:48:13');

-- --------------------------------------------------------

--
-- Structure de la table `presentation`
--

CREATE TABLE `presentation` (
  `id` int(11) NOT NULL,
  `libelle` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `presentation`
--

INSERT INTO `presentation` (`id`, `libelle`) VALUES
(18, 'SAC');

-- --------------------------------------------------------

--
-- Structure de la table `producteurs`
--

CREATE TABLE `producteurs` (
  `id` int(11) NOT NULL,
  `code_producteur` varchar(50) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `code_leads` varchar(50) DEFAULT NULL,
  `genre` enum('M','F','A') DEFAULT NULL COMMENT 'Masculin, Féminin, Autre',
  `cin` varchar(50) DEFAULT NULL,
  `contact` varchar(255) DEFAULT NULL,
  `nif` varchar(100) DEFAULT NULL,
  `stat` varchar(100) DEFAULT NULL,
  `date_naissance` date DEFAULT NULL,
  `annee_adhesion_coop` year(4) DEFAULT NULL,
  `cotisation_droit_adhesion` decimal(12,2) DEFAULT NULL,
  `site` varchar(255) DEFAULT NULL,
  `classement` enum('LEAD FARMER','PETIT PLANTEUR','FREELANCE') NOT NULL DEFAULT 'PETIT PLANTEUR',
  `situation` varchar(255) DEFAULT NULL,
  `cree_par_id` int(11) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `producteurs`
--

INSERT INTO `producteurs` (`id`, `code_producteur`, `nom`, `code_leads`, `genre`, `cin`, `contact`, `nif`, `stat`, `date_naissance`, `annee_adhesion_coop`, `cotisation_droit_adhesion`, `site`, `classement`, `situation`, `cree_par_id`, `date_creation`, `date_derniere_modif`, `dernier_modif_par_id`) VALUES
(1, 'BS/ATSK/001', 'ABDOUL Sylvain JOMA', 'LF 01/BS/CSM/ATSK', 'M', '719011012960', '0346740932/0325504361', NULL, NULL, '1978-08-06', '2016', 29.00, '1', 'LEAD FARMER', '5', 0, '2025-01-01 00:00:00', '2025-10-02 01:27:16', 0),
(2, 'BS/ATSK/002', 'Aly Bruno', NULL, 'M', '719341004080', NULL, NULL, NULL, NULL, '2016', 29.00, '1', 'PETIT PLANTEUR', '5', 0, '2025-01-01 00:00:00', '2025-10-02 01:27:16', 0),
(3, 'BS/ATSK/003', 'Assany Tohalibo (1/3)', NULL, 'M', '719131002872', NULL, NULL, NULL, '1968-03-03', '2016', 29000.00, '1', 'PETIT PLANTEUR', '5', 0, '2025-01-01 00:00:00', '2025-10-02 01:27:16', 0),
(4, 'BS/ATSK/004', 'AUGUISTIN Sabotsy', NULL, 'M', '719111000127', NULL, NULL, NULL, NULL, '2016', 29.00, '1', 'PETIT PLANTEUR', '5', 0, '2025-01-01 00:00:00', '2025-10-02 01:27:16', 0);

-- --------------------------------------------------------

--
-- Structure de la table `production_globale`
--

CREATE TABLE `production_globale` (
  `id` int(11) NOT NULL,
  `producteur_id` int(11) NOT NULL,
  `annee` year(4) NOT NULL,
  `nombre_pieds` int(11) DEFAULT NULL,
  `surface_totale_production_ha` decimal(10,2) DEFAULT NULL,
  `volume_production_estimee_t` decimal(10,2) DEFAULT NULL,
  `cree_par_id` int(11) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `production_globale`
--

INSERT INTO `production_globale` (`id`, `producteur_id`, `annee`, `nombre_pieds`, `surface_totale_production_ha`, `volume_production_estimee_t`, `cree_par_id`, `date_creation`, `date_derniere_modif`, `dernier_modif_par_id`) VALUES
(1, 1, '2025', 550, 0.88, 1.00, 0, '2025-01-01 00:00:00', '2025-10-02 01:27:16', 0),
(2, 2, '2025', 500, 0.80, 1.00, 0, '2025-01-01 00:00:00', '2025-10-02 01:27:16', 0);

-- --------------------------------------------------------

--
-- Structure de la table `production_mensuelle`
--

CREATE TABLE `production_mensuelle` (
  `id` int(11) NOT NULL,
  `producteur_id` int(11) NOT NULL,
  `annee` year(4) NOT NULL,
  `mois` enum('JAN','FEV','MAR','AVR','MAI','JUIN','JUIL','AOUT','SEPT','OCT','NOV','DEC') NOT NULL,
  `type_production` enum('SEC','FRAICHE') NOT NULL,
  `volume_produit_kg` decimal(10,2) NOT NULL,
  `cree_par_id` int(11) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `produits`
--

CREATE TABLE `produits` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `type` enum('Standard','Superieur') NOT NULL,
  `certification` enum('Bio','Conventionnel') NOT NULL,
  `categorie_id` int(11) NOT NULL,
  `classe` enum('PRODUITS','FOURNITURES') NOT NULL,
  `unite_vente_id` int(11) NOT NULL,
  `unite_achat_id` int(11) NOT NULL,
  `unite_stock_id` int(11) NOT NULL,
  `presentation_id` int(11) DEFAULT NULL,
  `forme_id` int(11) DEFAULT NULL,
  `qte_presentation` int(11) DEFAULT 0,
  `qte_forme` int(11) DEFAULT 0,
  `prix_vente` decimal(12,2) NOT NULL,
  `marge_beneficiaire_pct` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '% par rapport au prix d''achat',
  `prix_achat` decimal(12,2) NOT NULL DEFAULT 0.00,
  `stock_min` int(11) NOT NULL DEFAULT 0,
  `stock_max` int(11) NOT NULL DEFAULT 0,
  `image_url` text DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `produits`
--

INSERT INTO `produits` (`id`, `nom`, `type`, `certification`, `categorie_id`, `classe`, `unite_vente_id`, `unite_achat_id`, `unite_stock_id`, `presentation_id`, `forme_id`, `qte_presentation`, `qte_forme`, `prix_vente`, `marge_beneficiaire_pct`, `prix_achat`, `stock_min`, `stock_max`, `image_url`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(5, 'CACAO BIO SUPERIEUR', 'Superieur', 'Bio', 3, 'PRODUITS', 2, 3, 2, NULL, NULL, 1, 67, 0.00, 0.00, 36000.00, 10000, 50000, NULL, '2025-03-17 21:27:37', NULL, '2025-10-02 01:27:16', NULL),
(6, 'CACAO BIO STANDARD', 'Standard', 'Bio', 5, 'PRODUITS', 2, 3, 2, NULL, NULL, 1, 67, 0.00, 0.00, 40000.00, 0, 0, NULL, '2025-03-17 21:57:35', NULL, '2025-10-02 01:27:16', NULL),
(7, 'CACAO CONVENTIONEL', 'Standard', 'Bio', 4, 'PRODUITS', 2, 3, 2, NULL, NULL, 1, 67, 0.00, 0.00, 38000.00, 0, 0, NULL, '2025-03-17 21:58:01', NULL, '2025-10-02 01:27:16', NULL),
(8, 'STYLO', 'Standard', 'Bio', 6, 'FOURNITURES', 8, 7, 8, NULL, NULL, 1, 50, 1000.00, 0.00, 15000.00, 30, 500, NULL, '2025-03-20 04:03:03', NULL, '2025-10-02 01:27:16', NULL),
(9, 'CAHIER', 'Standard', 'Bio', 6, 'FOURNITURES', 8, 7, 8, NULL, NULL, 1, 50, 1000.00, 0.00, 15000.00, 30, 500, NULL, '2025-03-20 07:07:07', NULL, '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `produits_stock`
--

CREATE TABLE `produits_stock` (
  `id` int(11) NOT NULL,
  `produit_id` int(11) NOT NULL,
  `depot_id` int(11) NOT NULL,
  `lot_numero` varchar(100) NOT NULL,
  `quantite` int(11) NOT NULL DEFAULT 0,
  `date_entree` datetime NOT NULL DEFAULT current_timestamp(),
  `date_expiration` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `roles`
--

CREATE TABLE `roles` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `roles`
--

INSERT INTO `roles` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES
(1, 'admin', 'Administrateur système - Accès complet', '2025-09-28 06:48:13', '2025-09-28 06:48:13'),
(2, 'denis', 'Denis - Gestion des achats et stocks', '2025-09-28 06:48:13', '2025-09-28 06:48:13'),
(3, 'joassin', 'Joassin - Validation des achats', '2025-09-28 06:48:13', '2025-09-28 06:48:13'),
(4, 'guy', 'Guy - Comptabilité et paiements', '2025-09-28 06:48:13', '2025-09-28 06:48:13'),
(5, 'manager', 'Manager', '2025-10-01 14:24:09', '2025-10-01 14:24:09'),
(6, 'vendeur', 'Vendeur', '2025-10-01 14:24:09', '2025-10-01 14:24:09'),
(7, 'caissier', 'Caissier', '2025-10-01 14:24:09', '2025-10-01 14:24:09'),
(8, 'production', 'Production', '2025-10-01 14:24:09', '2025-10-01 14:24:09'),
(9, 'utilisateur', 'Utilisateur standard', '2025-10-01 14:24:09', '2025-10-01 14:24:09');

-- --------------------------------------------------------

--
-- Structure de la table `role_permissions`
--

CREATE TABLE `role_permissions` (
  `role_id` int(11) NOT NULL,
  `permission` varchar(191) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Déchargement des données de la table `role_permissions`
--

INSERT INTO `role_permissions` (`role_id`, `permission`) VALUES
(1, '*'),
(5, 'clients.create'),
(5, 'clients.delete'),
(5, 'clients.export'),
(5, 'clients.update'),
(5, 'entree_detail.create'),
(5, 'entree_detail.delete'),
(5, 'entree_detail.update'),
(5, 'entree_entete.create'),
(5, 'entree_entete.delete'),
(5, 'entree_entete.pay'),
(5, 'entree_entete.update'),
(5, 'entree_entete.validate'),
(5, 'entree_entete.view'),
(5, 'menu.achats.view'),
(5, 'menu.clients.view'),
(5, 'menu.dashboard.view'),
(5, 'menu.produits.view'),
(5, 'menu.stocks.view'),
(5, 'mouvement_stock.create'),
(5, 'mouvement_stock.view'),
(5, 'page.achats_entete.view'),
(5, 'page.clients.view'),
(5, 'page.dashboard.view'),
(5, 'page.gestion_produits.view'),
(5, 'page.gestion_stock.view'),
(5, 'produit.create'),
(5, 'produit.delete'),
(5, 'produit.update'),
(5, 'produit.view'),
(5, 'stock_detail.adjustment'),
(5, 'stock_detail.view'),
(7, 'commandes.payer'),
(8, 'matiere.*'),
(8, 'production.*'),
(9, 'menu.dashboard.view'),
(9, 'page.dashboard.view');

-- --------------------------------------------------------

--
-- Structure de la table `unites`
--

CREATE TABLE `unites` (
  `id` int(11) NOT NULL,
  `libelle` varchar(100) NOT NULL,
  `est_actif` tinyint(1) NOT NULL DEFAULT 1,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `unites`
--

INSERT INTO `unites` (`id`, `libelle`, `est_actif`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'POUDRE', 1, '2025-02-01 07:41:35', NULL, '2025-10-02 01:27:16', NULL),
(2, 'SAC', 1, '2025-02-01 07:41:40', NULL, '2025-10-02 01:27:16', NULL),
(3, 'KG', 1, '2025-02-01 07:41:54', NULL, '2025-10-02 01:27:16', NULL),
(6, 'TONNE', 1, '2025-02-01 07:58:21', NULL, '2025-10-02 01:27:16', NULL),
(7, 'BOITE', 1, '2025-03-20 04:01:10', NULL, '2025-10-02 01:27:16', NULL),
(8, 'UNITE', 1, '2025-03-20 04:01:19', NULL, '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role_id` int(11) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `full_name`, `password`, `role_id`, `phone`, `status`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', 'Administrateur', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 1, NULL, 1, '2025-10-02 08:14:18', '2025-09-28 06:48:13', '2025-10-02 08:14:18'),
(2, 'denis', '<EMAIL>', 'Denis', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 2, NULL, 1, NULL, '2025-09-28 06:48:13', '2025-09-28 10:30:42'),
(3, 'joassin', '<EMAIL>', 'Joassin', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 3, NULL, 1, NULL, '2025-09-28 06:48:13', '2025-09-28 10:30:42'),
(4, 'guy', '<EMAIL>', 'Guy', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 4, NULL, 1, NULL, '2025-09-28 06:48:13', '2025-09-28 10:30:42');

-- --------------------------------------------------------

--
-- Structure de la table `user_permissions`
--

CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `ventes_details`
--

CREATE TABLE `ventes_details` (
  `id` int(11) NOT NULL,
  `vente_id` int(11) NOT NULL,
  `produit_id` int(11) DEFAULT NULL,
  `depot_id` int(11) DEFAULT NULL,
  `expedition` varchar(100) DEFAULT NULL,
  `grade` varchar(50) DEFAULT NULL,
  `qualite` varchar(100) DEFAULT NULL,
  `qte_tonnes` decimal(10,2) DEFAULT NULL,
  `qte_dernier_stock` decimal(10,2) DEFAULT NULL,
  `nbre_lot` int(11) DEFAULT NULL,
  `bl` varchar(100) DEFAULT NULL,
  `conteneur` varchar(100) DEFAULT NULL,
  `seal` varchar(100) DEFAULT NULL,
  `lots` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `ventes_details`
--

INSERT INTO `ventes_details` (`id`, `vente_id`, `produit_id`, `depot_id`, `expedition`, `grade`, `qualite`, `qte_tonnes`, `qte_dernier_stock`, `nbre_lot`, `bl`, `conteneur`, `seal`, `lots`) VALUES
(1, 3, 7, 1, '07/01/25', 'Standard', 'Conventionnel', 25.00, NULL, 2, '0JM3DR1MA/TNR0158073', 'TCNU5657354', 'C718538', '145/24'),
(2, 4, 7, 2, '07/01/25', 'Standard', 'Conventionnel', 25.00, NULL, 2, '0JM3DR1MA/TNR0158073', 'ECMJ4908164', 'C7185137', '146/24'),
(3, 5, 6, 3, '08/01/25', 'Standard', 'Bio', 25.00, NULL, 2, NULL, NULL, NULL, '147/25'),
(4, 6, 6, 3, '08/01/25', 'Standard', 'Bio', 25.00, NULL, 2, NULL, NULL, NULL, '148/24'),
(5, 7, 7, 2, '30/01/25', 'Standard', 'Conventionnel', NULL, NULL, NULL, '0JM3ER1MA/TNR0158461', 'CMAU7566308', 'C7185165', '012/24'),
(6, 8, 7, 2, '30/01/25', 'Standard', 'Conventionnel', NULL, NULL, NULL, '0JM3ER1MA/TNR0158461', 'CMAU7566308', 'C7185165', '014/24'),
(7, 9, 7, 2, '30/1/25', 'Standard', 'Conventionnel', NULL, NULL, 1, '0JM3R1MA/TNR0158461', 'TGHU6066574', 'C7185166', '015/24'),
(8, 10, 7, 2, '30/1/25', 'Standard', 'Conventionnel', NULL, NULL, 2, '0JM3R1MA/TNR0158461', 'TGHU6066574', 'C7185166', '016/24'),
(9, 11, 5, 3, '31/01/25', 'Superieur', 'Bio', 13.00, NULL, 1, NULL, NULL, NULL, '013/24'),
(10, 12, NULL, 3, '01/2/25', 'Premium', 'Bio', 25.00, NULL, 4, '0JM3ER1MA/TNR0158478', 'CMAU485447', 'C7185163', '50/24'),
(11, 13, 6, 2, '17/3/25', 'Standard', 'Bio', 25.00, NULL, 2, '0JM3GR1MA/TNR0158978', 'CAIU7051953', 'C7185218', '100/25'),
(12, 14, 6, 2, '17/3/25', 'Standard', 'Bio', 25.00, NULL, 2, '0JM3GR1MA/TNR0158978', 'CMAU6759956', 'C7185219', '101/25');

-- --------------------------------------------------------

--
-- Structure de la table `ventes_entete`
--

CREATE TABLE `ventes_entete` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `n_domiciliation` varchar(255) DEFAULT NULL,
  `total_montant` decimal(18,2) DEFAULT NULL,
  `total_remise` decimal(18,2) DEFAULT NULL,
  `date_vente` date DEFAULT NULL,
  `statut` enum('Annuler','Encours','Facturer','En attente') NOT NULL DEFAULT 'Encours',
  `valeur_euro` decimal(18,2) DEFAULT NULL,
  `valeur_ar` decimal(18,2) DEFAULT NULL,
  `cours_devise` decimal(10,4) DEFAULT NULL,
  `dau_numero` varchar(100) DEFAULT NULL,
  `dau_date` date DEFAULT NULL,
  `facture_numero` varchar(100) DEFAULT NULL,
  `facture_date` date DEFAULT NULL,
  `lieux_exportation` varchar(255) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `ventes_entete`
--

INSERT INTO `ventes_entete` (`id`, `client_id`, `n_domiciliation`, `total_montant`, `total_remise`, `date_vente`, `statut`, `valeur_euro`, `valeur_ar`, `cours_devise`, `dau_numero`, `dau_date`, `facture_numero`, `facture_date`, `lieux_exportation`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(3, 5, '080000824 E 08076', 1.00, NULL, NULL, 'Facturer', 210487.20, 1026209294.88, 4875.4000, 'E6', '2025-01-09', '19/24', '2024-12-18', 'Nosy be', '2025-06-30 01:12:23', '1', '2025-10-02 01:27:16', NULL),
(4, 5, '080000924 E 08076', NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E6', '2025-01-09', '019/24', '2024-12-18', NULL, '2025-06-30 01:18:54', '1', '2025-10-02 01:27:16', NULL),
(5, 6, NULL, 180.00, NULL, NULL, 'Facturer', 180417.60, 879607967.04, 4875.4000, 'E7', '2025-01-09', NULL, NULL, NULL, '2025-06-30 01:22:26', '1', '2025-10-02 01:27:16', NULL),
(6, 6, NULL, NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E7', '2025-01-09', NULL, NULL, 'Nosy be', '2025-06-30 01:27:25', '1', '2025-10-02 01:27:16', NULL),
(7, 4, '080000925 E 00615', 817.00, NULL, NULL, 'Facturer', 170394.40, 817898231.83, 4800.0300, 'E25', '2025-02-05', '004/25', '2025-01-30', 'Nosy be', '2025-06-30 01:30:52', '1', '2025-10-02 01:27:16', NULL),
(8, 4, '080000925 E 00615', NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E25', '2025-02-05', '004/25', '2025-01-30', 'Nosy be', '2025-06-30 01:35:21', '1', '2025-10-02 01:27:16', NULL),
(9, 4, '080000925 E 00615', NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E25', '2025-02-05', '004/25', '2025-01-30', 'Nosy be', '2025-06-30 01:38:35', '1', '2025-10-02 01:27:16', NULL),
(10, 4, '080000925 E 00615', NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E25', '2025-02-05', '004/25', '2025-01-30', 'Nosy be', '2025-06-30 01:42:23', '1', '2025-10-02 01:27:16', NULL),
(11, 3, '080000925 E 00613', 47.00, NULL, NULL, 'Facturer', 47034.00, 225763200.00, 4800.0000, 'E23', '2025-02-05', '002/25', '2025-01-30', 'Nosy be', '2025-06-30 01:48:43', '1', '2025-10-02 01:27:16', NULL),
(12, 7, '080000925 E 00614', 433.00, NULL, NULL, 'Facturer', 90208.80, 433004946.26, 4800.0300, 'E24', '2025-02-05', '003/25', '2025-01-30', 'Nosy be', '2025-06-30 01:51:42', '1', '2025-10-02 01:27:16', NULL),
(13, 6, '080000925 E 01774', 901.00, NULL, NULL, 'Facturer', 180417.60, 901247253.98, 4995.3400, 'E57', '2025-03-24', '005/25', '2025-03-19', 'Nosy be', '2025-06-30 01:55:42', '1', '2025-10-02 01:27:16', NULL),
(14, 6, '080000925 E 01774', NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E57', '2025-03-24', '005/25', '2025-03-19', 'Nosy be', '2025-06-30 01:59:04', '1', '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

-- =================================================================
-- MISE À JOUR DES STRUCTURES D'ACHAT (Version 2, basée sur l'image)
-- =================================================================

--
-- Structure de la table `achat_entete` (Mise à jour)
--
DROP TABLE IF EXISTS `achat_detail`;
DROP TABLE IF EXISTS `achat_entete`;

CREATE TABLE `achat_entete` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `reference_achat` VARCHAR(100) NOT NULL UNIQUE COMMENT 'Ex: ACH-2025-0001',
  `fournisseur_id` INT NOT NULL COMMENT 'ID du Lead Farmer (table `producteurs`)',
  `date_achat` DATE DEFAULT NULL,
  `date_livraison` DATE DEFAULT NULL COMMENT 'Nouveau champ "Livré le"',
  `frais_transport` DECIMAL(12,2) DEFAULT 0.00 COMMENT 'Nouveau champ "Transport"',
  `statut` ENUM('SAISIE', 'LIVRE', 'CONTROLE', 'A_PAYER', 'PAYE', 'ANNULE') NOT NULL DEFAULT 'SAISIE' COMMENT 'Statut "LIVRE" ajouté',
  
  `date_creation` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP(),
  `cree_par` VARCHAR(255) DEFAULT NULL,
  `date_derniere_modif` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP(),
  `dernier_modif_par` VARCHAR(255) DEFAULT NULL,
  
  FOREIGN KEY (`fournisseur_id`) REFERENCES `producteurs`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Structure de la table `achat_detail` (Mise à jour)
--
CREATE TABLE `achat_detail` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `achat_entete_id` INT NOT NULL,
  `produit_id` INT NOT NULL,
  `lot_id` INT DEFAULT NULL COMMENT 'Référence au lot spécifique de ce produit',
  `petit_planteur_id` INT DEFAULT NULL COMMENT 'TRÈS IMPORTANT: ID du paysan source (table `producteurs`)',
  `depot_id` INT NOT NULL,

  -- Quantités et Unités
  `unite_achat_id` INT NOT NULL,
  `qte_brute_saisie` DECIMAL(10,2) NOT NULL,
  `qte_nette_controlee` DECIMAL(10,2) DEFAULT NULL,
  `ecart_controle` DECIMAL(10,2) GENERATED ALWAYS AS (`qte_brute_saisie` - `qte_nette_controlee`) STORED COMMENT 'Calculé automatiquement',
  `nombre_sacs` INT DEFAULT NULL,
  
  -- Suivi de stock
  `stock_avant_entree` DECIMAL(10,2) NOT NULL,

  -- Informations financières
  `prix_unitaire_net` DECIMAL(12,2) DEFAULT 0.00,
  `reduction` DECIMAL(12,2) DEFAULT 0.00,
  `montant_ht` DECIMAL(18,2) DEFAULT 0.00 COMMENT 'Calculé: (qte_nette * prix_unitaire) - reduction',
  `montant_ttc` DECIMAL(18,2) DEFAULT 0.00 COMMENT 'Montant final taxe incluse',

  `date_creation` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP(),
  `cree_par` VARCHAR(255) DEFAULT NULL,
  `date_derniere_modif` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP(),
  `dernier_modif_par` VARCHAR(255) DEFAULT NULL,
  
  FOREIGN KEY (`achat_entete_id`) REFERENCES `achat_entete`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`),
  FOREIGN KEY (`lot_id`) REFERENCES `lots`(`id`),
  FOREIGN KEY (`petit_planteur_id`) REFERENCES `producteurs`(`id`),
  FOREIGN KEY (`depot_id`) REFERENCES `depot`(`id`),
  FOREIGN KEY (`unite_achat_id`) REFERENCES `unites`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


--
-- Doublure de structure pour la vue `v_role_permissions`
-- (Voir ci-dessous la vue réelle)
--
CREATE TABLE `v_role_permissions` (
);

-- --------------------------------------------------------

--
-- Structure de la vue `v_role_permissions`
--
DROP TABLE IF EXISTS `v_role_permissions`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `v_role_permissions`  AS SELECT `r`.`id` AS `role_id`, `r`.`name` AS `role_name`, `p`.`id` AS `permission_id`, `p`.`name` AS `permission_name`, `p`.`description` AS `permission_description`, `p`.`category` AS `category` FROM ((`roles` `r` left join `role_permissions` `rp` on(`r`.`id` = `rp`.`role_id`)) left join `permissions` `p` on(`rp`.`permission_id` = `p`.`id`)) ;

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `categorie`
--
ALTER TABLE `categorie`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `libelle` (`libelle`);

--
-- Index pour la table `clients`
--
ALTER TABLE `clients`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `nif` (`nif`),
  ADD UNIQUE KEY `stat` (`stat`);

--
-- Index pour la table `communes`
--
ALTER TABLE `communes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_nom_commune` (`nom`),
  ADD KEY `idx_region` (`region`);

--
-- Index pour la table `cooperatives`
--
ALTER TABLE `cooperatives`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Index pour la table `depot`
--
ALTER TABLE `depot`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `libelle` (`libelle`);

--
-- Index pour la table `forme`
--
ALTER TABLE `forme`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `libelle` (`libelle`);

--
-- Index pour la table `fournisseurs`
--
ALTER TABLE `fournisseurs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `nif` (`nif`),
  ADD UNIQUE KEY `stat` (`stat`);

--
-- Index pour la table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Index pour la table `presentation`
--
ALTER TABLE `presentation`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `libelle` (`libelle`);

--
-- Index pour la table `producteurs`
--
ALTER TABLE `producteurs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code_producteur` (`code_producteur`),
  ADD UNIQUE KEY `cin` (`cin`);

--
-- Index pour la table `production_globale`
--
ALTER TABLE `production_globale`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_prod_unique_annuelle` (`producteur_id`,`annee`);

--
-- Index pour la table `production_mensuelle`
--
ALTER TABLE `production_mensuelle`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_prod_unique_mensuelle` (`producteur_id`,`annee`,`mois`,`type_production`);

--
-- Index pour la table `produits`
--
ALTER TABLE `produits`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_nom_produit` (`nom`),
  ADD KEY `fk_produits_categorie` (`categorie_id`),
  ADD KEY `fk_produits_unite_vente` (`unite_vente_id`),
  ADD KEY `fk_produits_unite_achat` (`unite_achat_id`),
  ADD KEY `fk_produits_unite_stock` (`unite_stock_id`),
  ADD KEY `fk_produits_presentation` (`presentation_id`),
  ADD KEY `fk_produits_forme` (`forme_id`);

--
-- Index pour la table `produits_stock`
--
ALTER TABLE `produits_stock`
  ADD PRIMARY KEY (`id`),
  ADD KEY `produit_id` (`produit_id`),
  ADD KEY `depot_id` (`depot_id`);

--
-- Index pour la table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Index pour la table `role_permissions`
--
ALTER TABLE `role_permissions`
  ADD PRIMARY KEY (`role_id`,`permission`),
  ADD KEY `permission` (`permission`);

--
-- Index pour la table `unites`
--
ALTER TABLE `unites`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `libelle` (`libelle`);

--
-- Index pour la table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `role_id` (`role_id`);

--
-- Index pour la table `user_permissions`
--
ALTER TABLE `user_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_permission` (`user_id`,`permission_id`),
  ADD KEY `permission_id` (`permission_id`);

--
-- Index pour la table `ventes_details`
--
ALTER TABLE `ventes_details`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_details_vente` (`vente_id`),
  ADD KEY `fk_details_produit` (`produit_id`),
  ADD KEY `fk_details_depot` (`depot_id`);

--
-- Index pour la table `ventes_entete`
--
ALTER TABLE `ventes_entete`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_client` (`client_id`),
  ADD KEY `idx_date_vente` (`date_vente`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `categorie`
--
ALTER TABLE `categorie`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT pour la table `clients`
--
ALTER TABLE `clients`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT pour la table `communes`
--
ALTER TABLE `communes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `cooperatives`
--
ALTER TABLE `cooperatives`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `depot`
--
ALTER TABLE `depot`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `forme`
--
ALTER TABLE `forme`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=80;

--
-- AUTO_INCREMENT pour la table `fournisseurs`
--
ALTER TABLE `fournisseurs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=46;

--
-- AUTO_INCREMENT pour la table `presentation`
--
ALTER TABLE `presentation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT pour la table `producteurs`
--
ALTER TABLE `producteurs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `production_globale`
--
ALTER TABLE `production_globale`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `production_mensuelle`
--
ALTER TABLE `production_mensuelle`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `produits`
--
ALTER TABLE `produits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT pour la table `produits_stock`
--
ALTER TABLE `produits_stock`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT pour la table `unites`
--
ALTER TABLE `unites`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT pour la table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `user_permissions`
--
ALTER TABLE `user_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `ventes_details`
--
ALTER TABLE `ventes_details`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT pour la table `ventes_entete`
--
ALTER TABLE `ventes_entete`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `production_globale`
--
ALTER TABLE `production_globale`
  ADD CONSTRAINT `fk_prod_globale_producteur` FOREIGN KEY (`producteur_id`) REFERENCES `producteurs` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `production_mensuelle`
--
ALTER TABLE `production_mensuelle`
  ADD CONSTRAINT `fk_prod_mensuelle_producteur` FOREIGN KEY (`producteur_id`) REFERENCES `producteurs` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `produits`
--
ALTER TABLE `produits`
  ADD CONSTRAINT `fk_produits_categorie` FOREIGN KEY (`categorie_id`) REFERENCES `categorie` (`id`),
  ADD CONSTRAINT `fk_produits_forme` FOREIGN KEY (`forme_id`) REFERENCES `forme` (`id`),
  ADD CONSTRAINT `fk_produits_presentation` FOREIGN KEY (`presentation_id`) REFERENCES `presentation` (`id`),
  ADD CONSTRAINT `fk_produits_unite_achat` FOREIGN KEY (`unite_achat_id`) REFERENCES `unites` (`id`),
  ADD CONSTRAINT `fk_produits_unite_stock` FOREIGN KEY (`unite_stock_id`) REFERENCES `unites` (`id`),
  ADD CONSTRAINT `fk_produits_unite_vente` FOREIGN KEY (`unite_vente_id`) REFERENCES `unites` (`id`);

--
-- Contraintes pour la table `produits_stock`
--
ALTER TABLE `produits_stock`
  ADD CONSTRAINT `produits_stock_ibfk_1` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`),
  ADD CONSTRAINT `produits_stock_ibfk_2` FOREIGN KEY (`depot_id`) REFERENCES `depot` (`id`);

--
-- Contraintes pour la table `role_permissions`
--
ALTER TABLE `role_permissions`
  ADD CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`);

--
-- Contraintes pour la table `user_permissions`
--
ALTER TABLE `user_permissions`
  ADD CONSTRAINT `user_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `ventes_details`
--
ALTER TABLE `ventes_details`
  ADD CONSTRAINT `fk_details_depot` FOREIGN KEY (`depot_id`) REFERENCES `depot` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_details_produit` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_details_vente` FOREIGN KEY (`vente_id`) REFERENCES `ventes_entete` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `ventes_entete`
--
ALTER TABLE `ventes_entete`
  ADD CONSTRAINT `fk_ventes_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

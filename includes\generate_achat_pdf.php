<?php
/**
 * Génération de PDF pour les achats
 */

session_start();
require_once  '../config/database.php';
require_once '../vendor/autoload.php';

// Vérification de l'authentification
if (!isset($_SESSION['username'])) {
    die('Accès non autorisé');
}

$achatId = $_GET['id'] ?? null;

if (!$achatId) {
    die('ID d\'achat manquant');
}

try {
    $db = Database::getInstance()->getConnection();
    
    // Récupérer les données de l'achat
    $sql = "SELECT 
                ae.*,
                p.nom as fournisseur_nom,
                p.contact as fournisseur_contact,
                p.site as fournisseur_site
            FROM achat_entete ae
            LEFT JOIN producteurs p ON ae.fournisseur_id = p.id
            WHERE ae.id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$achatId]);
    $achat = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$achat) {
        die('Achat non trouvé');
    }
    
    // Récupérer les détails de l'achat
    $sqlDetails = "SELECT 
                    ad.*,
                    pr.nom as produit_nom,
                    u.libelle as unite_libelle
                  FROM achat_detail ad
                  LEFT JOIN produits pr ON ad.produit_id = pr.id
                  LEFT JOIN unites u ON ad.unite_achat_id = u.id
                  WHERE ad.achat_entete_id = ?
                  ORDER BY ad.id";
    
    $stmtDetails = $db->prepare($sqlDetails);
    $stmtDetails->execute([$achatId]);
    $details = $stmtDetails->fetchAll(PDO::FETCH_ASSOC);
    
    // Créer le PDF
    $mpdf = new \Mpdf\Mpdf([
        'mode' => 'utf-8',
        'format' => 'A4',
        'orientation' => 'P',
        'margin_left' => 15,
        'margin_right' => 15,
        'margin_top' => 16,
        'margin_bottom' => 16,
        'margin_header' => 9,
        'margin_footer' => 9
    ]);
    
    // En-tête du PDF
    $html = '
    <style>
        body { font-family: Arial, sans-serif; font-size: 12px; }
        .header { text-align: center; margin-bottom: 20px; }
        .header h1 { color: #2c3e50; margin: 0; }
        .header h2 { color: #7f8c8d; margin: 5px 0; }
        .info-section { margin-bottom: 20px; }
        .info-section h3 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
        .info-table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
        .info-table td { padding: 5px; border: 1px solid #ddd; }
        .info-table .label { background-color: #f8f9fa; font-weight: bold; width: 30%; }
        .details-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .details-table th, .details-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        .details-table th { background-color: #3498db; color: white; }
        .details-table tr:nth-child(even) { background-color: #f8f9fa; }
        .total-section { margin-top: 20px; text-align: right; }
        .total-section .total-line { margin: 5px 0; }
        .total-section .grand-total { font-weight: bold; font-size: 14px; color: #2c3e50; }
        .status-badge { padding: 4px 8px; border-radius: 4px; color: white; font-weight: bold; }
        .status-saisie { background-color: #6c757d; }
        .status-livre { background-color: #17a2b8; }
        .status-controle { background-color: #ffc107; color: #000; }
        .status-a_payer { background-color: #dc3545; }
        .status-paye { background-color: #28a745; }
        .status-annule { background-color: #343a40; }
    </style>
    
    <div class="header">
        <h1>BON D\'ACHAT</h1>
        <h2>Référence: ' . htmlspecialchars($achat['reference_achat']) . '</h2>
    </div>
    
    <div class="info-section">
        <h3>Informations Générales</h3>
        <table class="info-table">
            <tr>
                <td class="label">Référence:</td>
                <td>' . htmlspecialchars($achat['reference_achat']) . '</td>
                <td class="label">Date d\'achat:</td>
                <td>' . htmlspecialchars($achat['date_achat']) . '</td>
            </tr>
            <tr>
                <td class="label">Fournisseur:</td>
                <td>' . htmlspecialchars($achat['fournisseur_nom']) . '</td>
                <td class="label">Date de livraison:</td>
                <td>' . htmlspecialchars($achat['date_livraison']) . '</td>
            </tr>
            <tr>
                <td class="label">Contact:</td>
                <td>' . htmlspecialchars($achat['fournisseur_contact']) . '</td>
                <td class="label">Statut:</td>
                <td><span class="status-badge status-' . strtolower($achat['statut']) . '">' . htmlspecialchars($achat['statut']) . '</span></td>
            </tr>
            <tr>
                <td class="label">Site:</td>
                <td>' . htmlspecialchars($achat['fournisseur_site']) . '</td>
                <td class="label">Frais de transport:</td>
                <td>' . number_format($achat['frais_transport'], 0, ',', ' ') . ' Ar</td>
            </tr>
        </table>
    </div>
    
    <div class="info-section">
        <h3>Détails des Produits</h3>
        <table class="details-table">
            <thead>
                <tr>
                    <th>Produit</th>
                    <th>N° Lot</th>
                    <th>Poids Net (kg)</th>
                    <th>Poids Brut (kg)</th>
                    <th>Nbr. Sacs</th>
                    <th>Écart (kg)</th>
                    <th>Prix Unitaire (Ar)</th>
                    <th>Réduction (Ar)</th>
                    <th>Montant HT (Ar)</th>
                </tr>
            </thead>
            <tbody>';
    
    $montantTotal = 0;
    foreach ($details as $detail) {
        $montantHT = $detail['montant_ht'] ?? 0;
        $montantTotal += $montantHT;
        
        $html .= '
            <tr>
                <td>' . htmlspecialchars($detail['produit_nom']) . '</td>
                <td>' . htmlspecialchars($detail['lot_numero']) . '</td>
                <td>' . number_format($detail['qte_nette_controlee'], 2, ',', ' ') . '</td>
                <td>' . number_format($detail['qte_brute_saisie'], 2, ',', ' ') . '</td>
                <td>' . number_format($detail['nombre_sacs'], 2, ',', ' ') . '</td>
                <td>' . number_format($detail['ecart_controle'], 2, ',', ' ') . '</td>
                <td>' . number_format($detail['prix_unitaire_net'], 0, ',', ' ') . '</td>
                <td>' . number_format($detail['reduction'], 0, ',', ' ') . '</td>
                <td>' . number_format($montantHT, 0, ',', ' ') . '</td>
            </tr>';
    }
    
    $html .= '
            </tbody>
        </table>
    </div>
    
    <div class="total-section">
        <div class="total-line">Frais de transport: ' . number_format($achat['frais_transport'], 0, ',', ' ') . ' Ar</div>
        <div class="total-line">Montant HT: ' . number_format($montantTotal, 0, ',', ' ') . ' Ar</div>
        <div class="total-line grand-total">Total TTC: ' . number_format($montantTotal + $achat['frais_transport'], 0, ',', ' ') . ' Ar</div>
    </div>
    
    <div style="margin-top: 30px; font-size: 10px; color: #7f8c8d;">
        <p>Document généré le ' . date('d/m/Y à H:i') . ' par ' . htmlspecialchars($_SESSION['username']) . '</p>
        <p>Créé le: ' . htmlspecialchars($achat['date_creation']) . ' | Modifié le: ' . htmlspecialchars($achat['date_derniere_modif']) . '</p>
    </div>';
    
    $mpdf->WriteHTML($html);
    
    // Nom du fichier
    $filename = 'Achat_' . $achat['reference_achat'] . '_' . date('Y-m-d') . '.pdf';
    
    // Envoyer le PDF au navigateur
    $mpdf->Output($filename, 'I');
    
} catch (Exception $e) {
    die('Erreur lors de la génération du PDF: ' . $e->getMessage());
}
?>

-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1
-- <PERSON><PERSON><PERSON><PERSON> le : lun. 06 oct. 2025 à 11:16
-- Version du serveur : 10.4.32-MariaDB
-- Version de PHP : 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `erp_cacao`
--

-- --------------------------------------------------------

--
-- Structure de la table `achat_detail`
--

CREATE TABLE `achat_detail` (
  `id` int(11) NOT NULL,
  `achat_entete_id` int(11) NOT NULL,
  `produit_id` int(11) NOT NULL,
  `depot_id` int(11) DEFAULT NULL,
  `petit_planteur_id` int(11) DEFAULT NULL,
  `producteurs_json` text DEFAULT NULL COMMENT 'Données des petits planteurs avec quantités au format JSON',
  `unite_achat_id` int(11) NOT NULL,
  `qte_brute_saisie` decimal(10,2) DEFAULT 0.00,
  `qte_nette_controlee` decimal(10,2) DEFAULT 0.00,
  `prix_unitaire_net` decimal(12,2) DEFAULT 0.00,
  `montant_ht` decimal(12,2) DEFAULT 0.00,
  `reduction` decimal(12,2) DEFAULT 0.00,
  `stock_avant_entree` decimal(10,2) DEFAULT 0.00,
  `lot_numero` varchar(100) DEFAULT NULL,
  `nombre_sacs` decimal(10,2) DEFAULT 0.00,
  `ecart_controle` decimal(10,2) DEFAULT 0.00,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `achat_detail`
--

INSERT INTO `achat_detail` (`id`, `achat_entete_id`, `produit_id`, `depot_id`, `petit_planteur_id`, `producteurs_json`, `unite_achat_id`, `qte_brute_saisie`, `qte_nette_controlee`, `prix_unitaire_net`, `montant_ht`, `reduction`, `stock_avant_entree`, `lot_numero`, `nombre_sacs`, `ecart_controle`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 1, 7, 1, NULL, NULL, 3, 400.00, 133.33, 0.00, 0.00, 0.00, 0.00, '4566', 5.97, 0.00, '2025-10-05 10:44:34', NULL, '2025-10-05 16:54:50', 'admin'),
(2, 1, 7, 1, NULL, NULL, 3, 400.00, 133.33, 0.00, 0.00, 0.00, 0.00, '4566', 5.97, 0.00, '2025-10-05 20:19:27', NULL, '2025-10-05 17:19:27', NULL),
(3, 1, 6, 1, NULL, NULL, 3, 4250.00, 1416.67, 0.00, 0.00, 0.00, 0.00, '7896', 63.43, 0.00, '2025-10-05 20:19:27', NULL, '2025-10-05 16:54:50', 'admin'),
(4, 1, 7, 1, NULL, NULL, 3, 400.00, 133.33, 0.00, 0.00, 0.00, 0.00, '4566', 5.97, 0.00, '2025-10-05 20:51:05', NULL, '2025-10-05 17:51:05', NULL),
(5, 1, 6, 1, NULL, NULL, 3, 4250.00, 1416.67, 0.00, 0.00, 0.00, 0.00, '7896', 63.43, 0.00, '2025-10-05 20:51:05', NULL, '2025-10-05 17:51:05', NULL),
(8, 2, 5, 1, NULL, '[{\"producteur_id\":3,\"qte\":100},{\"producteur_id\":4,\"qte\":100},{\"producteur_id\":2,\"qte\":10}]', 3, 630.00, 210.00, 0.00, 0.00, 0.00, 0.00, '5263', 10.00, 0.00, '2025-10-05 21:01:58', NULL, '2025-10-05 20:07:36', 'admin'),
(9, 3, 6, 1, NULL, '[{\"producteur_id\":2,\"qte\":100},{\"producteur_id\":3,\"qte\":200},{\"producteur_id\":4,\"qte\":35}]', 3, 1005.00, 335.00, 15000.00, 5025000.00, 0.00, 0.00, '253', 15.00, 0.00, '2025-10-05 21:30:23', NULL, '2025-10-05 21:56:15', 'admin'),
(10, 3, 16, 1, NULL, '[{\"producteur_id\":2,\"qte\":1161.33}]', 3, 3484.00, 1161.33, 16000.00, 18581280.00, 0.00, 0.00, '56', 52.00, 0.00, '2025-10-05 21:30:23', NULL, '2025-10-05 21:56:15', 'admin'),
(11, 4, 6, 1, NULL, NULL, 3, 670.00, 223.33, 15000.00, 3349950.00, 0.00, 0.00, '253', 10.00, 0.00, '2025-10-05 21:47:19', NULL, '2025-10-05 21:55:54', 'admin'),
(12, 4, 7, 1, NULL, NULL, 3, 1005.00, 335.00, 20000.00, 6700000.00, 0.00, 0.00, '523', 15.00, 0.00, '2025-10-05 21:47:19', NULL, '2025-10-05 21:55:54', 'admin'),
(18, 5, 16, 1, NULL, NULL, 3, 1800.00, 600.00, 15000.00, 9000000.00, 0.00, 0.00, '102', 26.87, 1200.00, '2025-10-06 01:41:18', NULL, '2025-10-05 22:11:35', 'admin'),
(19, 5, 7, 1, NULL, NULL, 3, 1005.00, 335.00, 25000.00, 8375000.00, 0.00, 0.00, '523', 15.00, 670.00, '2025-10-06 01:41:18', NULL, '2025-10-05 22:11:35', 'admin'),
(22, 6, 6, 1, NULL, NULL, 3, 335.00, 111.67, 0.00, 0.00, 0.00, 0.00, '125', 5.00, 223.33, '2025-10-06 08:17:44', NULL, '2025-10-06 04:18:07', 'admin'),
(23, 6, 7, 1, NULL, NULL, 3, 200.00, 66.67, 0.00, 0.00, 0.00, 0.00, '163', 2.99, 133.33, '2025-10-06 08:17:44', NULL, '2025-10-06 04:18:07', 'admin'),
(24, 6, 5, 1, NULL, NULL, 3, 10050.00, 3350.00, 0.00, 0.00, 0.00, 0.00, '523', 150.00, 6700.00, '2025-10-06 08:17:44', NULL, '2025-10-06 04:18:07', 'admin');

--
-- Déclencheurs `achat_detail`
--
DELIMITER $$
CREATE TRIGGER `tr_achat_detail_delete` AFTER DELETE ON `achat_detail` FOR EACH ROW BEGIN
    
    DELETE FROM mouvements_stock 
    WHERE reference_document = (SELECT reference_achat FROM achat_entete WHERE id = OLD.achat_entete_id)
    AND produit_id = OLD.produit_id AND depot_id = OLD.depot_id;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `tr_achat_detail_insert` AFTER INSERT ON `achat_detail` FOR EACH ROW BEGIN
    
    DECLARE unite_stock_id INT;
    DECLARE ref_achat VARCHAR(100);
    
    
    SELECT unite_stock_id INTO unite_stock_id FROM produits WHERE id = NEW.produit_id;
    
    
    SELECT reference_achat INTO ref_achat FROM achat_entete WHERE id = NEW.achat_entete_id;
    
    
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        reference_document, lot_numero, motif, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'ENTREE', NEW.qte_nette_controlee,
        unite_stock_id, ref_achat, NEW.lot_numero, 
        CONCAT('Validation achat ', ref_achat, ' - ', NEW.qte_nette_controlee, ' kg'), 
        NEW.date_creation, NEW.cree_par
    );
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `tr_achat_detail_update` AFTER UPDATE ON `achat_detail` FOR EACH ROW BEGIN
    
    DECLARE unite_stock_id INT;
    DECLARE ref_achat VARCHAR(100);
    
    
    DELETE FROM mouvements_stock 
    WHERE reference_document = (SELECT reference_achat FROM achat_entete WHERE id = NEW.achat_entete_id)
    AND produit_id = NEW.produit_id AND depot_id = NEW.depot_id;
    
    
    SELECT unite_stock_id INTO unite_stock_id FROM produits WHERE id = NEW.produit_id;
    
    
    SELECT reference_achat INTO ref_achat FROM achat_entete WHERE id = NEW.achat_entete_id;
    
    
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        reference_document, lot_numero, motif, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'ENTREE', NEW.qte_nette_controlee,
        unite_stock_id, ref_achat, NEW.lot_numero, 
        CONCAT('Validation achat ', ref_achat, ' - ', NEW.qte_nette_controlee, ' kg'), 
        NEW.date_creation, NEW.cree_par
    );
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Structure de la table `achat_entete`
--

CREATE TABLE `achat_entete` (
  `id` int(11) NOT NULL,
  `reference_achat` varchar(50) NOT NULL,
  `fournisseur_id` int(11) NOT NULL,
  `date_achat` date DEFAULT NULL,
  `date_livraison` date DEFAULT NULL,
  `statut` enum('SAISIE','LIVRE','CONTROLE','A_PAYER','PAYE','ANNULE') NOT NULL DEFAULT 'SAISIE',
  `frais_transport` decimal(12,2) DEFAULT 0.00,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `achat_entete`
--

INSERT INTO `achat_entete` (`id`, `reference_achat`, `fournisseur_id`, `date_achat`, `date_livraison`, `statut`, `frais_transport`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(2, 'ACH-2025-1005-217299', 3, '2025-10-04', '2025-10-12', 'ANNULE', 0.00, '2025-10-05 20:00:55', 'admin', '2025-10-05 17:28:21', 'admin'),
(3, 'ACH-2025-1005-971436', 1, '2025-10-03', '2025-10-05', 'PAYE', 0.00, '2025-10-05 20:30:23', 'admin', '2025-10-05 21:56:15', 'admin'),
(4, 'ACH-2025-1005-986522', 1, '2025-10-01', '2025-10-30', 'PAYE', 0.00, '2025-10-05 20:47:19', 'admin', '2025-10-05 21:55:54', 'admin'),
(5, 'ACH-2025-1006-884763', 5, '2025-10-02', '2025-10-06', 'PAYE', 0.00, '2025-10-06 00:05:21', 'admin', '2025-10-05 22:11:35', 'admin'),
(6, 'ACH-2025-1006-790050', 1, '2025-10-06', '2025-10-06', 'CONTROLE', 0.00, '2025-10-06 07:17:17', 'admin', '2025-10-06 04:18:07', 'admin');

-- --------------------------------------------------------

--
-- Structure de la table `categorie`
--

CREATE TABLE `categorie` (
  `id` int(11) NOT NULL,
  `libelle` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `classement` enum('PRODUITS','FOURNITURES') NOT NULL DEFAULT 'PRODUITS',
  `est_actif` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1 pour actif, 0 pour inactif',
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `categorie`
--

INSERT INTO `categorie` (`id`, `libelle`, `description`, `classement`, `est_actif`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(3, 'BIO', NULL, 'PRODUITS', 1, '2025-03-17 21:23:44', NULL, '2025-10-02 01:27:16', NULL),
(4, 'CONVENTIONEL', NULL, 'PRODUITS', 1, '2025-03-17 21:23:53', NULL, '2025-10-02 01:27:16', NULL),
(5, 'BIO - FT', NULL, 'PRODUITS', 1, '2025-03-17 21:24:01', NULL, '2025-10-02 01:27:16', NULL),
(6, 'CONSOMABLE', NULL, 'FOURNITURES', 1, '2025-03-20 04:00:26', NULL, '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `clients`
--

CREATE TABLE `clients` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `adresse` text DEFAULT NULL,
  `telephone` varchar(30) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `nif` varchar(100) DEFAULT NULL,
  `stat` varchar(100) DEFAULT NULL,
  `type_client` enum('Export','Local') NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `clients`
--

INSERT INTO `clients` (`id`, `nom`, `adresse`, `telephone`, `email`, `nif`, `stat`, `type_client`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'SAL IBRAHIM Tanambao', 'Antsiranana', '032284687524', '<EMAIL>', NULL, NULL, 'Export', '2025-02-01 04:25:31', 'admin', '2025-10-02 01:27:16', 'admin'),
(2, 'Test', 'Test', '032000', '<EMAIL>', '079736', '5373773', 'Export', '2025-05-23 00:56:28', 'admin', '2025-10-02 01:27:16', NULL),
(3, 'Felchlin ', NULL, NULL, NULL, NULL, NULL, 'Export', '2025-05-23 07:53:30', NULL, '2025-10-02 01:27:16', NULL),
(4, 'Barry calbaut', 'Amsterdam', NULL, NULL, NULL, NULL, 'Export', '2025-06-27 07:06:34', NULL, '2025-10-02 01:27:16', NULL),
(5, 'RAYA Dayeuhkolot', 'Inde', NULL, NULL, NULL, NULL, 'Export', '2025-06-30 06:15:27', NULL, '2025-10-02 01:27:16', NULL),
(6, 'TRADIN', 'AMSTERDAM', NULL, NULL, NULL, NULL, 'Export', '2025-06-30 06:27:16', NULL, '2025-10-02 01:27:16', NULL),
(7, 'ICAM SPA', 'Inde', NULL, NULL, NULL, NULL, 'Export', '2025-06-30 06:54:04', NULL, '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `communes`
--

CREATE TABLE `communes` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `code_postal` varchar(10) DEFAULT NULL,
  `region` varchar(255) DEFAULT NULL,
  `pays` varchar(100) DEFAULT 'Madagascar',
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `communes`
--

INSERT INTO `communes` (`id`, `nom`, `code_postal`, `region`, `pays`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'ANTSAKOAMANONDRO', '206', 'DIANA', 'MADAGASCAR', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(2, 'ANKATAFA VAOVAO', '206', 'DIANA', 'MADAGASCAR', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(3, 'AMBODIVANIO', '206', 'DIANA', 'MADAGASCAR', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(4, 'ANKARAMIBE', '201', 'DIANA', 'Madagascar', '2025-10-05 22:02:33', 'admin', '2025-10-05 19:02:56', 'admin');

-- --------------------------------------------------------

--
-- Structure de la table `cooperatives`
--

CREATE TABLE `cooperatives` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `adresse` text DEFAULT NULL,
  `telephone` varchar(30) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `cooperatives`
--

INSERT INTO `cooperatives` (`id`, `nom`, `adresse`, `telephone`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'Coopérative Sambirano Mateza', '20', NULL, '2025-02-01 09:05:43', 'admin', '2025-10-02 01:27:16', 'admin'),
(2, 'Coopérative Bio Sambirano', '20', NULL, '2025-03-17 22:21:58', NULL, '2025-10-02 01:27:16', NULL),
(3, 'Coopérative Cacao et Vanille Sambirano', '20', NULL, '2025-03-25 08:57:06', NULL, '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `depot`
--

CREATE TABLE `depot` (
  `id` int(11) NOT NULL,
  `libelle` varchar(255) NOT NULL,
  `est_actif` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1 pour actif, 0 pour inactif',
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `depot`
--

INSERT INTO `depot` (`id`, `libelle`, `est_actif`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'MAGASIN N°01', 1, '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `forme`
--

CREATE TABLE `forme` (
  `id` int(11) NOT NULL,
  `libelle` varchar(100) NOT NULL,
  `famille_forme` varchar(255) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `forme`
--

INSERT INTO `forme` (`id`, `libelle`, `famille_forme`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(79, 'KG', NULL, '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `fournisseurs`
--

CREATE TABLE `fournisseurs` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `contact` varchar(30) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `adresse` text DEFAULT NULL,
  `nif` varchar(50) DEFAULT NULL,
  `stat` varchar(50) DEFAULT NULL,
  `est_actif` tinyint(1) NOT NULL DEFAULT 1,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `date_derniere_modif` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `fournisseurs`
--

INSERT INTO `fournisseurs` (`id`, `nom`, `contact`, `email`, `adresse`, `nif`, `stat`, `est_actif`, `date_creation`, `date_derniere_modif`, `cree_par`, `dernier_modif_par`) VALUES
(1, 'HOUSSENI', '032', '<EMAIL>', 'AMBANJA', 'KI52639874526', '4152639875652', 1, '2025-03-20 00:00:00', NULL, NULL, NULL),
(3, 'IMPORTER', '032', '<EMAIL>', 'CENTRE VILLE', 'UJ85969748585', '15975369369654', 1, '2025-03-20 00:00:00', NULL, NULL, NULL),
(4, 'AKBAR ALY', '0342563989', '<EMAIL>', '41 Morarano', '41526398789', 'Stat4152636', 1, '0000-00-00 00:00:00', NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `mouvements_stock`
--

CREATE TABLE `mouvements_stock` (
  `id` int(11) NOT NULL,
  `produit_id` int(11) NOT NULL,
  `depot_id` int(11) NOT NULL,
  `type_mouvement` enum('ENTREE','SORTIE','AJUSTEMENT','TRANSFERT') NOT NULL,
  `quantite` decimal(10,2) NOT NULL,
  `unite_id` int(11) NOT NULL,
  `reference_document` varchar(100) DEFAULT NULL,
  `lot_numero` varchar(100) DEFAULT NULL,
  `motif` text DEFAULT NULL,
  `date_mouvement` datetime NOT NULL DEFAULT current_timestamp(),
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `mouvements_stock`
--

INSERT INTO `mouvements_stock` (`id`, `produit_id`, `depot_id`, `type_mouvement`, `quantite`, `unite_id`, `reference_document`, `lot_numero`, `motif`, `date_mouvement`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 6, 1, 'ENTREE', 111.67, 3, 'ACH-6', '125', 'Validation achat 6 - 111.67 kg (1 sac + 44.67 kg)', '2025-10-06 08:18:07', '2025-10-06 07:18:07', 'admin', '2025-10-06 04:18:07', 'admin'),
(2, 7, 1, 'ENTREE', 66.67, 2, 'ACH-6', '163', 'Validation achat 6 - 66.67 kg', '2025-10-06 08:18:07', '2025-10-06 07:18:07', 'admin', '2025-10-06 04:18:07', 'admin'),
(3, 5, 1, 'ENTREE', 3350.00, 2, 'ACH-6', '523', 'Validation achat 6 - 3350 kg', '2025-10-06 08:18:07', '2025-10-06 07:18:07', 'admin', '2025-10-06 04:18:07', 'admin');

-- --------------------------------------------------------

--
-- Structure de la table `operation_caisse`
--

CREATE TABLE `operation_caisse` (
  `id` int(11) NOT NULL,
  `achat_id` int(11) DEFAULT NULL COMMENT 'ID de l''achat concerné',
  `fournisseur_id` int(11) DEFAULT NULL COMMENT 'ID du fournisseur',
  `type_operation` enum('PAIEMENT_ACHAT','AVANCE','REMBOURSEMENT','AUTRE') NOT NULL,
  `mode_paiement` enum('CHEQUE','VIREMENT','ESPECE') NOT NULL,
  `reference_paiement` varchar(100) DEFAULT NULL COMMENT 'Numéro de chèque ou référence virement',
  `montant` decimal(18,2) NOT NULL,
  `statut` enum('EN_ATTENTE','VALIDE','ANNULE') DEFAULT 'EN_ATTENTE' COMMENT 'Statut du paiement',
  `date_paiement` date NOT NULL,
  `effectue_par` varchar(255) DEFAULT NULL,
  `commentaires` text DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `operation_caisse`
--

INSERT INTO `operation_caisse` (`id`, `achat_id`, `fournisseur_id`, `type_operation`, `mode_paiement`, `reference_paiement`, `montant`, `statut`, `date_paiement`, `effectue_par`, `commentaires`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 4, NULL, 'PAIEMENT_ACHAT', 'ESPECE', '', 10049950.00, 'VALIDE', '2025-10-05', '${currentUser}', '', '2025-10-05 23:35:56', 'admin', '2025-10-05 20:59:54', 'admin'),
(2, 3, NULL, 'PAIEMENT_ACHAT', 'CHEQUE', '14253669', 23606280.00, 'VALIDE', '2025-10-05', 'admin', '', '2025-10-06 00:42:37', 'admin', '2025-10-05 21:59:36', 'admin'),
(3, 4, NULL, 'PAIEMENT_ACHAT', 'ESPECE', '', 10049950.00, 'VALIDE', '2025-10-05', 'admin', '', '2025-10-06 00:55:54', 'admin', '2025-10-05 22:11:03', 'admin'),
(4, 3, NULL, 'PAIEMENT_ACHAT', 'ESPECE', '', 23606280.00, 'VALIDE', '2025-10-05', 'admin', '', '2025-10-06 00:56:15', 'admin', '2025-10-05 22:11:10', 'admin'),
(5, 5, NULL, 'PAIEMENT_ACHAT', 'ESPECE', '', 17375000.00, 'EN_ATTENTE', '2025-10-05', 'admin', '', '2025-10-06 01:11:35', 'admin', '2025-10-05 22:11:35', 'admin');

-- --------------------------------------------------------

--
-- Structure de la table `permissions`
--

CREATE TABLE `permissions` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `category` varchar(50) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `description`, `category`, `created_at`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'menu.achats.view', 'Voir le menu Achats', 'Menu', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(2, 'menu.stocks.view', 'Voir le menu Stocks', 'Menu', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(3, 'menu.comptabilite.view', 'Voir le menu Comptabilité', 'Menu', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(4, 'menu.utilisateurs.view', 'Voir le menu Utilisateurs', 'Menu', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'menu.rapports.view', 'Voir le menu Rapports', 'Menu', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(6, 'menu.parametres.view', 'Voir le menu Paramètres', 'Menu', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(7, 'menu.lead_farmers.view', 'Voir le menu Lead Farmers', 'Menu', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(8, 'menu.planteurs.view', 'Voir le menu Planteurs', 'Menu', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(9, 'achats.create', 'Créer un achat', 'Achats', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(10, 'achats.view', 'Voir les achats', 'Achats', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(11, 'achats.update', 'Modifier un achat', 'Achats', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(12, 'achats.validate', 'Valider un achat', 'Achats', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(13, 'achats.pay', 'Marquer un achat comme payé', 'Achats', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(14, 'achats.delete', 'Supprimer un achat', 'Achats', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(15, 'achats.export', 'Exporter les achats', 'Achats', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(16, 'achats.view_details', 'Voir les détails d\'un achat', 'Achats', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(17, 'stocks.view', 'Voir les stocks', 'Stocks', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(18, 'stocks.inventory', 'Effectuer un inventaire', 'Stocks', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(19, 'stocks.adjustment', 'Faire un ajustement de stock', 'Stocks', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(20, 'stocks.transfer', 'Effectuer un transfert', 'Stocks', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(21, 'stocks.view_history', 'Voir l\'historique des mouvements', 'Stocks', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(22, 'comptabilite.journal_caisse', 'Gérer le journal de caisse', 'Comptabilité', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(23, 'comptabilite.paiement_achat', 'Gérer les paiements d\'achats', 'Comptabilité', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(24, 'comptabilite.solde_initial', 'Gérer les soldes initiaux', 'Comptabilité', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(25, 'comptabilite.rapports', 'Voir les rapports financiers', 'Comptabilité', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(26, 'utilisateurs.view', 'Voir les utilisateurs', 'Utilisateurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(27, 'utilisateurs.create', 'Créer un utilisateur', 'Utilisateurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(28, 'utilisateurs.update', 'Modifier un utilisateur', 'Utilisateurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(29, 'utilisateurs.delete', 'Supprimer un utilisateur', 'Utilisateurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(30, 'utilisateurs.permissions', 'Gérer les permissions des utilisateurs', 'Utilisateurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(31, 'utilisateurs.roles', 'Gérer les rôles', 'Utilisateurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(32, 'lead_farmers.create', 'Créer un Lead Farmer', 'Lead Farmers', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(33, 'lead_farmers.view', 'Voir les Lead Farmers', 'Lead Farmers', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(34, 'lead_farmers.update', 'Modifier un Lead Farmer', 'Lead Farmers', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(35, 'lead_farmers.delete', 'Supprimer un Lead Farmer', 'Lead Farmers', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(36, 'lead_farmers.export', 'Exporter les Lead Farmers', 'Lead Farmers', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(37, 'planteurs.create', 'Créer un Planteur', 'Planteurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(38, 'planteurs.view', 'Voir les Planteurs', 'Planteurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(39, 'planteurs.update', 'Modifier un Planteur', 'Planteurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(40, 'planteurs.delete', 'Supprimer un Planteur', 'Planteurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(41, 'planteurs.export', 'Exporter les Planteurs', 'Planteurs', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(42, 'parametres.view', 'Voir les paramètres', 'Paramètres', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(43, 'parametres.create', 'Créer des paramètres', 'Paramètres', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(44, 'parametres.update', 'Modifier des paramètres', 'Paramètres', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(45, 'parametres.delete', 'Supprimer des paramètres', 'Paramètres', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `presentation`
--

CREATE TABLE `presentation` (
  `id` int(11) NOT NULL,
  `libelle` varchar(100) NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `presentation`
--

INSERT INTO `presentation` (`id`, `libelle`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(18, 'SAC', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `producteurs`
--

CREATE TABLE `producteurs` (
  `id` int(11) NOT NULL,
  `code_producteur` varchar(50) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `code_leads` varchar(50) DEFAULT NULL,
  `genre` enum('M','F','A') DEFAULT NULL COMMENT 'Masculin, Féminin, Autre',
  `cin` varchar(50) DEFAULT NULL,
  `contact` varchar(255) DEFAULT NULL,
  `adresse` text DEFAULT NULL,
  `telephone` varchar(30) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `nif` varchar(100) DEFAULT NULL,
  `stat` varchar(100) DEFAULT NULL,
  `date_naissance` date DEFAULT NULL,
  `annee_adhesion_coop` year(4) DEFAULT NULL,
  `cotisation_droit_adhesion` decimal(12,2) DEFAULT NULL,
  `site` varchar(255) DEFAULT NULL,
  `classement` enum('LEAD FARMER','PETIT PLANTEUR','FREELANCE') NOT NULL DEFAULT 'PETIT PLANTEUR',
  `situation` varchar(255) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL COMMENT 'Chemin vers la photo du producteur',
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `producteurs`
--

INSERT INTO `producteurs` (`id`, `code_producteur`, `nom`, `code_leads`, `genre`, `cin`, `contact`, `adresse`, `telephone`, `email`, `nif`, `stat`, `date_naissance`, `annee_adhesion_coop`, `cotisation_droit_adhesion`, `site`, `classement`, `situation`, `photo`, `date_creation`, `date_derniere_modif`, `cree_par`, `dernier_modif_par`) VALUES
(1, 'BS/ATSK/001', 'ABDOUL Sylvain JOMA', 'LF 01/BS/CSM/ATSK', 'M', '719011012960', '0346740932/0325504361', 'Antsiranana, Madagascar', '032284687524', '<EMAIL>', NULL, NULL, '1978-08-06', '2016', 29.00, '1', 'LEAD FARMER', '5', '097b6381f909d1ab3e2546902398ddbd.jpeg', '2025-01-01 00:00:00', '2025-10-05 20:39:19', NULL, 'admin'),
(2, 'BS/ATSK/002', 'Aly Bruno', '', 'M', '719341004080', NULL, 'Ambanja, Madagascar', '0342563989', '<EMAIL>', NULL, NULL, '0000-00-00', '2016', 29.00, '2', 'PETIT PLANTEUR', '5', 'a7e80eeca9f3eef711ae80b9ad2493a5.png', '2025-01-01 00:00:00', '2025-10-05 20:39:34', NULL, 'admin'),
(3, 'BS/ATSK/003', 'Assany Tohalibo (1/3)', '', 'M', '719131002872', NULL, '', '', '', NULL, NULL, '1968-03-03', '2016', 29000.00, '3', 'PETIT PLANTEUR', '5', '3217efcfba4c215705cbfb731950bb6e.png', '2025-01-01 00:00:00', '2025-10-05 20:39:53', NULL, 'admin'),
(4, 'BS/ATSK/004', 'AUGUISTIN Sabotsy', '', 'M', '719111000127', NULL, '', '', '', NULL, NULL, '0000-00-00', '2016', 29.00, '1', 'PETIT PLANTEUR', '5', '54ea39027f7cad9f2cac1ed69ff47c62.jpg', '2025-01-01 00:00:00', '2025-10-05 20:42:34', NULL, 'admin'),
(5, 'Marie Zoe', 'Blanchir', 'sdsqd', 'M', '715', NULL, '', '', '', NULL, NULL, '0000-00-00', NULL, NULL, '4', 'LEAD FARMER', NULL, 'cc74859aa2ac439ed1b7de155c1435b5.jpg', '2025-10-05 22:18:08', '2025-10-05 20:38:17', 'admin', 'admin');

-- --------------------------------------------------------

--
-- Structure de la table `production_globale`
--

CREATE TABLE `production_globale` (
  `id` int(11) NOT NULL,
  `producteur_id` int(11) NOT NULL,
  `annee` year(4) NOT NULL,
  `nombre_pieds` int(11) DEFAULT NULL,
  `surface_totale_production_ha` decimal(10,2) DEFAULT NULL,
  `volume_production_estimee_t` decimal(10,2) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `production_globale`
--

INSERT INTO `production_globale` (`id`, `producteur_id`, `annee`, `nombre_pieds`, `surface_totale_production_ha`, `volume_production_estimee_t`, `date_creation`, `date_derniere_modif`, `cree_par`, `dernier_modif_par`) VALUES
(1, 1, '2025', 550, 0.88, 1.00, '2025-01-01 00:00:00', '2025-10-02 01:27:16', NULL, NULL),
(2, 2, '2025', 500, 0.80, 1.00, '2025-01-01 00:00:00', '2025-10-02 01:27:16', NULL, NULL);

-- --------------------------------------------------------

--
-- Structure de la table `production_mensuelle`
--

CREATE TABLE `production_mensuelle` (
  `id` int(11) NOT NULL,
  `producteur_id` int(11) NOT NULL,
  `annee` year(4) NOT NULL,
  `mois` enum('JAN','FEV','MAR','AVR','MAI','JUIN','JUIL','AOUT','SEPT','OCT','NOV','DEC') NOT NULL,
  `type_production` enum('SEC','FRAICHE') NOT NULL,
  `volume_produit_kg` decimal(10,2) NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `produits`
--

CREATE TABLE `produits` (
  `id` int(11) NOT NULL,
  `nom` varchar(255) NOT NULL,
  `type` enum('Standard','Superieur') NOT NULL,
  `certification` enum('Bio','Conventionnel') NOT NULL,
  `categorie_id` int(11) NOT NULL,
  `classe` enum('PRODUITS','FOURNITURES') NOT NULL,
  `unite_vente_id` int(11) NOT NULL,
  `unite_achat_id` int(11) NOT NULL,
  `unite_stock_id` int(11) NOT NULL,
  `presentation_id` int(11) DEFAULT NULL,
  `forme_id` int(11) DEFAULT NULL,
  `qte_presentation` int(11) DEFAULT 0,
  `qte_forme` int(11) DEFAULT 0,
  `prix_vente` decimal(12,2) NOT NULL,
  `marge_beneficiaire_pct` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '% par rapport au prix d''achat',
  `prix_achat` decimal(12,2) NOT NULL DEFAULT 0.00,
  `stock_min` int(11) NOT NULL DEFAULT 0,
  `stock_max` int(11) NOT NULL DEFAULT 0,
  `image_url` text DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `produits`
--

INSERT INTO `produits` (`id`, `nom`, `type`, `certification`, `categorie_id`, `classe`, `unite_vente_id`, `unite_achat_id`, `unite_stock_id`, `presentation_id`, `forme_id`, `qte_presentation`, `qte_forme`, `prix_vente`, `marge_beneficiaire_pct`, `prix_achat`, `stock_min`, `stock_max`, `image_url`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(5, 'CACAO BIO SUPERIEUR', '', 'Bio', 3, 'PRODUITS', 2, 3, 2, 18, 79, 1, 67, 40000.00, 0.00, 36000.00, 10000, 50000, NULL, '2025-03-17 21:27:37', NULL, '2025-10-06 04:19:59', 'admin'),
(6, 'CACAO BIO STANDARD', 'Standard', 'Bio', 5, 'PRODUITS', 3, 3, 3, 18, 79, 1, 67, 50000.00, 0.00, 40000.00, 0, 0, NULL, '2025-03-17 21:57:35', NULL, '2025-10-06 04:04:11', 'admin'),
(7, 'CACAO CONVENTIONEL', 'Standard', 'Bio', 4, 'PRODUITS', 2, 3, 2, 18, 79, 1, 67, 50000.00, 0.00, 38000.00, 0, 0, NULL, '2025-03-17 21:58:01', NULL, '2025-10-06 04:20:13', 'admin'),
(16, 'CACAO BIO STANDARD (Copie)', 'Standard', 'Bio', 5, 'PRODUITS', 2, 3, 2, 18, 79, 1, 67, 15000.00, 0.00, 10000.00, 0, 0, NULL, '2025-10-05 06:42:34', 'admin', '2025-10-05 03:42:34', 'admin');

-- --------------------------------------------------------

--
-- Structure de la table `produits_stock`
--

CREATE TABLE `produits_stock` (
  `id` int(11) NOT NULL,
  `produit_id` int(11) NOT NULL,
  `depot_id` int(11) NOT NULL,
  `quantite` decimal(10,2) NOT NULL DEFAULT 0.00,
  `unite_stock_id` int(11) NOT NULL,
  `lot_numero` varchar(100) DEFAULT NULL,
  `date_entree` datetime DEFAULT current_timestamp(),
  `date_expiration` date DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `produits_stock`
--

INSERT INTO `produits_stock` (`id`, `produit_id`, `depot_id`, `quantite`, `unite_stock_id`, `lot_numero`, `date_entree`, `date_expiration`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 5, 1, 0.00, 1, '5263', '2025-10-05 21:02:52', NULL, '2025-10-05 20:02:52', 'admin', '2025-10-05 17:08:57', 'admin'),
(2, 6, 1, 558.33, 1, '253', '2025-10-05 21:30:37', NULL, '2025-10-05 20:30:37', 'admin', '2025-10-05 18:08:50', 'admin'),
(3, 16, 1, 1161.33, 1, '56', '2025-10-05 21:30:37', NULL, '2025-10-05 20:30:37', 'admin', '2025-10-05 17:30:37', 'admin'),
(4, 7, 1, 670.00, 1, '523', '2025-10-05 22:08:50', NULL, '2025-10-05 21:08:50', 'admin', '2025-10-05 21:41:30', 'admin'),
(5, 16, 1, 600.00, 1, '102', '2025-10-06 01:41:30', NULL, '2025-10-06 00:41:30', 'admin', '2025-10-05 21:41:30', 'admin'),
(6, 6, 1, 111.67, 3, '125', '2025-10-06 08:18:07', NULL, '2025-10-06 07:18:07', 'admin', '2025-10-06 04:18:07', 'admin'),
(7, 7, 1, 66.67, 2, '163', '2025-10-06 08:18:07', NULL, '2025-10-06 07:18:07', 'admin', '2025-10-06 04:18:07', 'admin'),
(8, 5, 1, 3350.00, 2, '523', '2025-10-06 08:18:07', NULL, '2025-10-06 07:18:07', 'admin', '2025-10-06 04:18:07', 'admin');

-- --------------------------------------------------------

--
-- Structure de la table `roles`
--

CREATE TABLE `roles` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `roles`
--

INSERT INTO `roles` (`id`, `name`, `description`, `created_at`, `updated_at`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'admin', 'Administrateur système - Accès complet', '2025-09-28 06:48:13', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(2, 'denis', 'Denis - Gestion des achats et stocks', '2025-09-28 06:48:13', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(3, 'joassin', 'Joassin - Validation des achats', '2025-09-28 06:48:13', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(4, 'guy', 'Guy - Comptabilité et paiements', '2025-09-28 06:48:13', '2025-09-28 06:48:13', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'manager', 'Manager', '2025-10-01 14:24:09', '2025-10-01 14:24:09', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(6, 'vendeur', 'Vendeur', '2025-10-01 14:24:09', '2025-10-01 14:24:09', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(7, 'caissier', 'Caissier', '2025-10-01 14:24:09', '2025-10-01 14:24:09', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(8, 'production', 'Production', '2025-10-01 14:24:09', '2025-10-01 14:24:09', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(9, 'utilisateur', 'Utilisateur standard', '2025-10-01 14:24:09', '2025-10-01 14:24:09', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `role_permissions`
--

CREATE TABLE `role_permissions` (
  `role_id` int(11) NOT NULL,
  `permission` varchar(191) NOT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

--
-- Déchargement des données de la table `role_permissions`
--

INSERT INTO `role_permissions` (`role_id`, `permission`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, '*', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'clients.create', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'clients.delete', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'clients.export', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'clients.update', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'entree_detail.create', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'entree_detail.delete', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'entree_detail.update', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'entree_entete.create', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'entree_entete.delete', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'entree_entete.pay', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'entree_entete.update', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'entree_entete.validate', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'entree_entete.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'menu.achats.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'menu.clients.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'menu.dashboard.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'menu.produits.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'menu.stocks.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'mouvement_stock.create', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'mouvement_stock.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'page.achats_entete.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'page.clients.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'page.dashboard.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'page.gestion_produits.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'page.gestion_stock.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'produit.create', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'produit.delete', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'produit.update', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'produit.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'stock_detail.adjustment', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 'stock_detail.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(7, 'commandes.payer', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(8, 'matiere.*', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(8, 'production.*', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(9, 'menu.dashboard.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(9, 'page.dashboard.view', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `unites`
--

CREATE TABLE `unites` (
  `id` int(11) NOT NULL,
  `libelle` varchar(100) NOT NULL,
  `est_actif` tinyint(1) NOT NULL DEFAULT 1,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `unites`
--

INSERT INTO `unites` (`id`, `libelle`, `est_actif`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'POUDRE', 1, '2025-02-01 07:41:35', NULL, '2025-10-02 01:27:16', NULL),
(2, 'SAC', 1, '2025-02-01 07:41:40', NULL, '2025-10-02 01:27:16', NULL),
(3, 'KG', 1, '2025-02-01 07:41:54', NULL, '2025-10-02 01:27:16', NULL),
(6, 'TONNE', 1, '2025-02-01 07:58:21', NULL, '2025-10-02 01:27:16', NULL),
(7, 'BOITE', 1, '2025-03-20 04:01:10', NULL, '2025-10-02 01:27:16', NULL),
(8, 'UNITE', 1, '2025-03-20 04:01:19', NULL, '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role_id` int(11) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `status` tinyint(1) DEFAULT 1,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `full_name`, `password`, `role_id`, `phone`, `status`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', 'Administrateur', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 1, NULL, 1, '2025-10-02 08:14:18', '2025-09-28 06:48:13', '2025-10-02 08:14:18'),
(2, 'denis', '<EMAIL>', 'Denis', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 2, NULL, 1, NULL, '2025-09-28 06:48:13', '2025-09-28 10:30:42'),
(3, 'joassin', '<EMAIL>', 'Joassin', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 3, NULL, 1, NULL, '2025-09-28 06:48:13', '2025-09-28 10:30:42'),
(4, 'guy', '<EMAIL>', 'Guy', '$2y$10$GCRoy.1CmiZCJLAwDEwE4OyPPMMl5NFpkQwaAW0D26oS8asNFmNay', 4, NULL, 1, NULL, '2025-09-28 06:48:13', '2025-09-28 10:30:42');

-- --------------------------------------------------------

--
-- Structure de la table `user_permissions`
--

CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  `granted` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT current_timestamp(),
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `ventes_details`
--

CREATE TABLE `ventes_details` (
  `id` int(11) NOT NULL,
  `vente_id` int(11) NOT NULL,
  `produit_id` int(11) DEFAULT NULL,
  `depot_id` int(11) DEFAULT NULL,
  `expedition` varchar(100) DEFAULT NULL,
  `grade` varchar(50) DEFAULT NULL,
  `qualite` varchar(100) DEFAULT NULL,
  `qte_tonnes` decimal(10,2) DEFAULT NULL,
  `qte_dernier_stock` decimal(10,2) DEFAULT NULL,
  `nbre_lot` int(11) DEFAULT NULL,
  `bl` varchar(100) DEFAULT NULL,
  `conteneur` varchar(100) DEFAULT NULL,
  `seal` varchar(100) DEFAULT NULL,
  `lots` varchar(255) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `ventes_details`
--

INSERT INTO `ventes_details` (`id`, `vente_id`, `produit_id`, `depot_id`, `expedition`, `grade`, `qualite`, `qte_tonnes`, `qte_dernier_stock`, `nbre_lot`, `bl`, `conteneur`, `seal`, `lots`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 3, 7, 1, '07/01/25', 'Standard', 'Conventionnel', 25.00, NULL, 2, '0JM3DR1MA/TNR0158073', 'TCNU5657354', 'C718538', '145/24', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(2, 4, 7, NULL, '07/01/25', 'Standard', 'Conventionnel', 25.00, NULL, 2, '0JM3DR1MA/TNR0158073', 'ECMJ4908164', 'C7185137', '146/24', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(3, 5, 6, NULL, '08/01/25', 'Standard', 'Bio', 25.00, NULL, 2, NULL, NULL, NULL, '147/25', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(4, 6, 6, NULL, '08/01/25', 'Standard', 'Bio', 25.00, NULL, 2, NULL, NULL, NULL, '148/24', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(5, 7, 7, NULL, '30/01/25', 'Standard', 'Conventionnel', NULL, NULL, NULL, '0JM3ER1MA/TNR0158461', 'CMAU7566308', 'C7185165', '012/24', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(6, 8, 7, NULL, '30/01/25', 'Standard', 'Conventionnel', NULL, NULL, NULL, '0JM3ER1MA/TNR0158461', 'CMAU7566308', 'C7185165', '014/24', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(7, 9, 7, NULL, '30/1/25', 'Standard', 'Conventionnel', NULL, NULL, 1, '0JM3R1MA/TNR0158461', 'TGHU6066574', 'C7185166', '015/24', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(8, 10, 7, NULL, '30/1/25', 'Standard', 'Conventionnel', NULL, NULL, 2, '0JM3R1MA/TNR0158461', 'TGHU6066574', 'C7185166', '016/24', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(9, 11, 5, NULL, '31/01/25', 'Superieur', 'Bio', 13.00, NULL, 1, NULL, NULL, NULL, '013/24', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(10, 12, NULL, NULL, '01/2/25', 'Premium', 'Bio', 25.00, NULL, 4, '0JM3ER1MA/TNR0158478', 'CMAU485447', 'C7185163', '50/24', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(11, 13, 6, NULL, '17/3/25', 'Standard', 'Bio', 25.00, NULL, 2, '0JM3GR1MA/TNR0158978', 'CAIU7051953', 'C7185218', '100/25', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL),
(12, 14, 6, NULL, '17/3/25', 'Standard', 'Bio', 25.00, NULL, 2, '0JM3GR1MA/TNR0158978', 'CMAU6759956', 'C7185219', '101/25', '2025-10-02 11:15:40', NULL, '2025-10-02 08:15:40', NULL);

--
-- Déclencheurs `ventes_details`
--
DELIMITER $$
CREATE TRIGGER `tr_ventes_details_delete_CORRIGE` AFTER DELETE ON `ventes_details` FOR EACH ROW BEGIN
    -- Supprimer le mouvement correspondant
    DELETE FROM mouvements_stock 
    WHERE source_type = 'VENTE' AND source_id = OLD.id;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `tr_ventes_details_insert_CORRIGE` AFTER INSERT ON `ventes_details` FOR EACH ROW BEGIN
    -- Toutes les déclarations DOIVENT être au début
    DECLARE v_unite_stock_id INT;
    DECLARE v_ref_vente VARCHAR(100);

    -- Récupérer les informations nécessaires
    SELECT unite_stock_id INTO v_unite_stock_id FROM produits WHERE id = NEW.produit_id;
    SELECT facture_numero INTO v_ref_vente FROM ventes_entete WHERE id = NEW.vente_id;
    
    -- Insérer le mouvement de sortie
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        source_type, source_id, source_reference, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'SORTIE', NEW.qte_tonnes * 1000, -- Conversion Tonnes -> KG
        v_unite_stock_id, 'VENTE', NEW.id, v_ref_vente, NOW(), 'system'
    );
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `tr_ventes_details_update_CORRIGE` AFTER UPDATE ON `ventes_details` FOR EACH ROW BEGIN
    -- Toutes les déclarations DOIVENT être au début
    DECLARE v_unite_stock_id INT;
    DECLARE v_ref_vente VARCHAR(100);

    -- Supprimer l'ancien mouvement
    DELETE FROM mouvements_stock 
    WHERE source_type = 'VENTE' AND source_id = OLD.id;
    
    -- Récupérer les informations nécessaires
    SELECT unite_stock_id INTO v_unite_stock_id FROM produits WHERE id = NEW.produit_id;
    SELECT facture_numero INTO v_ref_vente FROM ventes_entete WHERE id = NEW.vente_id;
    
    -- Insérer le nouveau mouvement de sortie
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        source_type, source_id, source_reference, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'SORTIE', NEW.qte_tonnes * 1000, -- Conversion Tonnes -> KG
        v_unite_stock_id, 'VENTE', NEW.id, v_ref_vente, NOW(), 'system'
    );
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Structure de la table `ventes_entete`
--

CREATE TABLE `ventes_entete` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `n_domiciliation` varchar(255) DEFAULT NULL,
  `total_montant` decimal(18,2) DEFAULT NULL,
  `total_remise` decimal(18,2) DEFAULT NULL,
  `date_vente` date DEFAULT NULL,
  `statut` enum('Annuler','Encours','Facturer','En attente') NOT NULL DEFAULT 'Encours',
  `valeur_euro` decimal(18,2) DEFAULT NULL,
  `valeur_ar` decimal(18,2) DEFAULT NULL,
  `cours_devise` decimal(10,4) DEFAULT NULL,
  `dau_numero` varchar(100) DEFAULT NULL,
  `dau_date` date DEFAULT NULL,
  `facture_numero` varchar(100) DEFAULT NULL,
  `facture_date` date DEFAULT NULL,
  `lieux_exportation` varchar(255) DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `ventes_entete`
--

INSERT INTO `ventes_entete` (`id`, `client_id`, `n_domiciliation`, `total_montant`, `total_remise`, `date_vente`, `statut`, `valeur_euro`, `valeur_ar`, `cours_devise`, `dau_numero`, `dau_date`, `facture_numero`, `facture_date`, `lieux_exportation`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(3, 5, '080000824 E 08076', 1.00, NULL, NULL, 'Facturer', 210487.20, 1026209294.88, 4875.4000, 'E6', '2025-01-09', '19/24', '2024-12-18', 'Nosy be', '2025-06-30 01:12:23', '1', '2025-10-02 01:27:16', NULL),
(4, 5, '080000924 E 08076', NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E6', '2025-01-09', '019/24', '2024-12-18', NULL, '2025-06-30 01:18:54', '1', '2025-10-02 01:27:16', NULL),
(5, 6, NULL, 180.00, NULL, NULL, 'Facturer', 180417.60, 879607967.04, 4875.4000, 'E7', '2025-01-09', NULL, NULL, NULL, '2025-06-30 01:22:26', '1', '2025-10-02 01:27:16', NULL),
(6, 6, NULL, NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E7', '2025-01-09', NULL, NULL, 'Nosy be', '2025-06-30 01:27:25', '1', '2025-10-02 01:27:16', NULL),
(7, 4, '080000925 E 00615', 817.00, NULL, NULL, 'Facturer', 170394.40, 817898231.83, 4800.0300, 'E25', '2025-02-05', '004/25', '2025-01-30', 'Nosy be', '2025-06-30 01:30:52', '1', '2025-10-02 01:27:16', NULL),
(8, 4, '080000925 E 00615', NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E25', '2025-02-05', '004/25', '2025-01-30', 'Nosy be', '2025-06-30 01:35:21', '1', '2025-10-02 01:27:16', NULL),
(9, 4, '080000925 E 00615', NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E25', '2025-02-05', '004/25', '2025-01-30', 'Nosy be', '2025-06-30 01:38:35', '1', '2025-10-02 01:27:16', NULL),
(10, 4, '080000925 E 00615', NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E25', '2025-02-05', '004/25', '2025-01-30', 'Nosy be', '2025-06-30 01:42:23', '1', '2025-10-02 01:27:16', NULL),
(11, 3, '080000925 E 00613', 47.00, NULL, NULL, 'Facturer', 47034.00, 225763200.00, 4800.0000, 'E23', '2025-02-05', '002/25', '2025-01-30', 'Nosy be', '2025-06-30 01:48:43', '1', '2025-10-02 01:27:16', NULL),
(12, 7, '080000925 E 00614', 433.00, NULL, NULL, 'Facturer', 90208.80, 433004946.26, 4800.0300, 'E24', '2025-02-05', '003/25', '2025-01-30', 'Nosy be', '2025-06-30 01:51:42', '1', '2025-10-02 01:27:16', NULL),
(13, 6, '080000925 E 01774', 901.00, NULL, NULL, 'Facturer', 180417.60, 901247253.98, 4995.3400, 'E57', '2025-03-24', '005/25', '2025-03-19', 'Nosy be', '2025-06-30 01:55:42', '1', '2025-10-02 01:27:16', NULL),
(14, 6, '080000925 E 01774', NULL, NULL, NULL, 'Facturer', NULL, NULL, NULL, 'E57', '2025-03-24', '005/25', '2025-03-19', 'Nosy be', '2025-06-30 01:59:04', '1', '2025-10-02 01:27:16', NULL);

-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `v_avances_fournisseurs`
-- (Voir ci-dessous la vue réelle)
--
CREATE TABLE `v_avances_fournisseurs` (
`fournisseur_id` int(11)
,`fournisseur_nom` varchar(255)
,`total_avances` decimal(40,2)
,`total_remboursements` decimal(40,2)
,`solde_avance` decimal(41,2)
);

-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `v_historique_mouvements`
-- (Voir ci-dessous la vue réelle)
--
CREATE TABLE `v_historique_mouvements` (
`id` int(11)
,`date_mouvement` datetime
,`produit_nom` varchar(255)
,`depot_nom` varchar(255)
,`type_mouvement` enum('ENTREE','SORTIE','AJUSTEMENT','TRANSFERT')
,`quantite` decimal(10,2)
,`unite` varchar(100)
,`lot_numero` varchar(100)
,`cree_par` varchar(255)
);

-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `v_paiements_detaille`
-- (Voir ci-dessous la vue réelle)
--
CREATE TABLE `v_paiements_detaille` (
`id` int(11)
,`achat_id` int(11)
,`reference_achat` varchar(50)
,`fournisseur_nom` varchar(255)
,`fournisseur_adresse` text
,`fournisseur_telephone` varchar(30)
,`fournisseur_email` varchar(255)
,`fournisseur_contact` varchar(255)
,`fournisseur_site` varchar(255)
,`fournisseur_classement` enum('LEAD FARMER','PETIT PLANTEUR','FREELANCE')
,`type_operation` enum('PAIEMENT_ACHAT','AVANCE','REMBOURSEMENT','AUTRE')
,`mode_paiement` enum('CHEQUE','VIREMENT','ESPECE')
,`reference_paiement` varchar(100)
,`montant` decimal(18,2)
,`date_paiement` date
,`effectue_par` varchar(255)
,`commentaires` text
,`date_creation` datetime
,`cree_par` varchar(255)
);


-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `v_stock_actuel`
-- (Voir ci-dessous la vue réelle)
--
CREATE TABLE `v_stock_actuel` (
`id` int(11)
,`produit_id` int(11)
,`produit_nom` varchar(255)
,`depot_id` int(11)
,`depot_nom` varchar(255)
,`stock_actuel` decimal(10,2)
,`unite_stock` varchar(100)
,`lot_numero` varchar(100)
,`date_entree` datetime
,`date_expiration` date
,`statut_stock` varchar(12)
);

-- --------------------------------------------------------

--
-- Doublure de structure pour la vue `v_stock_total_produits`
-- (Voir ci-dessous la vue réelle)
--
CREATE TABLE `v_stock_total_produits` (
`produit_id` int(11)
,`produit_nom` varchar(255)
,`stock_total` decimal(32,2)
,`unite_stock_id` int(11)
,`unite_stock_libelle` varchar(100)
);

-- --------------------------------------------------------

--
-- Structure de la vue `v_avances_fournisseurs`
--
DROP TABLE IF EXISTS `v_avances_fournisseurs`;

CREATE ALGORITHM=UNDEFINED DEFINER=`facdegsp__1WaEcTx1O5fRo-gxbnXGKdIleAaUM2a5`@`localhost` SQL SECURITY DEFINER VIEW `v_avances_fournisseurs`  AS SELECT `p`.`id` AS `fournisseur_id`, `p`.`nom` AS `fournisseur_nom`, coalesce(sum(case when `oc`.`type_operation` = 'AVANCE' then `oc`.`montant` else 0 end),0) AS `total_avances`, coalesce(sum(case when `oc`.`type_operation` = 'REMBOURSEMENT' then `oc`.`montant` else 0 end),0) AS `total_remboursements`, coalesce(sum(case when `oc`.`type_operation` = 'AVANCE' then `oc`.`montant` else 0 end),0) - coalesce(sum(case when `oc`.`type_operation` = 'REMBOURSEMENT' then `oc`.`montant` else 0 end),0) AS `solde_avance` FROM (`producteurs` `p` left join `operation_caisse` `oc` on(`p`.`id` = `oc`.`fournisseur_id`)) GROUP BY `p`.`id`, `p`.`nom` ;

-- --------------------------------------------------------

--
-- Structure de la vue `v_historique_mouvements`
--
DROP TABLE IF EXISTS `v_historique_mouvements`;

CREATE ALGORITHM=UNDEFINED DEFINER=`facdegsp__1WaEcTx1O5fRo-gxbnXGKdIleAaUM2a5`@`localhost` SQL SECURITY DEFINER VIEW `v_historique_mouvements`  AS SELECT `m`.`id` AS `id`, `m`.`date_mouvement` AS `date_mouvement`, `p`.`nom` AS `produit_nom`, `d`.`libelle` AS `depot_nom`, `m`.`type_mouvement` AS `type_mouvement`, `m`.`quantite` AS `quantite`, `u`.`libelle` AS `unite`, `m`.`lot_numero` AS `lot_numero`, `m`.`cree_par` AS `cree_par` FROM (((`mouvements_stock` `m` join `produits` `p` on(`m`.`produit_id` = `p`.`id`)) join `depot` `d` on(`m`.`depot_id` = `d`.`id`)) join `unites` `u` on(`m`.`unite_id` = `u`.`id`)) ORDER BY `m`.`date_mouvement` DESC ;

-- --------------------------------------------------------

--
-- Structure de la vue `v_paiements_detaille`
--
DROP TABLE IF EXISTS `v_paiements_detaille`;

CREATE ALGORITHM=UNDEFINED DEFINER=`facdegsp__1WaEcTx1O5fRo-gxbnXGKdIleAaUM2a5`@`localhost` SQL SECURITY DEFINER VIEW `v_paiements_detaille`  AS SELECT `oc`.`id` AS `id`, `oc`.`achat_id` AS `achat_id`, `ae`.`reference_achat` AS `reference_achat`, `p`.`nom` AS `fournisseur_nom`, `p`.`adresse` AS `fournisseur_adresse`, `p`.`telephone` AS `fournisseur_telephone`, `p`.`email` AS `fournisseur_email`, `p`.`contact` AS `fournisseur_contact`, `p`.`site` AS `fournisseur_site`, `p`.`classement` AS `fournisseur_classement`, `oc`.`type_operation` AS `type_operation`, `oc`.`mode_paiement` AS `mode_paiement`, `oc`.`reference_paiement` AS `reference_paiement`, `oc`.`montant` AS `montant`, `oc`.`date_paiement` AS `date_paiement`, `oc`.`effectue_par` AS `effectue_par`, `oc`.`commentaires` AS `commentaires`, `oc`.`date_creation` AS `date_creation`, `oc`.`cree_par` AS `cree_par` FROM ((`operation_caisse` `oc` left join `achat_entete` `ae` on(`oc`.`achat_id` = `ae`.`id`)) left join `producteurs` `p` on(`ae`.`fournisseur_id` = `p`.`id`)) WHERE `oc`.`type_operation` = 'PAIEMENT_ACHAT' ORDER BY `oc`.`date_paiement` DESC ;

-- --------------------------------------------------------

--
-- Structure de la vue `v_stock_actuel`
--
DROP TABLE IF EXISTS `v_stock_actuel`;

CREATE ALGORITHM=UNDEFINED DEFINER=`facdegsp__1WaEcTx1O5fRo-gxbnXGKdIleAaUM2a5`@`localhost` SQL SECURITY DEFINER VIEW `v_stock_actuel`  AS SELECT `ps`.`id` AS `id`, `ps`.`produit_id` AS `produit_id`, `p`.`nom` AS `produit_nom`, `ps`.`depot_id` AS `depot_id`, `d`.`libelle` AS `depot_nom`, `ps`.`quantite` AS `stock_actuel`, `u`.`libelle` AS `unite_stock`, `ps`.`lot_numero` AS `lot_numero`, `ps`.`date_entree` AS `date_entree`, `ps`.`date_expiration` AS `date_expiration`, CASE WHEN `ps`.`quantite` <= `p`.`stock_min` THEN 'STOCK_FAIBLE' WHEN `ps`.`quantite` >= `p`.`stock_max` THEN 'STOCK_ELEVE' ELSE 'STOCK_NORMAL' END AS `statut_stock` FROM (((`produits_stock` `ps` left join `produits` `p` on(`ps`.`produit_id` = `p`.`id`)) left join `depot` `d` on(`ps`.`depot_id` = `d`.`id`)) left join `unites` `u` on(`ps`.`unite_stock_id` = `u`.`id`)) WHERE `ps`.`quantite` > 0 ORDER BY `p`.`nom` ASC, `d`.`libelle` ASC, `ps`.`lot_numero` ASC ;

-- --------------------------------------------------------

--
-- Structure de la vue `v_stock_total_produits`
--
DROP TABLE IF EXISTS `v_stock_total_produits`;

CREATE ALGORITHM=UNDEFINED DEFINER=`facdegsp__1WaEcTx1O5fRo-gxbnXGKdIleAaUM2a5`@`localhost` SQL SECURITY DEFINER VIEW `v_stock_total_produits`  AS SELECT `p`.`id` AS `produit_id`, `p`.`nom` AS `produit_nom`, coalesce(sum(`ps`.`quantite`),0) AS `stock_total`, `p`.`unite_stock_id` AS `unite_stock_id`, `u`.`libelle` AS `unite_stock_libelle` FROM ((`produits` `p` left join `produits_stock` `ps` on(`p`.`id` = `ps`.`produit_id`)) left join `unites` `u` on(`p`.`unite_stock_id` = `u`.`id`)) GROUP BY `p`.`id`, `p`.`nom`, `p`.`unite_stock_id`, `u`.`libelle` ;

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `achat_detail`
--
ALTER TABLE `achat_detail`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_achat_entete` (`achat_entete_id`),
  ADD KEY `idx_produit` (`produit_id`),
  ADD KEY `idx_depot` (`depot_id`),
  ADD KEY `idx_planteur` (`petit_planteur_id`),
  ADD KEY `idx_unite` (`unite_achat_id`);

--
-- Index pour la table `achat_entete`
--
ALTER TABLE `achat_entete`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `reference_achat` (`reference_achat`),
  ADD KEY `idx_fournisseur` (`fournisseur_id`),
  ADD KEY `idx_statut` (`statut`),
  ADD KEY `idx_date_achat` (`date_achat`);

--
-- Index pour la table `categorie`
--
ALTER TABLE `categorie`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `libelle` (`libelle`);

--
-- Index pour la table `clients`
--
ALTER TABLE `clients`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `nif` (`nif`),
  ADD UNIQUE KEY `stat` (`stat`);

--
-- Index pour la table `communes`
--
ALTER TABLE `communes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_nom_commune` (`nom`),
  ADD KEY `idx_region` (`region`);

--
-- Index pour la table `cooperatives`
--
ALTER TABLE `cooperatives`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nom` (`nom`);

--
-- Index pour la table `depot`
--
ALTER TABLE `depot`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `libelle` (`libelle`);

--
-- Index pour la table `forme`
--
ALTER TABLE `forme`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `libelle` (`libelle`);

--
-- Index pour la table `fournisseurs`
--
ALTER TABLE `fournisseurs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `nif` (`nif`),
  ADD UNIQUE KEY `stat` (`stat`);

--
-- Index pour la table `mouvements_stock`
--
ALTER TABLE `mouvements_stock`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_produit_depot` (`produit_id`,`depot_id`),
  ADD KEY `idx_type_mouvement` (`type_mouvement`),
  ADD KEY `idx_date_mouvement` (`date_mouvement`),
  ADD KEY `idx_reference` (`reference_document`);

--
-- Index pour la table `operation_caisse`
--
ALTER TABLE `operation_caisse`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_operation_caisse_achat` (`achat_id`),
  ADD KEY `idx_operation_caisse_fournisseur` (`fournisseur_id`),
  ADD KEY `idx_operation_caisse_date` (`date_paiement`),
  ADD KEY `idx_operation_caisse_type` (`type_operation`);

--
-- Index pour la table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Index pour la table `presentation`
--
ALTER TABLE `presentation`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `libelle` (`libelle`);

--
-- Index pour la table `producteurs`
--
ALTER TABLE `producteurs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code_producteur` (`code_producteur`),
  ADD UNIQUE KEY `cin` (`cin`),
  ADD KEY `idx_classement` (`classement`),
  ADD KEY `idx_site` (`site`);

--
-- Index pour la table `production_globale`
--
ALTER TABLE `production_globale`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_prod_unique_annuelle` (`producteur_id`,`annee`);

--
-- Index pour la table `production_mensuelle`
--
ALTER TABLE `production_mensuelle`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_prod_unique_mensuelle` (`producteur_id`,`annee`,`mois`,`type_production`);

--
-- Index pour la table `produits`
--
ALTER TABLE `produits`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_nom_produit` (`nom`),
  ADD KEY `fk_produits_categorie` (`categorie_id`),
  ADD KEY `fk_produits_unite_vente` (`unite_vente_id`),
  ADD KEY `fk_produits_unite_achat` (`unite_achat_id`),
  ADD KEY `fk_produits_unite_stock` (`unite_stock_id`),
  ADD KEY `fk_produits_presentation` (`presentation_id`),
  ADD KEY `fk_produits_forme` (`forme_id`);

--
-- Index pour la table `produits_stock`
--
ALTER TABLE `produits_stock`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_produit_depot` (`produit_id`,`depot_id`),
  ADD KEY `idx_lot` (`lot_numero`),
  ADD KEY `idx_date_entree` (`date_entree`);

--
-- Index pour la table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Index pour la table `role_permissions`
--
ALTER TABLE `role_permissions`
  ADD PRIMARY KEY (`role_id`,`permission`),
  ADD KEY `permission` (`permission`);

--
-- Index pour la table `unites`
--
ALTER TABLE `unites`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `libelle` (`libelle`);

--
-- Index pour la table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `role_id` (`role_id`);

--
-- Index pour la table `user_permissions`
--
ALTER TABLE `user_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_permission` (`user_id`,`permission_id`),
  ADD KEY `permission_id` (`permission_id`);

--
-- Index pour la table `ventes_details`
--
ALTER TABLE `ventes_details`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_details_vente` (`vente_id`),
  ADD KEY `fk_details_produit` (`produit_id`),
  ADD KEY `fk_details_depot` (`depot_id`);

--
-- Index pour la table `ventes_entete`
--
ALTER TABLE `ventes_entete`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_client` (`client_id`),
  ADD KEY `idx_date_vente` (`date_vente`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `achat_detail`
--
ALTER TABLE `achat_detail`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT pour la table `achat_entete`
--
ALTER TABLE `achat_entete`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT pour la table `categorie`
--
ALTER TABLE `categorie`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT pour la table `clients`
--
ALTER TABLE `clients`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT pour la table `communes`
--
ALTER TABLE `communes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `cooperatives`
--
ALTER TABLE `cooperatives`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `depot`
--
ALTER TABLE `depot`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `forme`
--
ALTER TABLE `forme`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=80;

--
-- AUTO_INCREMENT pour la table `fournisseurs`
--
ALTER TABLE `fournisseurs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `mouvements_stock`
--
ALTER TABLE `mouvements_stock`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `operation_caisse`
--
ALTER TABLE `operation_caisse`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=46;

--
-- AUTO_INCREMENT pour la table `presentation`
--
ALTER TABLE `presentation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT pour la table `producteurs`
--
ALTER TABLE `producteurs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `production_globale`
--
ALTER TABLE `production_globale`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `production_mensuelle`
--
ALTER TABLE `production_mensuelle`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `produits`
--
ALTER TABLE `produits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT pour la table `produits_stock`
--
ALTER TABLE `produits_stock`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT pour la table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT pour la table `unites`
--
ALTER TABLE `unites`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT pour la table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `user_permissions`
--
ALTER TABLE `user_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `ventes_details`
--
ALTER TABLE `ventes_details`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT pour la table `ventes_entete`
--
ALTER TABLE `ventes_entete`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- Contraintes pour les tables déchargéespermissions
--

--
-- Contraintes pour la table `operation_caisse`
--
ALTER TABLE `operation_caisse`
  ADD CONSTRAINT `operation_caisse_ibfk_1` FOREIGN KEY (`achat_id`) REFERENCES `achat_entete` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `operation_caisse_ibfk_2` FOREIGN KEY (`fournisseur_id`) REFERENCES `producteurs` (`id`) ON DELETE SET NULL;

--
-- Contraintes pour la table `production_globale`
--
ALTER TABLE `production_globale`
  ADD CONSTRAINT `fk_prod_globale_producteur` FOREIGN KEY (`producteur_id`) REFERENCES `producteurs` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `production_mensuelle`
--
ALTER TABLE `production_mensuelle`
  ADD CONSTRAINT `fk_prod_mensuelle_producteur` FOREIGN KEY (`producteur_id`) REFERENCES `producteurs` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `produits`
--
ALTER TABLE `produits`
  ADD CONSTRAINT `fk_produits_categorie` FOREIGN KEY (`categorie_id`) REFERENCES `categorie` (`id`),
  ADD CONSTRAINT `fk_produits_forme` FOREIGN KEY (`forme_id`) REFERENCES `forme` (`id`),
  ADD CONSTRAINT `fk_produits_presentation` FOREIGN KEY (`presentation_id`) REFERENCES `presentation` (`id`),
  ADD CONSTRAINT `fk_produits_unite_achat` FOREIGN KEY (`unite_achat_id`) REFERENCES `unites` (`id`),
  ADD CONSTRAINT `fk_produits_unite_stock` FOREIGN KEY (`unite_stock_id`) REFERENCES `unites` (`id`),
  ADD CONSTRAINT `fk_produits_unite_vente` FOREIGN KEY (`unite_vente_id`) REFERENCES `unites` (`id`);

--
-- Contraintes pour la table `role_permissions`
--
ALTER TABLE `role_permissions`
  ADD CONSTRAINT `fk_role_permissions_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`);

--
-- Contraintes pour la table `user_permissions`
--
ALTER TABLE `user_permissions`
  ADD CONSTRAINT `user_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `ventes_details`
--
ALTER TABLE `ventes_details`
  ADD CONSTRAINT `fk_details_depot` FOREIGN KEY (`depot_id`) REFERENCES `depot` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_details_produit` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_details_vente` FOREIGN KEY (`vente_id`) REFERENCES `ventes_entete` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `ventes_entete`
--
ALTER TABLE `ventes_entete`
  ADD CONSTRAINT `fk_ventes_client` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

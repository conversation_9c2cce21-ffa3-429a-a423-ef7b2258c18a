/**
 * Gestion des Producteurs
 */

let currentProducteurId = null;
let currentCommuneId = null;
let selectedSiteId = null;

$(document).ready(function() {
    // Initialisation
    loadSites();
    loadProducteurs();
    loadStatistics();
    
    // Event listeners
    setupEventListeners();
});

function setupEventListeners() {
    // Boutons producteurs
    $('#btnSaveProducteur').on('click', saveProducteur);
    $('#btnRefreshProducteurs').on('click', function() {
        loadProducteurs();
        loadStatistics();
    });
    
    // Boutons communes
    $('#btnSaveCommune').on('click', saveCommune);
    
    // Filtres
    $('#filterSites').on('input', filterSites);
    $('#filterProducteurs').on('input', filterProducteurs);
    $('#filterClassement').on('change', filterProducteurs);
    $('#btnResetFilters').on('click', resetFilters);
    
    // Preview photo
    $('#photoProducteur').on('change', previewPhoto);
    
    // Event delegation pour les boutons dynamiques
    $(document).on('click', '.btn-edit-producteur', function() {
        const id = $(this).data('id');
        editProducteur(id);
    });
    
    $(document).on('click', '.btn-delete-producteur', function() {
        const id = $(this).data('id');
        deleteProducteur(id);
    });
    
    $(document).on('click', '.btn-change-classification', function() {
        const id = $(this).data('id');
        const currentClass = $(this).data('current-class');
        changeClassification(id, currentClass);
    });
    
    $(document).on('click', '.btn-edit-commune', function() {
        const id = $(this).data('id');
        editCommune(id);
    });
    
    $(document).on('click', '.btn-delete-commune', function() {
        const id = $(this).data('id');
        deleteCommune(id);
    });
    
    $(document).on('click', '.site-card', function() {
        const id = $(this).data('id');
        selectSite(id);
    });
}

// ===== GESTION DES SITES =====

function loadSites() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'communes',
        data: JSON.stringify({})
    }).done(function(response) {
        if (response.success) {
            displaySites(response.data);
            loadSitesForSelect();
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de charger les sites', 'error');
    });
}

function displaySites(sites) {
    let html = '';
    sites.forEach(site => {
        const isActive = selectedSiteId == site.id ? 'active' : '';
        html += `
            <div class="site-card ${isActive}" data-id="${site.id}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${site.nom}</strong>
                        <br>
                        <small class="text-muted">${site.region || 'N/A'} - ${site.pays || 'Madagascar'}</small>
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-warning btn-edit-commune" data-id="${site.id}" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger btn-delete-commune" data-id="${site.id}" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    $('#sitesList').html(html);
}

function loadSitesForSelect() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'communes',
        data: JSON.stringify({})
    }).done(function(response) {
        if (response.success) {
            const select = $('#siteProducteur');
            select.empty();
            select.append('<option value="">Sélectionner un site</option>');
            
            response.data.forEach(site => {
                select.append(`<option value="${site.id}">${site.nom} - ${site.region || 'N/A'}</option>`);
            });
        }
    });
}

function selectSite(siteId) {
    selectedSiteId = parseInt(siteId); // S'assurer que c'est un entier
    $('.site-card').removeClass('active');
    $(`.site-card[data-id="${siteId}"]`).addClass('active');
    
    // Filtrer les producteurs par site
    loadProducteurs();
}

function filterSites() {
    const filter = $('#filterSites').val().toLowerCase();
    $('.site-card').each(function() {
        const siteName = $(this).find('strong').text().toLowerCase();
        const region = $(this).find('small').text().toLowerCase();
        
        if (siteName.includes(filter) || region.includes(filter)) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}

// ===== GESTION DES PRODUCTEURS =====

function loadProducteurs() {
    let sql = `SELECT 
                p.*,
                c.nom as site_nom,
                c.region as site_region
              FROM producteurs p
              LEFT JOIN communes c ON p.site = c.nom
              WHERE 1=1`;
    
    const params = [];
    
    if (selectedSiteId) {
        sql += ` AND site = ?`;
        params.push(parseInt(selectedSiteId)); // S'assurer que c'est un entier
    }
    
    sql += ` ORDER BY p.classement DESC, p.nom ASC`;
    
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'producteurs',
        sql: sql,
        params: JSON.stringify(params)
    }).done(function(response) {
        if (response.success) {
            displayProducteurs(response.data);
        } else {
            console.error('Erreur SQL:', response.message);
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur AJAX:', error, xhr.responseText);
        showAlert('Erreur', 'Impossible de charger les producteurs', 'error');
    });
}

function displayProducteurs(producteurs) {
    const leadFarmers = producteurs.filter(p => p.classement === 'LEAD FARMER');
    const petitsPlanteurs = producteurs.filter(p => p.classement === 'PETIT PLANTEUR');
    const freelance = producteurs.filter(p => p.classement === 'FREELANCE');
    
    displayProducteursByClass('leadFarmersList', leadFarmers);
    displayProducteursByClass('petitsPlanteursList', petitsPlanteurs);
    displayProducteursByClass('freelanceList', freelance);
}

function displayProducteursByClass(containerId, producteurs) {
    let html = '';
    
    if (producteurs.length === 0) {
        html = '<div class="col-12"><p class="text-muted text-center">Aucun producteur trouvé</p></div>';
    } else {
        producteurs.forEach(producteur => {
            html += generateProducteurCard(producteur);
        });
    }
    
    $(`#${containerId}`).html(html);
}

function generateProducteurCard(producteur) {
    const photoHtml = producteur.photo ? 
        `<img src="../includes/uploads/producteurs/${producteur.photo}" class="producer-photo" alt="Photo">` :
        `<div class="producer-photo-placeholder"><i class="fas fa-user"></i></div>`;
    
    const classificationBadge = getClassificationBadge(producteur.classement);
    const newClassification = getNewClassification(producteur.classement);
    
    return `
        <div class="col-md-6 col-lg-4">
            <div class="producer-card">
                <div class="d-flex">
                    <div class="me-3">
                        ${photoHtml}
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">${producteur.nom}</h6>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-outline-warning btn-edit-producteur" data-id="${producteur.id}" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger btn-delete-producteur" data-id="${producteur.id}" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">
                                <strong>Code:</strong> ${producteur.code_producteur}<br>
                                <strong>Site:</strong> ${producteur.site_nom || 'N/A'}<br>
                                <strong>Contact:</strong> ${producteur.telephone || 'N/A'}
                            </small>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="classification-badge ${producteur.classement.toLowerCase().replace(' ', '-')}">
                                ${producteur.classement}
                            </span>
                            ${newClassification ? `
                                <button class="btn btn-sm btn-outline-primary btn-change-classification" 
                                        data-id="${producteur.id}" 
                                        data-current-class="${producteur.classement}"
                                        title="Changer en ${newClassification}">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getClassificationBadge(classement) {
    const badges = {
        'LEAD FARMER': 'lead-farmer',
        'PETIT PLANTEUR': 'petit-planteur',
        'FREELANCE': 'freelance'
    };
    return badges[classement] || '';
}

function getNewClassification(currentClass) {
    const changes = {
        'LEAD FARMER': 'PETIT PLANTEUR',
        'PETIT PLANTEUR': 'LEAD FARMER',
        'FREELANCE': 'PETIT PLANTEUR'
    };
    return changes[currentClass];
}

function filterProducteurs() {
    const classementFilter = $('#filterClassement').val();
    const textFilter = $('#filterProducteurs').val().toLowerCase();
    
    $('.producer-card').each(function() {
        const card = $(this);
        const producteurClass = card.find('.classification-badge').text().trim();
        const producteurName = card.find('h6').text().toLowerCase();
        const producteurCode = card.find('small').text().toLowerCase();
        
        let show = true;
        
        if (classementFilter && producteurClass !== classementFilter) {
            show = false;
        }
        
        if (textFilter && !producteurName.includes(textFilter) && !producteurCode.includes(textFilter)) {
            show = false;
        }
        
        if (show) {
            card.closest('.col-md-6').show();
        } else {
            card.closest('.col-md-6').hide();
        }
    });
}

function resetFilters() {
    $('#filterClassement').val('');
    $('#filterProducteurs').val('');
    selectedSiteId = null;
    $('.site-card').removeClass('active');
    loadProducteurs();
}

// ===== CRUD PRODUCTEURS =====

function saveProducteur() {
    const formData = new FormData();
    const data = {
        code_producteur: $('#codeProducteur').val(),
        nom: $('#nomProducteur').val(),
        code_leads: $('#codeLeads').val(),
        genre: $('#genreProducteur').val(),
        cin: $('#cinProducteur').val(),
        date_naissance: $('#dateNaissance').val(),
        telephone: $('#telephoneProducteur').val(),
        email: $('#emailProducteur').val(),
        site: $('#siteProducteur').val(),
        classement: $('#classementProducteur').val(),
        adresse: $('#adresseProducteur').val()
    };
    
    // Ajouter les paramètres de base
    formData.append('action', currentProducteurId ? 'update' : 'create');
    formData.append('table', 'producteurs');
    if (currentProducteurId) {
        formData.append('id', currentProducteurId);
    }
    
    // Ajouter les données
    formData.append('data', JSON.stringify(data));
    
    // Ajouter la photo si elle existe
    const photoFile = $('#photoProducteur')[0].files[0];
    if (photoFile) {
        formData.append('photo', photoFile);
    }
    
    $.ajax({
        url: '../includes/traitement.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                showAlert('Succès', 'Producteur enregistré avec succès', 'success');
                $('#modalProducteur').modal('hide');
                resetProducteurForm();
                loadProducteurs();
                loadStatistics();
            } else {
                showAlert('Erreur', response.message, 'error');
            }
        },
        error: function() {
            showAlert('Erreur', 'Impossible d\'enregistrer le producteur', 'error');
        }
    });
}

function editProducteur(id) {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'producteurs',
        data: JSON.stringify({ id: id })
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const producteur = response.data[0];
            currentProducteurId = id;
            
            // Remplir le formulaire
            $('#codeProducteur').val(producteur.code_producteur);
            $('#nomProducteur').val(producteur.nom);
            $('#codeLeads').val(producteur.code_leads);
            $('#genreProducteur').val(producteur.genre);
            $('#cinProducteur').val(producteur.cin);
            $('#dateNaissance').val(producteur.date_naissance);
            $('#telephoneProducteur').val(producteur.telephone);
            $('#emailProducteur').val(producteur.email);
            $('#siteProducteur').val(producteur.site);
            $('#classementProducteur').val(producteur.classement);
            $('#adresseProducteur').val(producteur.adresse);
            
            // Afficher la photo existante
            if (producteur.photo) {
                $('#photoPreview').html(`<img src="../includes/uploads/producteurs/${producteur.photo}" class="producer-photo" alt="Photo">`);
            } else {
                $('#photoPreview').html('<div class="producer-photo-placeholder"><i class="fas fa-user"></i></div>');
            }
            
            // Changer le titre du modal
            $('#modalProducteurTitle').html('<i class="fas fa-user-edit"></i> Modifier le Producteur');
            $('#btnSaveProducteurText').text('Mettre à jour');
            
            $('#modalProducteur').modal('show');
        }
    });
}

function deleteProducteur(id) {
    Swal.fire({
        title: 'Supprimer le producteur',
        text: 'Êtes-vous sûr de vouloir supprimer ce producteur ?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Oui, supprimer',
        cancelButtonText: 'Annuler',
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6'
    }).then((result) => {
        if (result.isConfirmed) {
            $.post('../includes/traitement.php', {
                action: 'delete',
                table: 'producteurs',
                id: id
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', 'Producteur supprimé avec succès', 'success');
                    loadProducteurs();
                    loadStatistics();
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Impossible de supprimer le producteur', 'error');
            });
        }
    });
}

function changeClassification(id, currentClass) {
    const newClass = getNewClassification(currentClass);
    
    Swal.fire({
        title: 'Changer le classement',
        text: `Changer ce producteur de "${currentClass}" vers "${newClass}" ?`,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Oui, changer',
        cancelButtonText: 'Annuler',
        confirmButtonColor: '#007bff',
        cancelButtonColor: '#6c757d'
    }).then((result) => {
        if (result.isConfirmed) {
            $.post('../includes/traitement.php', {
                action: 'update',
                table: 'producteurs',
                id: id,
                data: JSON.stringify({ classement: newClass })
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', 'Classement modifié avec succès', 'success');
                    loadProducteurs();
                    loadStatistics();
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Impossible de modifier le classement', 'error');
            });
        }
    });
}

function resetProducteurForm() {
    currentProducteurId = null;
    $('#formProducteur')[0].reset();
    $('#photoPreview').html('<div class="producer-photo-placeholder"><i class="fas fa-user"></i></div>');
    $('#modalProducteurTitle').html('<i class="fas fa-user-plus"></i> Nouveau Producteur');
    $('#btnSaveProducteurText').text('Enregistrer');
}

function previewPhoto() {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            $('#photoPreview').html(`<img src="${e.target.result}" class="producer-photo" alt="Photo">`);
        };
        reader.readAsDataURL(file);
    }
}

// ===== CRUD COMMUNES =====

function saveCommune() {
    const data = {
        nom: $('#nomCommune').val(),
        code_postal: $('#codePostal').val(),
        region: $('#regionCommune').val(),
        pays: $('#paysCommune').val()
    };
    
    const action = currentCommuneId ? 'update' : 'create';
    const params = currentCommuneId ? 
        { action: 'update', table: 'communes', id: currentCommuneId, data: JSON.stringify(data) } :
        { action: 'create', table: 'communes', data: JSON.stringify(data) };
    
    $.post('../includes/traitement.php', params).done(function(response) {
        if (response.success) {
            showAlert('Succès', 'Commune enregistrée avec succès', 'success');
            $('#modalCommune').modal('hide');
            resetCommuneForm();
            loadSites();
            loadStatistics();
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible d\'enregistrer la commune', 'error');
    });
}

function editCommune(id) {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'communes',
        data: JSON.stringify({ id: id })
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const commune = response.data[0];
            currentCommuneId = id;
            
            $('#nomCommune').val(commune.nom);
            $('#codePostal').val(commune.code_postal);
            $('#regionCommune').val(commune.region);
            $('#paysCommune').val(commune.pays);
            
            $('#modalCommuneTitle').html('<i class="fas fa-map-marker-alt"></i> Modifier la Commune');
            $('#btnSaveCommuneText').text('Mettre à jour');
            
            $('#modalCommune').modal('show');
        }
    });
}

function deleteCommune(id) {
    // Vérifier si la commune est utilisée par des producteurs
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'producteurs',
        sql: 'SELECT COUNT(*) as count FROM producteurs p JOIN communes c ON p.site = c.nom WHERE c.id = ?',
        params: JSON.stringify([id])
    }).done(function(response) {
        if (response.success && response.data[0].count > 0) {
            showAlert('Erreur', 'Cette commune ne peut pas être supprimée car elle est utilisée par des producteurs', 'error');
            return;
        }
        
        Swal.fire({
            title: 'Supprimer la commune',
            text: 'Êtes-vous sûr de vouloir supprimer cette commune ?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6'
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'delete',
                    table: 'communes',
                    id: id
                }).done(function(response) {
                    if (response.success) {
                        showAlert('Succès', 'Commune supprimée avec succès', 'success');
                        loadSites();
                        loadStatistics();
                    } else {
                        showAlert('Erreur', response.message, 'error');
                    }
                }).fail(function() {
                    showAlert('Erreur', 'Impossible de supprimer la commune', 'error');
                });
            }
        });
    });
}

function resetCommuneForm() {
    currentCommuneId = null;
    $('#formCommune')[0].reset();
    $('#paysCommune').val('Madagascar');
    $('#regionCommune').val('DIANA');
    $('#modalCommuneTitle').html('<i class="fas fa-map-marker-alt"></i> Nouvelle Commune');
    $('#btnSaveCommuneText').text('Enregistrer');
}

// ===== STATISTIQUES =====

function loadStatistics() {
    // Statistiques des sites
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'communes',
        sql: 'SELECT COUNT(*) as count FROM communes',
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            $('#statTotalSites').text(response.data[0].count);
        }
    });
    
    // Statistiques des producteurs
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'producteurs',
        sql: `SELECT 
                classement,
                COUNT(*) as count
              FROM producteurs
              GROUP BY classement`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            let leadFarmers = 0;
            let petitsPlanteurs = 0;
            let freelance = 0;
            let total = 0;
            
            response.data.forEach(stat => {
                total += parseInt(stat.count);
                switch(stat.classement) {
                    case 'LEAD FARMER':
                        leadFarmers = stat.count;
                        break;
                    case 'PETIT PLANTEUR':
                        petitsPlanteurs = stat.count;
                        break;
                    case 'FREELANCE':
                        freelance = stat.count;
                        break;
                }
            });
            
            $('#statLeadFarmers').text(leadFarmers);
            $('#statPetitsPlanteurs').text(petitsPlanteurs);
            $('#statTotalProducteurs').text(total);
        }
    });
}

// ===== FONCTIONS D'ALERTE =====

function showAlert(title, message, type) {
    const iconMap = { 'success': 'success', 'error': 'error', 'warning': 'warning', 'info': 'info', 'danger': 'error' };
    
    Swal.fire({
        title: title,
        text: message,
        icon: iconMap[type] || 'info',
        confirmButtonText: 'OK',
        confirmButtonColor: '#007bff'
    });
}

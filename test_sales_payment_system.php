<?php
/**
 * Test complet du système de paiements de ventes
 * Ce script teste le workflow complet: Vente → Facturation → Paiement
 */

require_once 'config/config.php';
require_once CONFIG_PATH . '/database.php';

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<h2>✅ Connexion à la base de données réussie</h2>";
} catch (PDOException $e) {
    die("<h2>❌ Erreur de connexion : " . $e->getMessage() . "</h2>");
}

echo "<h1>Test Complet du Système de Paiements de Ventes</h1>";

// 1. Vérifier la structure de la table operation_caisse
echo "<h3>1. Vérification de la structure de la table operation_caisse</h3>";

try {
    $stmt = $pdo->query("SHOW COLUMNS FROM operation_caisse");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredColumns = ['vente_id', 'type_operation', 'montant', 'date_paiement', 'mode_paiement', 'reference_paiement', 'statut', 'utilisateur'];
    $missingColumns = array_diff($requiredColumns, $columns);
    
    if (empty($missingColumns)) {
        echo "✅ Table operation_caisse correctement structurée<br>";
        echo "Colonnes disponibles: " . implode(', ', $columns) . "<br>";
    } else {
        echo "❌ Colonnes manquantes: " . implode(', ', $missingColumns) . "<br>";
        echo "⚠️ Exécutez le script d'extension de la table operation_caisse<br>";
    }
} catch (PDOException $e) {
    echo "❌ Erreur lors de la vérification : " . $e->getMessage() . "<br>";
}

// 2. Vérifier les statuts de ventes
echo "<h3>2. Vérification des statuts de ventes</h3>";

try {
    $stmt = $pdo->query("SHOW COLUMNS FROM ventes_entete LIKE 'statut'");
    $statutColumn = $stmt->fetch();
    
    if ($statutColumn) {
        echo "✅ Colonne statut trouvée: " . $statutColumn['Type'] . "<br>";
        
        // Vérifier si les nouveaux statuts sont présents
        if (strpos($statutColumn['Type'], 'EN COURS') !== false && 
            strpos($statutColumn['Type'], 'FACTURE') !== false && 
            strpos($statutColumn['Type'], 'PAYE') !== false) {
            echo "✅ Nouveaux statuts de ventes configurés correctement<br>";
        } else {
            echo "❌ Statuts de ventes non mis à jour. Statuts actuels: " . $statutColumn['Type'] . "<br>";
        }
    }
} catch (PDOException $e) {
    echo "❌ Erreur lors de la vérification des statuts : " . $e->getMessage() . "<br>";
}

// 3. Créer une vente de test complète
echo "<h3>3. Test du workflow complet de vente et paiement</h3>";

try {
    // Récupérer ou créer un client
    $stmt = $pdo->query("SELECT id, nom FROM clients LIMIT 1");
    $client = $stmt->fetch();
    
    if (!$client) {
        echo "Création d'un client de test...<br>";
        $pdo->exec("INSERT INTO clients (nom, type_client, adresse, telephone, email) VALUES 
                   ('Client Test Paiement', 'EXPORTATEUR', '789 Rue Paiement', '+261 34 11 22 33', '<EMAIL>')");
        
        $stmt = $pdo->query("SELECT id, nom FROM clients WHERE nom = 'Client Test Paiement'");
        $client = $stmt->fetch();
    }
    
    // Récupérer un produit
    $stmt = $pdo->query("SELECT id, nom, prix_unitaire FROM produits LIMIT 1");
    $produit = $stmt->fetch();
    
    if (!$produit) {
        echo "Création d'un produit de test...<br>";
        $pdo->exec("INSERT INTO produits (nom, prix_unitaire, unite) VALUES 
                   ('Cacao Premium Test', 3000.00, 'kg')");
        
        $stmt = $pdo->query("SELECT id, nom, prix_unitaire FROM produits WHERE nom = 'Cacao Premium Test'");
        $produit = $stmt->fetch();
    }
    
    echo "✅ Client: {$client['nom']} (ID: {$client['id']})<br>";
    echo "✅ Produit: {$produit['nom']} (ID: {$produit['id']})<br>";
    
    // Étape 1: Créer une vente avec statut EN COURS
    $reference = 'VTE-PAY-TEST-' . date('YmdHis');
    $dateVente = date('Y-m-d');
    $totalMontant = 6000.00; // 2 tonnes à 3000 Ar
    $coursDevise = 4875.40;
    $valeurEur = $totalMontant / $coursDevise;
    
    echo "<h4>Étape 1: Création de la vente (EN COURS)</h4>";
    
    $stmt = $pdo->prepare("INSERT INTO ventes_entete (
        client_id, total_montant, date_vente, statut, valeur_euro, valeur_ar, 
        cours_devise, facture_numero, facture_date, cree_par
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $result = $stmt->execute([
        $client['id'], $totalMontant, $dateVente, 'EN COURS', 
        $valeurEur, $totalMontant, $coursDevise, $reference, $dateVente, 'system'
    ]);
    
    if ($result) {
        $venteId = $pdo->lastInsertId();
        echo "✅ Vente créée avec succès (ID: $venteId, Statut: EN COURS)<br>";
        
        // Étape 2: Mettre la vente EN ATTENTE
        echo "<h4>Étape 2: Mise en attente de la vente</h4>";
        
        $stmt = $pdo->prepare("UPDATE ventes_entete SET statut = 'EN ATTENTE', dernier_modif_par = 'system' WHERE id = ?");
        $stmt->execute([$venteId]);
        echo "✅ Vente mise en attente<br>";
        
        // Étape 3: Facturer la vente
        echo "<h4>Étape 3: Facturation de la vente</h4>";
        
        $stmt = $pdo->prepare("UPDATE ventes_entete SET statut = 'FACTURE', dernier_modif_par = 'system' WHERE id = ?");
        $stmt->execute([$venteId]);
        echo "✅ Vente facturée (prête pour paiement)<br>";
        
        // Étape 4: Enregistrer un paiement
        echo "<h4>Étape 4: Enregistrement du paiement</h4>";
        
        // Vérifier si la colonne vente_id existe
        $stmt = $pdo->query("SHOW COLUMNS FROM operation_caisse LIKE 'vente_id'");
        if ($stmt->rowCount() > 0) {
            $referencePaiement = 'PAY-VTE-' . date('YmdHis');
            
            $stmt = $pdo->prepare("INSERT INTO operation_caisse (
                type_operation, vente_id, montant, date_paiement, mode_paiement, 
                reference_paiement, commentaires, utilisateur, statut
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            
            $result = $stmt->execute([
                'ENCAISSEMENT_VENTE', $venteId, $totalMontant, $dateVente, 'VIREMENT',
                $referencePaiement, 'Test paiement vente automatique', 'system', 'EN_ATTENTE'
            ]);
            
            if ($result) {
                $paiementId = $pdo->lastInsertId();
                echo "✅ Paiement enregistré (ID: $paiementId, Statut: EN_ATTENTE)<br>";
                
                // Étape 5: Valider le paiement et mettre la vente PAYE
                echo "<h4>Étape 5: Validation du paiement</h4>";
                
                $stmt = $pdo->prepare("UPDATE operation_caisse SET statut = 'VALIDE', date_validation = NOW() WHERE id = ?");
                $stmt->execute([$paiementId]);
                
                $stmt = $pdo->prepare("UPDATE ventes_entete SET statut = 'PAYE', dernier_modif_par = 'system' WHERE id = ?");
                $stmt->execute([$venteId]);
                
                echo "✅ Paiement validé et vente marquée comme PAYÉE<br>";
                
                // Résumé final
                echo "<h4>Résumé du workflow complet:</h4>";
                echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
                echo "<ul>";
                echo "<li><strong>Vente ID:</strong> $venteId</li>";
                echo "<li><strong>Référence:</strong> $reference</li>";
                echo "<li><strong>Client:</strong> {$client['nom']}</li>";
                echo "<li><strong>Montant:</strong> " . number_format($totalMontant, 2) . " Ar</li>";
                echo "<li><strong>Statut Final:</strong> PAYE</li>";
                echo "<li><strong>Paiement ID:</strong> $paiementId</li>";
                echo "<li><strong>Référence Paiement:</strong> $referencePaiement</li>";
                echo "<li><strong>Mode Paiement:</strong> VIREMENT</li>";
                echo "</ul>";
                echo "</div>";
                
            } else {
                echo "❌ Erreur lors de l'enregistrement du paiement<br>";
            }
        } else {
            echo "❌ Colonne vente_id manquante dans operation_caisse<br>";
        }
        
    } else {
        echo "❌ Erreur lors de la création de la vente<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur lors du test : " . $e->getMessage() . "<br>";
}

// 4. Statistiques finales
echo "<h3>4. Statistiques du système</h3>";

try {
    // Compter les ventes par statut
    $stmt = $pdo->query("SELECT statut, COUNT(*) as count FROM ventes_entete GROUP BY statut");
    $ventesStats = $stmt->fetchAll();
    
    echo "<h5>Ventes par statut:</h5>";
    foreach ($ventesStats as $stat) {
        echo "- {$stat['statut']}: {$stat['count']}<br>";
    }
    
    // Compter les paiements par statut
    $stmt = $pdo->query("SELECT statut, COUNT(*) as count FROM operation_caisse WHERE type_operation = 'ENCAISSEMENT_VENTE' GROUP BY statut");
    $paiementsStats = $stmt->fetchAll();
    
    echo "<h5>Paiements de ventes par statut:</h5>";
    foreach ($paiementsStats as $stat) {
        echo "- {$stat['statut']}: {$stat['count']}<br>";
    }
    
    // Montant total des paiements validés
    $stmt = $pdo->query("SELECT SUM(montant) as total FROM operation_caisse WHERE type_operation = 'ENCAISSEMENT_VENTE' AND statut = 'VALIDE'");
    $totalPaiements = $stmt->fetch()['total'] ?? 0;
    
    echo "<h5>Montant total des paiements validés:</h5>";
    echo number_format($totalPaiements, 2) . " Ar<br>";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors du calcul des statistiques : " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>✅ Test complet du système de paiements terminé avec succès!</h3>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h5>🚀 Accès aux pages:</h5>";
echo "<p><a href='pages/gestion_ventes.php' class='btn btn-primary' style='margin-right: 10px;'>📊 Gestion des Ventes</a></p>";
echo "<p><a href='pages/gestion_paiements_ventes.php' class='btn btn-success'>💳 Gestion des Paiements de Ventes</a></p>";
echo "</div>";
echo "<p><small>Test effectué le " . date('Y-m-d H:i:s') . "</small></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Système Paiements Ventes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .btn-primary { background: #007bff; }
        .btn-success { background: #28a745; }
        .btn:hover { opacity: 0.8; }
        ul { background: #f8f9fa; padding: 15px; border-radius: 5px; }
        h4 { color: #0056b3; margin-top: 20px; }
    </style>
</head>
<body>
    <!-- Le contenu PHP est affiché ci-dessus -->
</body>
</html>

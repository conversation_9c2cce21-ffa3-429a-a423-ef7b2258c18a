-- ===================================================================
-- CRÉATION DES TABLES POUR LA GESTION DES ACHATS
-- ===================================================================

-- Table achat_entete (en-tête des achats)
CREATE TABLE IF NOT EXISTS `achat_entete` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `reference_achat` varchar(50) NOT NULL UNIQUE,
  `fournisseur_id` int(11) NOT NULL,
  `date_achat` date DEFAULT NULL,
  `date_livraison` date DEFAULT NULL,
  `statut` enum('SAISIE','LIVRE','CONTROLE','A_PAYER','PAYE','ANNULE') NOT NULL DEFAULT 'SAISIE',
  `frais_transport` decimal(12,2) DEFAULT 0.00,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_fournisseur` (`fournisseur_id`),
  KEY `idx_statut` (`statut`),
  KEY `idx_date_achat` (`date_achat`),
  CONSTRAINT `fk_achat_fournisseur` FOREIGN KEY (`fournisseur_id`) REFERENCES `producteurs` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table achat_detail (détails des achats)
CREATE TABLE IF NOT EXISTS `achat_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `achat_entete_id` int(11) NOT NULL,
  `produit_id` int(11) NOT NULL,
  `depot_id` int(11) DEFAULT NULL,
  `petit_planteur_id` int(11) DEFAULT NULL,
  `unite_achat_id` int(11) NOT NULL,
  `qte_brute_saisie` decimal(10,2) DEFAULT 0.00,
  `qte_nette_controlee` decimal(10,2) DEFAULT 0.00,
  `prix_unitaire_net` decimal(12,2) DEFAULT 0.00,
  `montant_ht` decimal(12,2) DEFAULT 0.00,
  `reduction` decimal(12,2) DEFAULT 0.00,
  `stock_avant_entree` decimal(10,2) DEFAULT 0.00,
  `lot_numero` varchar(100) DEFAULT NULL,
  `nombre_sacs` decimal(10,2) DEFAULT 0.00,
  `ecart_controle` decimal(10,2) DEFAULT 0.00,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_achat_entete` (`achat_entete_id`),
  KEY `idx_produit` (`produit_id`),
  KEY `idx_depot` (`depot_id`),
  KEY `idx_planteur` (`petit_planteur_id`),
  KEY `idx_unite` (`unite_achat_id`),
  CONSTRAINT `fk_detail_achat_entete` FOREIGN KEY (`achat_entete_id`) REFERENCES `achat_entete` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_detail_produit` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_detail_depot` FOREIGN KEY (`depot_id`) REFERENCES `depot` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_detail_planteur` FOREIGN KEY (`petit_planteur_id`) REFERENCES `producteurs` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_detail_unite` FOREIGN KEY (`unite_achat_id`) REFERENCES `unites` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table produits_stock (stock des produits par dépôt)
CREATE TABLE IF NOT EXISTS `produits_stock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `produit_id` int(11) NOT NULL,
  `depot_id` int(11) NOT NULL,
  `quantite` decimal(10,2) NOT NULL DEFAULT 0.00,
  `unite_stock_id` int(11) NOT NULL,
  `lot_numero` varchar(100) DEFAULT NULL,
  `date_entree` datetime DEFAULT current_timestamp(),
  `date_expiration` date DEFAULT NULL,
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_produit_depot` (`produit_id`, `depot_id`),
  KEY `idx_lot` (`lot_numero`),
  KEY `idx_date_entree` (`date_entree`),
  CONSTRAINT `fk_stock_produit` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_stock_depot` FOREIGN KEY (`depot_id`) REFERENCES `depot` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_stock_unite` FOREIGN KEY (`unite_stock_id`) REFERENCES `unites` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table mouvements_stock (historique des mouvements de stock)
CREATE TABLE IF NOT EXISTS `mouvements_stock` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `produit_id` int(11) NOT NULL,
  `depot_id` int(11) NOT NULL,
  `type_mouvement` enum('ENTREE','SORTIE','AJUSTEMENT','TRANSFERT') NOT NULL,
  `quantite` decimal(10,2) NOT NULL,
  `unite_id` int(11) NOT NULL,
  `reference_document` varchar(100) DEFAULT NULL,
  `lot_numero` varchar(100) DEFAULT NULL,
  `motif` text DEFAULT NULL,
  `date_mouvement` datetime NOT NULL DEFAULT current_timestamp(),
  `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
  `cree_par` varchar(255) DEFAULT NULL,
  `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `dernier_modif_par` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_produit_depot` (`produit_id`, `depot_id`),
  KEY `idx_type_mouvement` (`type_mouvement`),
  KEY `idx_date_mouvement` (`date_mouvement`),
  KEY `idx_reference` (`reference_document`),
  CONSTRAINT `fk_mouvement_produit` FOREIGN KEY (`produit_id`) REFERENCES `produits` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_mouvement_depot` FOREIGN KEY (`depot_id`) REFERENCES `depot` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_mouvement_unite` FOREIGN KEY (`unite_id`) REFERENCES `unites` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Vue pour le stock total par produit
CREATE OR REPLACE VIEW `v_stock_total_produits` AS
SELECT 
    p.id as produit_id,
    p.nom as produit_nom,
    COALESCE(SUM(ps.quantite), 0) as stock_total,
    p.unite_stock_id,
    u.libelle as unite_stock_libelle
FROM produits p
LEFT JOIN produits_stock ps ON p.id = ps.produit_id
LEFT JOIN unites u ON p.unite_stock_id = u.id
GROUP BY p.id, p.nom, p.unite_stock_id, u.libelle;

-- Insertion de quelques données de test pour les dépôts si nécessaire
INSERT IGNORE INTO `depot` (`id`, `libelle`, `est_actif`) VALUES
(1, 'MAGASIN N°01', 1),
(2, 'MAGASIN N°02', 1),
(3, 'MAGASIN N°03', 1);

-- Insertion de quelques données de test pour les unités si nécessaire
INSERT IGNORE INTO `unites` (`id`, `libelle`, `est_actif`, `date_creation`, `cree_par`, `date_derniere_modif`, `dernier_modif_par`) VALUES
(1, 'POUDRE', 1, NOW(), 'system', NOW(), 'system'),
(2, 'SAC', 1, NOW(), 'system', NOW(), 'system'),
(3, 'KG', 1, NOW(), 'system', NOW(), 'system'),
(6, 'TONNE', 1, NOW(), 'system', NOW(), 'system'),
(7, 'BOITE', 1, NOW(), 'system', NOW(), 'system'),
(8, 'UNITE', 1, NOW(), 'system', NOW(), 'system');

COMMIT;
<?php
session_start();
require_once dirname(__DIR__) . '/config/config.php';
require_once CONFIG_PATH . '/database.php';
require_once INCLUDES_PATH . '/auth.php';
require_once INCLUDES_PATH . '/rbac.php';

// Vérification de l'authentification
$auth = new Auth();
$auth->checkAuth();

// Vérification des permissions
$rbac = new RBAC();
$rbac->requirePermission('producteurs.view');

$currentUser = $_SESSION['username'];
?>
<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Gestion des Producteurs</title>
    <!-- Bootstrap Cerulean -->
    <link href="https://cdn.jsdelivr.net/npm/bootswatch@5.3.2/dist/cerulean/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- CSS personnalisé -->
    <style>
        .producer-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .producer-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .producer-photo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .producer-photo-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #dee2e6;
            color: #6c757d;
            font-size: 24px;
        }
        .classification-badge {
            font-size: 0.75em;
            padding: 0.4em 0.8em;
            border-radius: 20px;
        }
        .lead-farmer {
            background-color: #dc3545;
            color: white;
        }
        .petit-planteur {
            background-color: #28a745;
            color: white;
        }
        .freelance {
            background-color: #ffc107;
            color: #212529;
        }
        .site-card {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .site-card:hover {
            background: #e9ecef;
            border-color: #007bff;
        }
        .site-card.active {
            background: #007bff;
            color: white;
            border-color: #0056b3;
        }
        .action-buttons {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
    </style>
</head>

<body>
    <div class="container-fluid p-3">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="mb-0">
                <i class="fas fa-users"></i> Gestion des Producteurs
            </h4>
            <div class="btn-group">
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalProducteur" title="Nouveau producteur">
                    <i class="fas fa-plus"></i> Nouveau Producteur
                </button>
                <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#modalCommune" title="Nouvelle commune">
                    <i class="fas fa-map-marker-alt"></i> Nouvelle Commune
                </button>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="d-flex align-items-center p-2 border rounded">
                    <div class="h4 mb-0 me-3" id="statTotalSites">0</div>
                    <div class="text-muted">Total Sites</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center p-2 border rounded">
                    <div class="h4 mb-0 me-3" id="statLeadFarmers">0</div>
                    <div class="text-muted">Lead Farmers</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center p-2 border rounded">
                    <div class="h4 mb-0 me-3" id="statPetitsPlanteurs">0</div>
                    <div class="text-muted">Petits Planteurs</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="d-flex align-items-center p-2 border rounded">
                    <div class="h4 mb-0 me-3" id="statTotalProducteurs">0</div>
                    <div class="text-muted">Total Producteurs</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Liste des Sites -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Sites (Communes)</h6>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalCommune" title="Ajouter">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-2">
                        <div class="mb-2">
                            <input type="text" id="filterSites" class="form-control form-control-sm" placeholder="Filtrer les sites...">
                        </div>
                        <div id="sitesList" style="max-height: 500px; overflow-y: auto;">
                            <!-- Sites chargés dynamiquement -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des Producteurs -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Liste des Producteurs</h6>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalProducteur" title="Ajouter">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="btn btn-outline-secondary" id="btnRefreshProducteurs" title="Actualiser">
                                <i class="fas fa-sync"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-2">
                        <div class="mb-2">
                            <div class="row g-2">
                                <div class="col-md-4">
                                    <select id="filterClassement" class="form-select form-select-sm">
                                        <option value="">Tous les classements</option>
                                        <option value="LEAD FARMER">Lead Farmers</option>
                                        <option value="PETIT PLANTEUR">Petits Planteurs</option>
                                        <option value="FREELANCE">Freelance</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <input type="text" id="filterProducteurs" class="form-control form-control-sm" placeholder="Rechercher un producteur...">
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-outline-secondary btn-sm" id="btnResetFilters">
                                        <i class="fas fa-undo"></i> Réinitialiser
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Lead Farmers -->
                        <div class="mb-3">
                            <h6 class="text-danger mb-2">
                                <i class="fas fa-crown"></i> Lead Farmers
                            </h6>
                            <div id="leadFarmersList" class="row g-2">
                                <!-- Lead Farmers chargés dynamiquement -->
                            </div>
                        </div>

                        <!-- Petits Planteurs -->
                        <div class="mb-3">
                            <h6 class="text-success mb-2">
                                <i class="fas fa-seedling"></i> Petits Planteurs
                            </h6>
                            <div id="petitsPlanteursList" class="row g-2">
                                <!-- Petits Planteurs chargés dynamiquement -->
                            </div>
                        </div>

                        <!-- Freelance -->
                        <div class="mb-3">
                            <h6 class="text-warning mb-2">
                                <i class="fas fa-user-tie"></i> Freelance
                            </h6>
                            <div id="freelanceList" class="row g-2">
                                <!-- Freelance chargés dynamiquement -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Producteur -->
    <div class="modal fade" id="modalProducteur" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalProducteurTitle">
                        <i class="fas fa-user-plus"></i> Nouveau Producteur
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="formProducteur" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center mb-3">
                                    <div id="photoPreview" class="producer-photo-placeholder mx-auto">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <input type="file" id="photoProducteur" name="photo" class="form-control form-control-sm mt-2" accept="image/*">
                                    <small class="text-muted">Photo du producteur</small>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <label class="form-label">Code Producteur *</label>
                                        <input type="text" id="codeProducteur" name="code_producteur" class="form-control" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Nom Complet *</label>
                                        <input type="text" id="nomProducteur" name="nom" class="form-control" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Code Leads</label>
                                        <input type="text" id="codeLeads" name="code_leads" class="form-control">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Genre</label>
                                        <select id="genreProducteur" name="genre" class="form-select">
                                            <option value="">Sélectionner</option>
                                            <option value="M">Masculin</option>
                                            <option value="F">Féminin</option> 
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">CIN</label>
                                        <input type="text" id="cinProducteur" name="cin" class="form-control">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Date de Naissance</label>
                                        <input type="date" id="dateNaissance" name="date_naissance" class="form-control">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Téléphone</label>
                                        <input type="text" id="telephoneProducteur" name="telephone" class="form-control">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Email</label>
                                        <input type="email" id="emailProducteur" name="email" class="form-control">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Site (Commune) *</label>
                                        <select id="siteProducteur" name="site" class="form-select" required>
                                            <option value="">Sélectionner un site</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Classement *</label>
                                        <select id="classementProducteur" name="classement" class="form-select" required>
                                            <option value="PETIT PLANTEUR">Petit Planteur</option>
                                            <option value="LEAD FARMER">Lead Farmer</option>
                                            <option value="FREELANCE">Freelance</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">Adresse</label>
                                        <textarea id="adresseProducteur" name="adresse" class="form-control" rows="2"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSaveProducteur">
                        <i class="fas fa-save"></i> <span id="btnSaveProducteurText">Enregistrer</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Commune -->
    <div class="modal fade" id="modalCommune" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalCommuneTitle">
                        <i class="fas fa-map-marker-alt"></i> Nouvelle Commune
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="formCommune">
                        <div class="mb-3">
                            <label class="form-label">Nom de la Commune *</label>
                            <input type="text" id="nomCommune" name="nom" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Code Postal</label>
                            <input type="text" id="codePostal" name="code_postal" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Région</label>
                            <input type="text" id="regionCommune" name="region" class="form-control" value="DIANA">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Pays</label>
                            <input type="text" id="paysCommune" name="pays" class="form-control" value="Madagascar">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSaveCommune">
                        <i class="fas fa-save"></i> <span id="btnSaveCommuneText">Enregistrer</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JS Bootstrap + DataTables + SweetAlert -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/gestion_producteurs.js?v=<?php echo date('YmdHis'); ?>"></script>

</body>

</html>

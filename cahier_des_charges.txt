Cahier des charges – Gestion des Matières Premières et Traçabilité

1.  Matières premières

Chaîne d’approvisionnement : <PERSON><PERSON> → Lead Farmer → Coopérative

Exemple : Coopérative DAMAS = [Paysan 1 : 25 kg + Paysan 2 : 25 kg +
Paysan 3 : 0 kg] = 50 kg

2.  Suivi et Traçabilité

Fiche de référence : un numéro unique est attribué à chaque opération.

Flux de gestion : - Achat auprès du Lead → Stock - Quantité par sac -
Type : Bio (Standard / Supérieur) ou Conventionnel (Standard /
Supérieur) - Responsable : Denis

-   Stock → Lead
    -   Prix unitaire payé au producteur
    -   Quantité + poids brut (obligatoire à l’enregistrement)
    -   Limitation : Denis n’agit que sur le poids brut

Vérification du Lead Farmer : - Contrôle qualité + analyse des paysans
rattachés - Résultat consigné dans une liste Excel des Leads - L’agent
(Denis) effectue une descente terrain pour : - Estimer la production par
paysan - Enregistrer la quantité prévisionnelle par Lead

Exemple : Lead DAMAS doit livrer : - 500 tonnes de cacao Bio - 300 kg de
cacao Supérieur

3.  Certification

Lors de l’achat chez un Lead Farmer, l’agent (Joassin) enregistre : -
Produit - Quantité brute - Quantité nette - Date - Quantité initiale
(stock avant entrée) - Statut - N° Lot

Gestion des lots : - Chaque qualité (Bio, Standard, Supérieur) possède
ses propres lots - Exemple : Lot 001 Standard / Lot 001 Supérieur -
Chaque lot gère son stock indépendamment - Lorsqu’un lot atteint son
seuil, un nouveau lot est créé (Lot 002, etc.)

Exemple : - Lot 001 Bio = 375 sacs → passage à Lot 002 - Lot Supérieur :
tant que < 375 sacs, Lot 001 continue

Base de données : - Table entree_entete (en-tête d’achat) - Table
entree_detail (détails par produit) - Chaque entrée peut contenir
plusieurs produits - Pour chaque produit, on enregistre la quantité
initiale avant l’entrée

Processus d’achat (Joassin) : 1. Vérification → Conversion Poids brut →
Poids net 2. Définition du type de cacao (Bio / Conventionnel + Standard
ou Supérieur) 3. Calcul du poids net à payer

Cycle de statut : - Brouillon → En cours → Validé → À payer - Une fois
validé : - Le stock est incrémenté avec dépôt, produit et lot - Exemple
: - Dépôt = 1, Produit = BIO001, Lot = 0001, Quantité = XXXX - Dépôt =
1, Produit = BIO001, Lot = 0002, Quantité = XXXX - Lot max = 374 sacs
(par qualité) - Statut devient À payer et l’entrée devient non
modifiable

4.  Comptabilité (Étape 2 – Responsable : Guy)

Toute entrée au statut À payer bascule en comptabilité.

Traitement : - Chaque entrée doit être réglée → introduction du prix
unitaire - Ce champ n’est disponible qu’au statut Payé

Éléments enregistrés : - Date - Mode de paiement (Virement, Chèque avec
référence, Espèces) - Lead Farmer - Produit / Qualité - Quantité - Prix
unitaire - Montant total - Avance - Net à payer

Lien caisse : - Chaque paiement est enregistré comme une opération de
caisse - Chaque jour, l’agent renseigne le montant initial de la caisse

Journal de caisse et banque : - Suivi des mouvements : paiements
entrants, dépenses, ventes, etc. - Suivi des soldes initiaux (banque et
caisse)

Format journal : - Date - Bénéficiaire - Mode de paiement - Libellé -
Montant - Débit / Crédit / Solde

5.  Spécifications techniques

-   Authentification et sécurité
    -   Système de connexion (login) obligatoire
    -   Gestion des droits d’accès via rbac.php (rôles et permissions)
-   Interface utilisateur
    -   Page d’accueil (index.php) : chaque menu s’ouvre dans un nouvel
        onglet interne (type tab avec iframe), similaire aux onglets
        d’un navigateur Chrome
    -   Possibilité d’ouvrir plusieurs pages simultanément
    -   Prévoir la création de différents types d’onglets pour les
        autres pages
-   Design
    -   Un fichier CSS unique pour assurer la cohérence visuelle
    -   Style professionnel, sobre et homogène (dominante grise, sans
        couleurs vives)
    -   Composants de taille réduite (small)

<?php
/**
 * Test complet du système de ventes
 * Ce script teste la création d'une vente avec tous les champs requis
 */

require_once 'config/config.php';
require_once CONFIG_PATH . '/database.php';

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<h2>✅ Connexion à la base de données réussie</h2>";
} catch (PDOException $e) {
    die("<h2>❌ Erreur de connexion : " . $e->getMessage() . "</h2>");
}

echo "<h1>Test Complet du Système de Ventes</h1>";

// 1. Test de création d'une vente complète
echo "<h3>1. Test de création d'une vente complète</h3>";

try {
    // Récupérer un client
    $stmt = $pdo->query("SELECT id, nom FROM clients LIMIT 1");
    $client = $stmt->fetch();
    
    if (!$client) {
        echo "❌ Aucun client trouvé. Création d'un client de test...<br>";
        $pdo->exec("INSERT INTO clients (nom, type_client, adresse, telephone, email) VALUES 
                   ('Client Test Complet', 'EXPORTATEUR', '456 Rue Test', '+261 34 98 765 43', '<EMAIL>')");
        
        $stmt = $pdo->query("SELECT id, nom FROM clients WHERE nom = 'Client Test Complet'");
        $client = $stmt->fetch();
    }
    
    // Récupérer un produit
    $stmt = $pdo->query("SELECT id, nom, prix_unitaire FROM produits LIMIT 1");
    $produit = $stmt->fetch();
    
    if (!$produit) {
        echo "❌ Aucun produit trouvé. Création d'un produit de test...<br>";
        $pdo->exec("INSERT INTO produits (nom, prix_unitaire, unite) VALUES 
                   ('Cacao Test Premium', 2800.00, 'kg')");
        
        $stmt = $pdo->query("SELECT id, nom, prix_unitaire FROM produits WHERE nom = 'Cacao Test Premium'");
        $produit = $stmt->fetch();
    }
    
    // Récupérer un dépôt
    $stmt = $pdo->query("SELECT id, nom FROM depot LIMIT 1");
    $depot = $stmt->fetch();
    
    if (!$depot) {
        echo "❌ Aucun dépôt trouvé. Création d'un dépôt de test...<br>";
        $pdo->exec("INSERT INTO depot (nom, adresse) VALUES 
                   ('Dépôt Test', 'Adresse dépôt test')");
        
        $stmt = $pdo->query("SELECT id, nom FROM depot WHERE nom = 'Dépôt Test'");
        $depot = $stmt->fetch();
    }
    
    echo "✅ Client trouvé: {$client['nom']} (ID: {$client['id']})<br>";
    echo "✅ Produit trouvé: {$produit['nom']} (ID: {$produit['id']}, Prix: {$produit['prix_unitaire']} Ar)<br>";
    echo "✅ Dépôt trouvé: {$depot['nom']} (ID: {$depot['id']})<br>";
    
    // Créer une vente complète avec TOUS les champs
    $reference = 'VTE-TEST-' . date('YmdHis');
    $dateVente = date('Y-m-d');
    $totalMontant = 5600.00; // 2 tonnes à 2800 Ar
    $coursDevise = 4875.40;
    $valeurEur = $totalMontant / $coursDevise;
    
    echo "<h4>Création de la vente en-tête...</h4>";
    
    $stmt = $pdo->prepare("INSERT INTO ventes_entete (
        client_id, n_domiciliation, total_montant, total_remise, date_vente, statut,
        valeur_euro, valeur_ar, cours_devise, dau_numero, dau_date, 
        facture_numero, facture_date, lieux_exportation, cree_par
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $result = $stmt->execute([
        $client['id'],
        'DOM-' . date('Ymd'),
        $totalMontant,
        0.00, // Pas de remise
        $dateVente,
        'Encours',
        $valeurEur,
        $totalMontant,
        $coursDevise,
        'DAU-' . date('Ymd'),
        $dateVente,
        $reference,
        $dateVente,
        'Port de Tamatave',
        'system'
    ]);
    
    if ($result) {
        $venteId = $pdo->lastInsertId();
        echo "✅ Vente en-tête créée avec succès (ID: $venteId)<br>";
        
        echo "<h4>Création des détails de vente...</h4>";
        
        // Créer les détails avec TOUS les champs
        $stmt = $pdo->prepare("INSERT INTO ventes_details (
            vente_id, produit_id, depot_id, expedition, grade, qualite, 
            qte_tonnes, qte_dernier_stock, nbre_lot, bl, conteneur, 
            seal, lots, cree_par
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $result = $stmt->execute([
            $venteId,
            $produit['id'],
            $depot['id'],
            'EXP-' . date('Ymd'),
            'Grade 1',
            'Premium',
            2.000, // 2 tonnes
            0, // Stock avant sortie (à calculer)
            5, // Nombre de lots
            'BL-' . date('Ymd'),
            'CONT-' . date('Ymd'),
            'SEAL-' . date('Ymd'),
            'LOT1,LOT2,LOT3,LOT4,LOT5',
            'system'
        ]);
        
        if ($result) {
            echo "✅ Détails de vente créés avec succès<br>";
            
            echo "<h4>Test des opérations de workflow...</h4>";
            
            // Test 1: Mettre en attente
            $stmt = $pdo->prepare("UPDATE ventes_entete SET statut = 'En attente', dernier_modif_par = 'system' WHERE id = ?");
            $stmt->execute([$venteId]);
            echo "✅ Vente mise en attente<br>";
            
            // Test 2: Facturer
            $stmt = $pdo->prepare("UPDATE ventes_entete SET statut = 'Facturer', dernier_modif_par = 'system' WHERE id = ?");
            $stmt->execute([$venteId]);
            echo "✅ Vente facturée<br>";
            
            // Test 3: Enregistrer un paiement
            echo "<h4>Test d'enregistrement de paiement...</h4>";
            
            // Vérifier si la table operation_caisse supporte les ventes
            $stmt = $pdo->query("SHOW COLUMNS FROM operation_caisse LIKE 'vente_id'");
            if ($stmt->rowCount() > 0) {
                $stmt = $pdo->prepare("INSERT INTO operation_caisse (
                    type_operation, montant, date_paiement, mode_paiement, 
                    reference_paiement, commentaires, vente_id, utilisateur, statut
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $result = $stmt->execute([
                    'ENCAISSEMENT_VENTE',
                    $totalMontant,
                    $dateVente,
                    'VIREMENT',
                    'VIR-' . date('YmdHis'),
                    'Paiement test vente complète',
                    $venteId,
                    'system',
                    'VALIDE'
                ]);
                
                if ($result) {
                    echo "✅ Paiement enregistré avec succès<br>";
                } else {
                    echo "❌ Erreur lors de l'enregistrement du paiement<br>";
                }
            } else {
                echo "⚠️ Table operation_caisse non étendue pour les ventes. Exécutez le script d'extension.<br>";
            }
            
            echo "<h4>Résumé de la vente créée:</h4>";
            echo "<ul>";
            echo "<li><strong>ID Vente:</strong> $venteId</li>";
            echo "<li><strong>Référence:</strong> $reference</li>";
            echo "<li><strong>Client:</strong> {$client['nom']}</li>";
            echo "<li><strong>Produit:</strong> {$produit['nom']}</li>";
            echo "<li><strong>Quantité:</strong> 2.000 tonnes</li>";
            echo "<li><strong>Montant:</strong> " . number_format($totalMontant, 2) . " Ar</li>";
            echo "<li><strong>Valeur EUR:</strong> " . number_format($valeurEur, 2) . " €</li>";
            echo "<li><strong>Statut:</strong> Facturer</li>";
            echo "</ul>";
            
        } else {
            echo "❌ Erreur lors de la création des détails<br>";
        }
        
    } else {
        echo "❌ Erreur lors de la création de la vente en-tête<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur lors du test : " . $e->getMessage() . "<br>";
}

// 2. Vérification de l'intégrité des données
echo "<h3>2. Vérification de l'intégrité des données</h3>";

try {
    // Compter les ventes
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ventes_entete");
    $ventesCount = $stmt->fetch()['count'];
    echo "✅ Nombre total de ventes: $ventesCount<br>";
    
    // Compter les détails
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ventes_details");
    $detailsCount = $stmt->fetch()['count'];
    echo "✅ Nombre total de détails: $detailsCount<br>";
    
    // Vérifier les paiements
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM operation_caisse WHERE type_operation = 'ENCAISSEMENT_VENTE'");
    $paiementsCount = $stmt->fetch()['count'];
    echo "✅ Nombre de paiements de vente: $paiementsCount<br>";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors de la vérification : " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>✅ Test complet terminé avec succès!</h3>";
echo "<p><a href='pages/gestion_ventes.php' class='btn btn-primary'>🚀 Accéder à la Gestion des Ventes</a></p>";
echo "<p><small>Test effectué le " . date('Y-m-d H:i:s') . "</small></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Complet Ventes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .btn:hover { background: #0056b3; }
        ul { background: #f8f9fa; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <!-- Le contenu PHP est affiché ci-dessus -->
</body>
</html>

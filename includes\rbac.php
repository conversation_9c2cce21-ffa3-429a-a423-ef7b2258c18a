<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/../config/database.php';

class RBAC
{
    /** @var PDO */
    private $db;

    /**
     * Load permissions configuration from config/permissions.php
     * Structure example:
     * [
     *   'menus' => [
     *      [ 'key' => 'dashboard', 'label' => 'Tableau de bord', 'page' => 'dashboard.php', 'actions' => [] ],
     *      ...
     *   ]
     * ]
     */
    public static function getPermissionsConfig()
    {
        $configFile = __DIR__ . '/../config/permissions.php';
        if (!file_exists($configFile)) {
            return [ 'menus' => [] ];
        }
        /** @noinspection PhpIncludeInspection */
        $config = include $configFile;
        if (!is_array($config)) {
            return [ 'menus' => [] ];
        }
        return $config + [ 'menus' => [] ];
    }

    public function __construct()
    {
        $this->db = Database::getInstance()->getConnection();
        $this->migrate();
        $this->seedDefaults();
    }

    private function migrate()
    {
        // roles table
        $this->db->exec(
            "CREATE TABLE IF NOT EXISTS roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                description VARCHAR(255) NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8"
        );

        // role_permissions table
        $this->db->exec(
            "CREATE TABLE IF NOT EXISTS role_permissions (
                role_id INT NOT NULL,
                permission VARCHAR(191) NOT NULL,
                PRIMARY KEY (role_id, permission),
                INDEX (permission),
                CONSTRAINT fk_role_permissions_role FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8"
        );
    }

    private function seedDefaults()
    {
        // Seed base roles if not present
        $baseRoles = [
            'admin' => 'Administrateur'
        ,   'manager' => 'Manager'
        ,   'vendeur' => 'Vendeur'
        ,   'caissier' => 'Caissier'
        ,   'production' => 'Production'
        ,   'utilisateur' => 'Utilisateur standard'
        ];

        $stmt = $this->db->prepare('SELECT name FROM roles');
        $stmt->execute();
        $existing = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $existingMap = array_flip($existing ?: []);

        $insert = $this->db->prepare('INSERT INTO roles (name, description) VALUES (?, ?)');
        foreach ($baseRoles as $name => $desc) {
            if (!isset($existingMap[$name])) {
                $insert->execute([$name, $desc]);
            }
        }

        // Ensure admin has wildcard permission "*"
        $adminId = $this->getRoleIdByName('admin');
        if ($adminId) {
            $stmt = $this->db->prepare('SELECT COUNT(*) FROM role_permissions WHERE role_id = ? AND permission = ?');
            $stmt->execute([$adminId, '*']);
            if ((int)$stmt->fetchColumn() === 0) {
                $ins = $this->db->prepare('INSERT INTO role_permissions (role_id, permission) VALUES (?, ?)');
                $ins->execute([$adminId, '*']);
            }
        }

        // Seed default permissions for other roles if they have none yet
        $this->seedDefaultPermissionsForPredefinedRoles();
    }

    private function seedDefaultPermissionsForPredefinedRoles()
    {
        $cfg = self::getPermissionsConfig();

        // Helper to build all perms for a menu
        $buildMenuPerms = function($menu) {
            $perms = [];
            if (!isset($menu['key'])) return $perms;
            $perms[] = self::permissionKeyForMenu($menu['key']);
            if (!empty($menu['page'])) {
                $perms[] = self::permissionKeyForPage($menu['page']);
            }
            if (!empty($menu['actions']) && is_array($menu['actions'])) {
                foreach ($menu['actions'] as $a) { $perms[] = $a; }
            }
            return $perms;
        };

        // Vendeur: catalogue (produits_finis), commandes
        $vendeurPerms = [];
        foreach ($cfg['menus'] as $m) {
            if (in_array($m['key'], ['produits_finis','commandes'], true)) {
                $vendeurPerms = array_merge($vendeurPerms, $buildMenuPerms($m));
                // Ajout wildcard d'actions de commandes
                $vendeurPerms[] = 'commandes.*';
            }
        }
        $this->ensurePermissionsIfEmpty('vendeur', array_unique($vendeurPerms));

        // Caissier: factures, caisses (journal de caisse), sorties de caisse
        $caissierPerms = [];
        foreach ($cfg['menus'] as $m) {
            if (in_array($m['key'], ['factures','caisses','sorties_caisse'], true)) {
                $caissierPerms = array_merge($caissierPerms, $buildMenuPerms($m));
                $caissierPerms[] = $m['key'] . '.*';
            }
        }
        // paiement_commandes
        $caissierPerms[] = 'commandes.payer';
        $this->ensurePermissionsIfEmpty('caissier', array_unique($caissierPerms));

        // Production: matières premières, validation production
        $prodPerms = [];
        foreach ($cfg['menus'] as $m) {
            if (in_array($m['key'], ['matiere_premiere','validation_production'], true)) {
                $prodPerms = array_merge($prodPerms, $buildMenuPerms($m));
            }
        }
        // Ajout wildcards utiles
        $prodPerms[] = 'matiere.*';
        $prodPerms[] = 'production.*';
        $this->ensurePermissionsIfEmpty('production', array_unique($prodPerms));

        // Manager: tous sauf gestion_utilisateur
        $mgrPerms = [];
        foreach ($cfg['menus'] as $m) {
            if ($m['key'] === 'gestion_utilisateur') continue;
            $mgrPerms = array_merge($mgrPerms, $buildMenuPerms($m));
        }
        $this->ensurePermissionsIfEmpty('manager', array_unique($mgrPerms));

        // Utilisateur: accès au dashboard seulement
        $userPerms = [];
        foreach ($cfg['menus'] as $m) {
            if ($m['key'] === 'dashboard') {
                $userPerms = array_merge($userPerms, $buildMenuPerms($m));
                break;
            }
        }
        $this->ensurePermissionsIfEmpty('utilisateur', array_unique($userPerms));
    }

    private function ensurePermissionIfMissing($roleName, $permission)
    {
        $roleId = $this->getRoleIdByName($roleName);
        if (!$roleId) return;
        $stmt = $this->db->prepare('SELECT COUNT(*) FROM role_permissions WHERE role_id = ? AND permission = ?');
        $stmt->execute([$roleId, $permission]);
        if ((int)$stmt->fetchColumn() === 0) {
            $ins = $this->db->prepare('INSERT INTO role_permissions (role_id, permission) VALUES (?, ?)');
            $ins->execute([$roleId, $permission]);
        }
    }

    private function ensurePermissionsIfEmpty($roleName, array $permissions)
    {
        $roleId = $this->getRoleIdByName($roleName);
        if (!$roleId) return;
        $stmt = $this->db->prepare('SELECT COUNT(*) FROM role_permissions WHERE role_id = ?');
        $stmt->execute([$roleId]);
        if ((int)$stmt->fetchColumn() > 0) return; // ne pas écraser des droits existants
        if (empty($permissions)) return;
        $ins = $this->db->prepare('INSERT INTO role_permissions (role_id, permission) VALUES (?, ?)');
        foreach ($permissions as $p) {
            $p = trim($p);
            if ($p !== '') { $ins->execute([$roleId, $p]); }
        }
    }

    public function getRoleIdByName($roleName)
    {
        $stmt = $this->db->prepare('SELECT id FROM roles WHERE name = ?');
        $stmt->execute([$roleName]);
        $id = $stmt->fetchColumn();
        return $id ? (int)$id : null;
    }

    public function getPermissionsForRoleName($roleName)
    {
        $roleId = $this->getRoleIdByName($roleName);
        if (!$roleId) {
            return [];
        }
        $stmt = $this->db->prepare('SELECT permission FROM role_permissions WHERE role_id = ?');
        $stmt->execute([$roleId]);
        $perms = $stmt->fetchAll(PDO::FETCH_COLUMN) ?: [];
        return $perms;
    }

    public static function currentUserRoleName()
    {
        if (!isset($_SESSION['user'])) {
            return null;
        }
        $role = isset($_SESSION['user']['role']) ? $_SESSION['user']['role'] : null;
        return $role ?: null;
    }

    public function roleHasPermission($roleName, $permissionKey)
    {
        if (!$roleName) {
            return false;
        }
        // Admin wildcard
        if ($roleName === 'admin') {
            return true;
        }
        $perms = $this->getPermissionsForRoleName($roleName);
        if (in_array('*', $perms, true)) {
            return true;
        }
        // Exact match
        if (in_array($permissionKey, $perms, true)) {
            return true;
        }
        // Support simple namespace wildcard, e.g. facture.* matches facture.view
        $parts = explode('.', $permissionKey);
        while (count($parts) > 1) {
            array_pop($parts);
            $try = implode('.', $parts) . '.*';
            if (in_array($try, $perms, true)) {
                return true;
            }
        }
        return false;
    }

    public function requirePermission($permissionKey)
    {
        $role = self::currentUserRoleName();
        if ($this->roleHasPermission($role, $permissionKey)) {
            return true;
        }
        http_response_code(403);
        echo 'Accès refusé';
        exit;
    }

    public static function permissionKeyForMenu($menuKey)
    {
        return 'menu.' . $menuKey . '.view';
    }

    public static function permissionKeyForPage($pageFileBasename)
    {
        $key = strtolower(preg_replace('/\.php$/i', '', $pageFileBasename));
        return 'page.' . $key . '.view';
    }

    public static function listAllPermissionsFromConfig()
    {
        $cfg = self::getPermissionsConfig();
        $permissions = [];
        if (!empty($cfg['menus']) && is_array($cfg['menus'])) {
            foreach ($cfg['menus'] as $menu) {
                if (!isset($menu['key'])) continue;
                $menuKey = $menu['key'];
                $permissions[] = self::permissionKeyForMenu($menuKey);
                if (!empty($menu['page'])) {
                    $permissions[] = self::permissionKeyForPage($menu['page']);
                }
                if (!empty($menu['actions']) && is_array($menu['actions'])) {
                    foreach ($menu['actions'] as $act) {
                        $permissions[] = $act;
                    }
                }
            }
        }
        return array_values(array_unique($permissions));
    }
}

// Helper functions (procedural) for convenient use in pages
function rbac_instance()
{
    static $i = null;
    if ($i === null) {
        $i = new RBAC();
    }
    return $i;
}

function rbac_can($permissionKey)
{
    return rbac_instance()->roleHasPermission(RBAC::currentUserRoleName(), $permissionKey);
}

function rbac_require($permissionKey)
{
    return rbac_instance()->requirePermission($permissionKey);
}

function rbac_require_page_access_by_filename($fullPath)
{
    $basename = basename($fullPath);
    $key = RBAC::permissionKeyForPage($basename);
    return rbac_require($key);
}

function rbac_menu_allowed($menuKey)
{
    return rbac_can(RBAC::permissionKeyForMenu($menuKey));
} 
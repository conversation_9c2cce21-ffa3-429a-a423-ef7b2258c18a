<?php
error_reporting(E_ALL);
error_reporting(-1);
ini_set('error_reporting', E_ALL);
/**
 * ===================================================================
 * API Générique Complète
 * ===================================================================
 * Gère les opérations CRUD simples et en masse, l'exécution de SQL
 * et l'upload de fichiers, avec un contrôle d'accès basé sur les rôles (RBAC).
 *
 * --- ACTIONS SIMPLES ---
 * - action: 'create', 'read', 'update', 'delete', 'execute_sql'
 * - table: Le nom de la table.
 * - data: Un objet JSON. Ex: '{"nom":"test"}'
 * - id: L'ID de l'enregistrement.
 *
 * --- ACTIONS EN MASSE (BULK) ---
 * - action: 'create_bulk', 'update_bulk', 'delete_bulk'
 * - table: Le nom de la table.
 * - data (pour create/update): Un tableau d'objets JSON. Ex: '[{"nom":"A"},{"nom":"B"}]'
 * - ids (pour delete): Un tableau d'IDs JSON. Ex: '[1, 2, 3]'
 *
 * --- SÉCURITÉ ---
 * Le rôle de l'utilisateur est récupéré depuis $_SESSION['role'].
 * Les permissions sont définies dans la variable $permissions.
 */

// --- DÉMARRAGE DE SESSION ET CONFIGURATION ---
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Utilisation de la configuration existante
require_once dirname(__DIR__) . '/config/database.php';
define('UPLOAD_DIR', __DIR__ . '/uploads/');

ini_set('display_errors', 1); // À désactiver en production
error_reporting(E_ALL);
header('Content-Type: application/json');


// --- CONFIGURATION DES PERMISSIONS RBAC ---
require_once dirname(__DIR__) . '/includes/rbac.php';
$rbac = new RBAC();

/**
 * Fonction pour vérifier les permissions.
 */
function hasPermission(string $role, string $table, string $action, array $permissions): bool {
    if (!isset($permissions[$role])) {
        return false;
    }
    $rolePermissions = $permissions[$role];
    if (isset($rolePermissions['*']) && in_array('*', $rolePermissions['*'])) {
        return true;
    }
    if (isset($rolePermissions[$table])) {
        return in_array('*', $rolePermissions[$table]) || in_array($action, $rolePermissions[$table]);
    }
    if (isset($rolePermissions['*'])) {
        return in_array($action, $rolePermissions['*']);
    }
    return false;
}


// --- POINT D'ENTRÉE DE L'API ---
try {
    $db = Database::getInstance()->getConnection();
    $pdo = $db;

    $action = $_POST['action'] ?? null;
    $table = $_POST['table'] ?? null;
    
    if (!$action || !$table) {
        throw new InvalidArgumentException("L'action et le nom de la table sont requis.");
    }

    // --- VÉRIFICATION DES PERMISSIONS ---
    $userRole = $_SESSION['user']['role'] ?? $_SESSION['role'] ?? 'utilisateur';
    
    // Pour les actions execute_sql, on utilise une permission générique
    $permissionKey = ($action === 'execute_sql') ? 'produits.view' : $table . '.' . $action;
    
    // Vérification des permissions avec la méthode correcte de RBAC
    if (!$rbac->roleHasPermission($userRole, $table . '.' . $action)) {
        http_response_code(403); // Forbidden
        throw new Exception("Accès non autorisé. Vous n'avez pas les droits pour effectuer cette action.");
    }

    $id = $_POST['id'] ?? null;
    $ids = isset($_POST['ids']) ? json_decode($_POST['ids'], true) : [];
    $data = isset($_POST['data']) ? json_decode($_POST['data'], true) : [];

    $handler = new CrudHandler($pdo, $table);
    $response = [];

    if (!empty($_FILES)) {
        foreach ($_FILES as $fieldName => $file) {
            if ($file['error'] === UPLOAD_ERR_OK) {
                $data[$fieldName] = $handler->handleUpload($file, $table);
            }
        }
    }

    $pdo->beginTransaction();

    switch ($action) {
        case 'create':
            $newId = $handler->create($data);
            $response = ['success' => true, 'message' => 'Enregistrement créé.', 'id' => $newId];
            break;
        case 'read':
            $results = $handler->read($data);
            $response = ['success' => true, 'data' => $results];
            break;
        case 'update':
            if (!$id) throw new InvalidArgumentException("L'ID est requis pour la mise à jour.");
            $handler->update($id, $data);
            $response = ['success' => true, 'message' => 'Mise à jour réussie.'];
            break;
        case 'delete':
            if (!$id) throw new InvalidArgumentException("L'ID est requis pour la suppression.");
            $handler->delete($id);
            $response = ['success' => true, 'message' => 'Suppression réussie.'];
            break;
        case 'create_bulk':
            $rowCount = $handler->createBulk($data);
            $response = ['success' => true, 'message' => "$rowCount enregistrements créés."];
            break;
        case 'update_bulk':
            $rowCount = $handler->updateBulk($data);
            $response = ['success' => true, 'message' => "$rowCount enregistrements mis à jour."];
            break;
        case 'delete_bulk':
            $rowCount = $handler->deleteBulk($ids);
            $response = ['success' => true, 'message' => "$rowCount enregistrements supprimés."];
            break;
        case 'execute_sql':
            $sql = $_POST['sql'] ?? null;
            $params = isset($_POST['params']) ? json_decode($_POST['params'], true) : [];
            if (!$sql) throw new InvalidArgumentException("La requête SQL est requise.");
            // Pour execute_sql, on utilise une table fictive pour la vérification des permissions
            $handler = new CrudHandler($pdo, 'produits'); // Table par défaut pour les permissions
            $results = $handler->executeRawSql($sql, $params);
            $response = ['success' => true, 'data' => $results];
            break;
        default:
            throw new InvalidArgumentException("Action non reconnue.");
    }

    $pdo->commit();
    echo json_encode($response);

} catch (Exception $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    if (http_response_code() < 400) { // Ne pas écraser un code 403
        http_response_code(400); // Bad Request par défaut
    }
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

// ===================================================================
// CLASSE DE GESTION CRUD
// ===================================================================
class CrudHandler {
    private PDO $pdo;
    private string $table;

    public function __construct(PDO $pdo, string $tableName) {
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $tableName)) {
            throw new InvalidArgumentException("Nom de table invalide.");
        }
        $this->pdo = $pdo;
        $this->table = $tableName;
    }

    public function create(array $data): string {
        if (empty($data)) throw new InvalidArgumentException("Aucune donnée à insérer.");
        
        // Ajouter les champs de traçabilité si la table les supporte
        $data = $this->addAuditFields($data, 'create');
        
        $columns = array_keys($data);
        $placeholders = array_map(fn($c) => ":$c", $columns);
        $sql = sprintf("INSERT INTO `%s` (%s) VALUES (%s)", $this->table, implode(', ', array_map(fn($c) => "`$c`", $columns)), implode(', ', $placeholders));
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($data);
        return $this->pdo->lastInsertId();
    }

    public function read(array $conditions = []): array {
        $sql = "SELECT * FROM `{$this->table}`";
        if (!empty($conditions)) {
            $whereClauses = array_map(fn($col) => "`$col` = :$col", array_keys($conditions));
            $sql .= " WHERE " . implode(' AND ', $whereClauses);
        }
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($conditions);
        return $stmt->fetchAll();
    }

    public function update($id, array $data): void {
        if (empty($data)) throw new InvalidArgumentException("Aucune donnée à mettre à jour.");
        
        // Ajouter les champs de traçabilité si la table les supporte
        $data = $this->addAuditFields($data, 'update');
        
        $setClauses = array_map(fn($col) => "`$col` = :$col", array_keys($data));
        $sql = sprintf("UPDATE `%s` SET %s WHERE id = :id", $this->table, implode(', ', $setClauses));
        $data['id'] = $id;
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($data);
    }

    public function delete($id): void {
        $sql = "DELETE FROM `{$this->table}` WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$id]);
    }

    public function createBulk(array $dataArray): int {
        if (empty($dataArray) || !isset($dataArray[0]) || !is_array($dataArray[0])) {
            throw new InvalidArgumentException("Les données pour 'create_bulk' doivent être un tableau d'objets.");
        }
        $columns = array_keys($dataArray[0]);
        $placeholders = '(' . implode(',', array_fill(0, count($columns), '?')) . ')';
        $allPlaceholders = implode(',', array_fill(0, count($dataArray), $placeholders));
        $sql = sprintf("INSERT INTO `%s` (%s) VALUES %s", $this->table, implode(', ', array_map(fn($c) => "`$c`", $columns)), $allPlaceholders);
        $values = [];
        foreach ($dataArray as $row) {
            foreach ($columns as $column) {
                $values[] = $row[$column] ?? null;
            }
        }
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($values);
        return $stmt->rowCount();
    }
    
    public function updateBulk(array $dataArray): int {
        if (empty($dataArray)) throw new InvalidArgumentException("Les données pour 'update_bulk' ne peuvent être vides.");
        $updatedCount = 0;
        foreach ($dataArray as $item) {
            if (!isset($item['id'])) continue;
            $id = $item['id'];
            unset($item['id']);
            if (!empty($item)) {
                $this->update($id, $item);
                $updatedCount++;
            }
        }
        return $updatedCount;
    }

    public function deleteBulk(array $ids): int {
        if (empty($ids)) return 0;
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "DELETE FROM `{$this->table}` WHERE id IN ($placeholders)";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($ids);
        return $stmt->rowCount();
    }
    
    public function executeRawSql(string $sql, array $params = []): array {
        if (stripos(trim($sql), 'SELECT') !== 0) {
            throw new InvalidArgumentException("Seules les requêtes SELECT sont autorisées.");
        }
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    public function handleUpload(array $file, string $subfolder): string {
        $uploadSubDir = UPLOAD_DIR . basename($subfolder) . '/';
        if (!is_dir($uploadSubDir)) {
            mkdir($uploadSubDir, 0755, true);
        }
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $safeFilename = bin2hex(random_bytes(16)) . '.' . $extension;
        $targetPath = $uploadSubDir . $safeFilename;
        if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
            throw new RuntimeException("Échec de l'upload du fichier.");
        }
        return $safeFilename;
    }

    /**
     * Ajoute les champs de traçabilité aux données si la table les supporte
     */
    private function addAuditFields(array $data, string $operation): array {
        $currentUser = $_SESSION['username'] ?? 'system';
        $currentTime = date('Y-m-d H:i:s');
        
        // Vérifier si la table a les colonnes de traçabilité
        $hasAuditColumns = $this->tableHasAuditColumns();
        
        if ($hasAuditColumns) {
            if ($operation === 'create') {
                $data['date_creation'] = $currentTime;
                $data['cree_par'] = $currentUser;
            }
            
            // Toujours ajouter les champs de modification
            $data['date_derniere_modif'] = $currentTime;
            $data['dernier_modif_par'] = $currentUser;
        }
        
        return $data;
    }
    
    /**
     * Vérifie si la table a les colonnes de traçabilité
     */
    private function tableHasAuditColumns(): bool {
        try {
            $sql = "SHOW COLUMNS FROM `{$this->table}` LIKE 'date_creation'";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
}
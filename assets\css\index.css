<style>
body { background: #f4f6f9; }
.tab-bar {
    display: flex;
    align-items: center;
    /* CORRECTION: Ajout de 'solid' */ 
    background: #ffff;
    padding: 0 5px;
    white-space: nowrap;
    overflow: visible; 
    position: relative; 
    z-index: 10;
}

.custom-tab.active {
    background: #fff;
    border-top: 3px solid #1b96ff;
    /* CORRECTION et AJOUTS pour masquer la ligne */
    border-left: 1px solid #ddd;  /* Ajout de bordures latérales pour la continuité */
    border-right: 1px solid #ddd; /* Ajout de bordures latérales pour la continuité */
    border-bottom: 3px solid #fff; /* Bordure de la couleur du fond pour "effacer" la ligne en dessous */
    margin-bottom: -6px; /* Tirez l'onglet vers le bas pour recouvrir la bordure */
    position: relative; /* Assure que le z-index fonctionne correctement */
    font-weight: 600;
    color: #1b96ff;
    z-index: 9999999999 !important;
}

.tab-pane {
    padding: 0px;
    background: #fff;  
    /* CORRECTION: 'solid' est plus approprié que 'inset' pour ce style */
    border-top: 3px solid #1b96ff; /* La même bordure grise que la barre */
    height: 100%;
}
.app-launcher {
  padding: 5px 10px;
  font-size: 18px;
  color: #444;
  cursor: pointer;
  z-index: 999999 !important;
}
.user-menu {
  margin-left: auto;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 18px;
  color: #444;
  z-index: 999999 !important;
}
.custom-tab {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  margin: 0 0px;
  background: #f9f9f9; 
  border-bottom: none;
  font-size: 0.8rem;
  cursor: pointer;
}
/* .custom-tab.active {
  background: #fff;
  border-top: 3px solid #1b96ff;
  border-bottom: #444;
  font-weight: 600;
  color: #1b96ff;
} */
.custom-tab .close {
  margin-left: 6px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
}
.custom-tab .close:hover { color: red; }
.tab-actions {
  margin-left: 4px;
  position: relative;
  z-index: 999999 !important;
}
.tab-actions .btn {
  padding: 0 4px;
  font-size: 12px;
  border: none;
  background: transparent;
}
.dropdown-menu {
  z-index: 999999 !important;
  border-radius: 0;
}
/* .tab-pane {
  padding: 0px;
  background: #fff;  
  border-top: 3px inset #1b96ff;
  height: 100%;
} */
.tab-actions .dropdown-menu,
.app-launcher + .dropdown-menu,
.user-menu .dropdown-menu {
    position: absolute !important; /* permet au menu de sortir du flux */
    z-index: 999999 !important; /* assure qu'il soit au-dessus */
}

/* AJOUTEZ CES RÈGLES À VOTRE <style> */



/* Ajoutez ces nouvelles règles */
.tab-content {
    /* Calcule la hauteur restante de l'écran (100vh) moins la hauteur de la barre d'onglets (ex: 42px) */
    height: calc(100vh - 44px);
}

iframe {
    width: 100%;
    height: 100%;
    border: none; 
} 
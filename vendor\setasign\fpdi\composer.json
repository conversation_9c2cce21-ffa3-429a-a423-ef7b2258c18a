{"name": "setasign/fpdi", "homepage": "https://www.setasign.com/fpdi", "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "type": "library", "keywords": ["pdf", "fpdi", "fpdf"], "license": "MIT", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "require": {"php": "^5.6 || ^7.0 || ^8.0", "ext-zlib": "*"}, "conflict": {"setasign/tfpdf": "<1.31"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8.6", "tecnickcom/tcpdf": "~6.2", "setasign/tfpdf": "~1.33", "squizlabs/php_codesniffer": "^3.5"}, "autoload-dev": {"psr-4": {"setasign\\Fpdi\\": "tests/"}}}
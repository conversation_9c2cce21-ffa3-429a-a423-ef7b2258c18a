$(document).ready(function() {
    let productsTable, stockTable, transfersTable, adjustmentsTable;
    let currentTab = 'products';

    // Initialisation
    init();

    function init() {
        loadProducts();
        loadDepots();
        loadStock();
        loadTransfers();
        loadAdjustments();
        bindEvents();
    }

    function bindEvents() {
        // Boutons principaux
        $('#btnAddProduct').click(showProductModal);
        $('#btnRefresh').click(refreshCurrentTab);
        $('#btnSaveProduct').click(saveProduct);
        $('#btnNewTransfer').click(showTransferModal);
        $('#btnSaveTransfer').click(saveTransfer);
        $('#btnNewAdjustment').click(showAdjustmentModal);
        $('#btnSaveAdjustment').click(saveAdjustment);
        $('#btnFilterStock').click(filterStock);

        // Changement d'onglets
        $('#productTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
            const target = $(e.target).attr('data-bs-target');
            currentTab = target.replace('#', '');
            
            switch(currentTab) {
                case 'products':
                    if (!productsTable) loadProducts();
                    break;
                case 'stock':
                    if (!stockTable) loadStock();
                    break;
                case 'transfers':
                    if (!transfersTable) loadTransfers();
                    break;
                case 'adjustments':
                    if (!adjustmentsTable) loadAdjustments();
                    break;
            }
        });

        // Changement de produit pour les transferts et ajustements
        $('#transferProduit, #adjustmentProduit').change(function() {
            loadLotsForProduct($(this).val(), $(this).attr('id').includes('transfer') ? 'transferLot' : 'adjustmentLot');
        });
    }

    function loadProducts() {
        if (productsTable) {
            productsTable.destroy();
        }

        productsTable = $('#productsTable').DataTable({
            ajax: {
                url: '../includes/traitement.php',
                type: 'POST',
                data: {
                    action: 'read',
                    table: 'produit'
                },
                dataSrc: ''
            },
            columns: [
                { data: 'code' },
                { data: 'nom' },
                { data: 'certification' },
                { data: 'type_qualite' },
                { 
                    data: 'prix_achat',
                    render: function(data) {
                        return new Intl.NumberFormat('fr-MG', {
                            style: 'currency',
                            currency: 'MGA'
                        }).format(data);
                    }
                },
                { 
                    data: 'prix_vente',
                    render: function(data) {
                        return new Intl.NumberFormat('fr-MG', {
                            style: 'currency',
                            currency: 'MGA'
                        }).format(data);
                    }
                },
                {
                    data: null,
                    render: function(data, type, row) {
                        return `
                            <button class="btn btn-sm btn-primary btn-action" onclick="editProduct(${row.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger btn-action" onclick="deleteProduct(${row.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        `;
                    }
                }
            ],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            responsive: true,
            pageLength: 25
        });
    }

    function loadStock() {
        if (stockTable) {
            stockTable.destroy();
        }

        stockTable = $('#stockTable').DataTable({
            ajax: {
                url: '../includes/traitement.php',
                type: 'POST',
                data: {
                    action: 'rawQuery',
                    sql: `
                        SELECT 
                            d.nom as depot_nom,
                            p.nom as produit_nom,
                            p.certification,
                            p.type_qualite,
                            l.numero as lot_numero,
                            sd.quantite_actuelle,
                            sd.nombre_sacs,
                            sd.derniere_mise_a_jour
                        FROM stock_detail sd
                        JOIN depot d ON sd.depot_id = d.id
                        JOIN produit p ON sd.produit_id = p.id
                        JOIN lot l ON sd.lot_id = l.id
                        ORDER BY d.nom, p.nom, l.numero
                    `
                },
                dataSrc: 'data'
            },
            columns: [
                { data: 'depot_nom' },
                { data: 'produit_nom' },
                { data: 'certification' },
                { data: 'type_qualite' },
                { data: 'lot_numero' },
                { 
                    data: 'quantite_actuelle',
                    render: function(data) {
                        return new Intl.NumberFormat('fr-MG', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        }).format(data) + ' kg';
                    }
                },
                { data: 'nombre_sacs' },
                { 
                    data: 'derniere_mise_a_jour',
                    render: function(data) {
                        return new Date(data).toLocaleString('fr-FR');
                    }
                }
            ],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            responsive: true,
            pageLength: 25
        });
    }

    function loadTransfers() {
        if (transfersTable) {
            transfersTable.destroy();
        }

        transfersTable = $('#transfersTable').DataTable({
            ajax: {
                url: '../includes/traitement.php',
                type: 'POST',
                data: {
                    action: 'rawQuery',
                    sql: `
                        SELECT 
                            ms.date_mouvement,
                            p.nom as produit_nom,
                            ds.nom as depot_source,
                            dd.nom as depot_destination,
                            ms.quantite,
                            ms.reference_document,
                            ms.id
                        FROM mouvement_stock ms
                        JOIN produit p ON ms.produit_id = p.id
                        JOIN depot ds ON ms.depot_id = ds.id
                        JOIN depot dd ON ms.depot_destination_id = dd.id
                        WHERE ms.type_mouvement = 'TRANSFERT'
                        ORDER BY ms.date_mouvement DESC
                    `
                },
                dataSrc: 'data'
            },
            columns: [
                { 
                    data: 'date_mouvement',
                    render: function(data) {
                        return new Date(data).toLocaleString('fr-FR');
                    }
                },
                { data: 'produit_nom' },
                { data: 'depot_source' },
                { data: 'depot_destination' },
                { 
                    data: 'quantite',
                    render: function(data) {
                        return new Intl.NumberFormat('fr-MG', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        }).format(data) + ' kg';
                    }
                },
                { data: 'reference_document' },
                {
                    data: null,
                    render: function(data, type, row) {
                        return `
                            <button class="btn btn-sm btn-danger btn-action" onclick="deleteTransfer(${row.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        `;
                    }
                }
            ],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            responsive: true,
            pageLength: 25
        });
    }

    function loadAdjustments() {
        if (adjustmentsTable) {
            adjustmentsTable.destroy();
        }

        adjustmentsTable = $('#adjustmentsTable').DataTable({
            ajax: {
                url: '../includes/traitement.php',
                type: 'POST',
                data: {
                    action: 'rawQuery',
                    sql: `
                        SELECT 
                            ms.date_mouvement,
                            p.nom as produit_nom,
                            d.nom as depot_nom,
                            ms.type_mouvement,
                            ms.quantite_avant,
                            ms.quantite_apres,
                            (ms.quantite_apres - ms.quantite_avant) as difference,
                            ms.libelle,
                            ms.id
                        FROM mouvement_stock ms
                        JOIN produit p ON ms.produit_id = p.id
                        JOIN depot d ON ms.depot_id = d.id
                        WHERE ms.type_mouvement IN ('AJUSTEMENT', 'INVENTAIRE')
                        ORDER BY ms.date_mouvement DESC
                    `
                },
                dataSrc: 'data'
            },
            columns: [
                { 
                    data: 'date_mouvement',
                    render: function(data) {
                        return new Date(data).toLocaleString('fr-FR');
                    }
                },
                { data: 'produit_nom' },
                { data: 'depot_nom' },
                { data: 'type_mouvement' },
                { 
                    data: 'quantite_avant',
                    render: function(data) {
                        return new Intl.NumberFormat('fr-MG', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        }).format(data) + ' kg';
                    }
                },
                { 
                    data: 'quantite_apres',
                    render: function(data) {
                        return new Intl.NumberFormat('fr-MG', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        }).format(data) + ' kg';
                    }
                },
                { 
                    data: 'difference',
                    render: function(data) {
                        const formatted = new Intl.NumberFormat('fr-MG', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        }).format(data) + ' kg';
                        const color = data >= 0 ? 'text-success' : 'text-danger';
                        return `<span class="${color}">${formatted}</span>`;
                    }
                },
                { data: 'libelle' },
                {
                    data: null,
                    render: function(data, type, row) {
                        return `
                            <button class="btn btn-sm btn-danger btn-action" onclick="deleteAdjustment(${row.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        `;
                    }
                }
            ],
            language: {
                url: 'https://cdn.datatables.net/1.13.6/i18n/fr-FR.json'
            },
            responsive: true,
            pageLength: 25
        });
    }

    function loadDepots() {
        $.post('../includes/traitement.php', {
            action: 'read',
            table: 'depot'
        }, function(data) {
            const depots = data;
            let options = '<option value="">Sélectionner...</option>';
            depots.forEach(depot => {
                options += `<option value="${depot.id}">${depot.nom}</option>`;
            });
            
            $('#depotFilter, #transferDepotSource, #transferDepotDestination, #adjustmentDepot').html(options);
        });
    }

    function loadLotsForProduct(produitId, targetSelectId) {
        if (!produitId) {
            $(`#${targetSelectId}`).html('<option value="">Sélectionner un lot...</option>');
            return;
        }

        $.post('../includes/traitement.php', {
            action: 'rawQuery',
            sql: `
                SELECT l.id, l.numero 
                FROM lot l 
                JOIN stock_detail sd ON l.id = sd.lot_id 
                WHERE sd.produit_id = ? AND sd.quantite_actuelle > 0
                ORDER BY l.numero
            `,
            params: [produitId]
        }, function(response) {
            if (response.success) {
                let options = '<option value="">Sélectionner un lot...</option>';
                response.data.forEach(lot => {
                    options += `<option value="${lot.id}">${lot.numero}</option>`;
                });
                $(`#${targetSelectId}`).html(options);
            }
        });
    }

    function showProductModal(productId = null) {
        $('#productModalTitle').text(productId ? 'Modifier Produit' : 'Nouveau Produit');
        $('#productForm')[0].reset();
        $('#productId').val(productId || '');
        
        if (productId) {
            loadProductData(productId);
        }
        
        $('#productModal').modal('show');
    }

    function loadProductData(productId) {
        $.post('../includes/traitement.php', {
            action: 'read',
            table: 'produit',
            filters: [{ id: productId }]
        }, function(data) {
            if (data.length > 0) {
                const product = data[0];
                $('#productId').val(product.id);
                $('#productCode').val(product.code);
                $('#productName').val(product.nom);
                $('#productCertification').val(product.certification);
                $('#productTypeQualite').val(product.type_qualite);
                $('#productPrixAchat').val(product.prix_achat);
                $('#productPrixVente').val(product.prix_vente);
            }
        });
    }

    function saveProduct() {
        const formData = {
            id: $('#productId').val(),
            code: $('#productCode').val(),
            nom: $('#productName').val(),
            certification: $('#productCertification').val(),
            type_qualite: $('#productTypeQualite').val(),
            prix_achat: parseFloat($('#productPrixAchat').val()),
            prix_vente: parseFloat($('#productPrixVente').val())
        };

        const action = formData.id ? 'update' : 'create';
        const dataArray = formData.id ? [formData] : [formData];

        $.post('../includes/traitement.php', {
            action: action,
            table: 'produit',
            data: JSON.stringify(dataArray)
        }, function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Succès',
                    text: response.message,
                    icon: 'success'
                });
                $('#productModal').modal('hide');
                loadProducts();
            } else {
                Swal.fire({
                    title: 'Erreur',
                    text: response.message,
                    icon: 'error'
                });
            }
        });
    }

    function showTransferModal() {
        loadProductsForSelect('#transferProduit');
        loadDepotsForTransfer();
        $('#transferForm')[0].reset();
        $('#transferModal').modal('show');
    }

    function loadProductsForSelect(selectId) {
        $.post('../includes/traitement.php', {
            action: 'read',
            table: 'produit'
        }, function(data) {
            let options = '<option value="">Sélectionner un produit...</option>';
            data.forEach(product => {
                options += `<option value="${product.id}">${product.nom} (${product.code})</option>`;
            });
            $(selectId).html(options);
        });
    }

    function loadDepotsForTransfer() {
        $.post('../includes/traitement.php', {
            action: 'read',
            table: 'depot'
        }, function(data) {
            let options = '<option value="">Sélectionner...</option>';
            data.forEach(depot => {
                options += `<option value="${depot.id}">${depot.nom}</option>`;
            });
            $('#transferDepotSource, #transferDepotDestination').html(options);
        });
    }

    function saveTransfer() {
        const formData = {
            produit_id: $('#transferProduit').val(),
            lot_id: $('#transferLot').val(),
            depot_source_id: $('#transferDepotSource').val(),
            depot_destination_id: $('#transferDepotDestination').val(),
            quantite: parseFloat($('#transferQuantite').val()),
            reference_document: $('#transferReference').val(),
            libelle: $('#transferLibelle').val()
        };

        // Validation
        if (!formData.produit_id || !formData.lot_id || !formData.depot_source_id || 
            !formData.depot_destination_id || !formData.quantite) {
            Swal.fire({
                title: 'Erreur',
                text: 'Veuillez remplir tous les champs obligatoires',
                icon: 'error'
            });
            return;
        }

        if (formData.depot_source_id === formData.depot_destination_id) {
            Swal.fire({
                title: 'Erreur',
                text: 'Le dépôt source et destination doivent être différents',
                icon: 'error'
            });
            return;
        }

        $.post('../includes/traitement.php', {
            action: 'transferStock',
            data: JSON.stringify(formData)
        }, function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Succès',
                    text: response.message,
                    icon: 'success'
                });
                $('#transferModal').modal('hide');
                loadTransfers();
                loadStock();
            } else {
                Swal.fire({
                    title: 'Erreur',
                    text: response.message,
                    icon: 'error'
                });
            }
        });
    }

    function showAdjustmentModal() {
        loadProductsForSelect('#adjustmentProduit');
        loadDepotsForAdjustment();
        $('#adjustmentForm')[0].reset();
        $('#adjustmentModal').modal('show');
    }

    function loadDepotsForAdjustment() {
        $.post('../includes/traitement.php', {
            action: 'read',
            table: 'depot'
        }, function(data) {
            let options = '<option value="">Sélectionner...</option>';
            data.forEach(depot => {
                options += `<option value="${depot.id}">${depot.nom}</option>`;
            });
            $('#adjustmentDepot').html(options);
        });
    }

    function saveAdjustment() {
        const formData = {
            produit_id: $('#adjustmentProduit').val(),
            lot_id: $('#adjustmentLot').val(),
            depot_id: $('#adjustmentDepot').val(),
            type_ajustement: $('#adjustmentType').val(),
            quantite_avant: parseFloat($('#adjustmentQuantiteAvant').val()),
            quantite_apres: parseFloat($('#adjustmentQuantiteApres').val()),
            libelle: $('#adjustmentLibelle').val()
        };

        // Validation
        if (!formData.produit_id || !formData.lot_id || !formData.depot_id || 
            !formData.type_ajustement || !formData.libelle) {
            Swal.fire({
                title: 'Erreur',
                text: 'Veuillez remplir tous les champs obligatoires',
                icon: 'error'
            });
            return;
        }

        $.post('../includes/traitement.php', {
            action: 'adjustStock',
            data: JSON.stringify(formData)
        }, function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Succès',
                    text: response.message,
                    icon: 'success'
                });
                $('#adjustmentModal').modal('hide');
                loadAdjustments();
                loadStock();
            } else {
                Swal.fire({
                    title: 'Erreur',
                    text: response.message,
                    icon: 'error'
                });
            }
        });
    }

    function filterStock() {
        const depotId = $('#depotFilter').val();
        const produitId = $('#produitFilter').val();
        
        let whereClause = '';
        let params = [];
        
        if (depotId) {
            whereClause += ' AND sd.depot_id = ?';
            params.push(depotId);
        }
        
        if (produitId) {
            whereClause += ' AND sd.produit_id = ?';
            params.push(produitId);
        }

        const sql = `
            SELECT 
                d.nom as depot_nom,
                p.nom as produit_nom,
                p.certification,
                p.type_qualite,
                l.numero as lot_numero,
                sd.quantite_actuelle,
                sd.nombre_sacs,
                sd.derniere_mise_a_jour
            FROM stock_detail sd
            JOIN depot d ON sd.depot_id = d.id
            JOIN produit p ON sd.produit_id = p.id
            JOIN lot l ON sd.lot_id = l.id
            WHERE 1=1 ${whereClause}
            ORDER BY d.nom, p.nom, l.numero
        `;

        stockTable.ajax.url('../includes/traitement.php').load();
    }

    function refreshCurrentTab() {
        switch(currentTab) {
            case 'products':
                loadProducts();
                break;
            case 'stock':
                loadStock();
                break;
            case 'transfers':
                loadTransfers();
                break;
            case 'adjustments':
                loadAdjustments();
                break;
        }
    }

    // Fonctions globales pour les boutons d'action
    window.editProduct = function(id) {
        showProductModal(id);
    };

    window.deleteProduct = function(id) {
        Swal.fire({
            title: 'Confirmer la suppression',
            text: 'Êtes-vous sûr de vouloir supprimer ce produit ?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'delete',
                    table: 'produit',
                    ids: [id]
                }, function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Succès',
                            text: 'Produit supprimé avec succès',
                            icon: 'success'
                        });
                        loadProducts();
                    } else {
                        Swal.fire({
                            title: 'Erreur',
                            text: response.message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    };

    window.deleteTransfer = function(id) {
        Swal.fire({
            title: 'Confirmer la suppression',
            text: 'Êtes-vous sûr de vouloir supprimer ce transfert ?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'deleteTransfer',
                    id: id
                }, function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Succès',
                            text: 'Transfert supprimé avec succès',
                            icon: 'success'
                        });
                        loadTransfers();
                        loadStock();
                    } else {
                        Swal.fire({
                            title: 'Erreur',
                            text: response.message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    };

    window.deleteAdjustment = function(id) {
        Swal.fire({
            title: 'Confirmer la suppression',
            text: 'Êtes-vous sûr de vouloir supprimer cet ajustement ?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'deleteAdjustment',
                    id: id
                }, function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Succès',
                            text: 'Ajustement supprimé avec succès',
                            icon: 'success'
                        });
                        loadAdjustments();
                        loadStock();
                    } else {
                        Swal.fire({
                            title: 'Erreur',
                            text: response.message,
                            icon: 'error'
                        });
                    }
                });
            }
        });
    };
});
<?php
session_start();
require_once dirname(__DIR__) . '/config/config.php';
require_once CONFIG_PATH . '/database.php';
require_once INCLUDES_PATH . '/auth.php';
require_once INCLUDES_PATH . '/rbac.php';

// Vérification de l'authentification
$auth = new Auth();
$auth->checkAuth();

// Vérification des permissions
$rbac = new RBAC();
$rbac->requirePermission('produit.view');

$currentUser = $_SESSION['username'];
?>
<!DOCTYPE html>
<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Gestion Produits</title>
    <!-- Bootstrap Cerulean -->
    <link href="https://cdn.jsdelivr.net/npm/bootswatch@5.3.2/dist/cerulean/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- CSS personnalisé -->
    <style>
        .badge {
            font-size: 0.75em;
            padding: 0.35em 0.65em;
        }
        .btn-sm i {
            font-size: 0.875em;
        }
        .table th {
            font-size: 0.875rem;
            font-weight: 600;
        }
        .table td {
            font-size: 0.875rem;
        }
        .btn-group .btn {
            margin-right: 0.25rem;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        .text-center .badge {
            display: inline-block;
            min-width: 50px;
        }
        .table-warning {
            background-color: rgba(255, 193, 7, 0.1) !important;
        }
        .table-success {
            background-color: rgba(25, 135, 84, 0.1) !important;
        }
        .small .badge {
            font-size: 0.7em;
            padding: 0.25em 0.5em;
        }
        .small .text-muted {
            font-size: 0.75em;
        }
    </style>
</head>

<body>
    <div class="container-fluid p-3">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0">📦 Gestion des Produits</h6>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalProduit" title="Nouveau produit">
                    <i class="fas fa-plus"></i> Nouveau
                </button>
                <button class="btn btn-outline-success" title="Cloner produit">
                    <i class="fas fa-copy"></i> Cloner
                </button>
                <button class="btn btn-outline-danger" title="Supprimer sélection">
                    <i class="fas fa-trash"></i> Supprimer
                </button>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mb-2">
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalUnites" title="Gérer les unités">
                    <i class="fas fa-ruler"></i> Unités
                </button>
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalCategories" title="Gérer les catégories">
                    <i class="fas fa-tags"></i> Catégories
                </button>
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalDepots" title="Gérer les dépôts">
                    <i class="fas fa-warehouse"></i> Dépôts
                </button>
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalAjustement" title="Ajustements de stock">
                    <i class="fas fa-adjust"></i> Ajustements
                </button>
                <button class="btn btn-outline-success" id="btnCloneSelected" title="Cloner les produits sélectionnés" disabled>
                    <i class="fas fa-copy"></i> Cloner
                </button>
                <button class="btn btn-outline-danger" id="btnDeleteSelected" title="Supprimer les produits sélectionnés" disabled>
                    <i class="fas fa-trash"></i> Supprimer
                </button>
            </div>
        </div>

        <div class="row g-2">
            <!-- Table Produits -->
            <div class="col-9">
                <div class="card shadow-sm border-0">
                    <div class="card-header py-2">
                        <h6 class="mb-0">Produits</h6>
                    </div>
                    <div class="card-body p-2">
                        <!-- Table Produits -->
                        <div class="table-responsive">
                            <table id="tableProduits" class="table table-sm table-hover align-middle table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th><input type="checkbox"></th>
                                        <th>Nom</th>
                                        <th>Type</th>
                                        <th>Certification</th>
                                        <th>Prix Vente</th>
                                        <th>Stock</th>
                                        <th>Unités & Conversion</th>
                                        <th>#</th>
                                    </tr>
                                </thead>
                                <tbody>
                                     
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Historique Mouvements -->
            <div class="col-3">
                <div class="card shadow-sm border-0">
                    <div class="card-header py-2">
                        <h6 class="mb-0"><i class="fas fa-history"></i> Historique des mouvements </h6>
                    </div>
                    <div class="card-body p-2">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Produit / Lot</th>
                                    <th>Qté</th>
                                    <th>Dépot</th>
                                    <th>Créé par</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>02/10/2025</td>
                                    <td>CACAO BIO<br/><small class="text-muted">Lot-001</small></td>
                                    <td>+10</td>
                                    <td>Magasin Central</td>
                                    <td>Rachid</td>
                                </tr>
                                <tr>
                                    <td>01/10/2025</td>
                                    <td>CACAO SUPERIEUR<br/><small class="text-muted">Lot-002</small></td>
                                    <td>-5</td>
                                    <td>Magasin Annexe</td>
                                    <td>Etienne</td>
                                </tr>
                                <tr>
                                    <td>28/09/2025</td>
                                    <td>CACAO STANDARD<br/><small class="text-muted">Lot-003</small></td>
                                    <td>+20</td>
                                    <td>Magasin Central</td>
                                    <td>Sara</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>




    </div>

    <!-- Modal Produit -->
    <div class="modal fade" id="modalProduit" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title">Produit</h6>
                    <button type="button" class="btn-close btn-sm" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form class="row g-2">
                        <div class="col-12">
                            <label class="form-label form-label-sm">Nom</label>
                            <input type="text" class="form-control form-control-sm" placeholder="Nom">
                        </div>



                        <div class="col-12">
                            <label class="form-label form-label-sm">Catégorie</label>
                            <select id="categorieSelect" class="form-select form-select-sm">
                                <option value="">Sélectionner une catégorie</option>
                            </select>
                        </div>

                        <div class="col-12 mb-1">
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label form-label-sm">Type</label>
                                    <select class="form-select form-select-sm">
                                        <option>Standard</option>
                                        <option>Supérieur</option>
                                    </select>
                                </div>

                                <div class="col-6">
                                    <label class="form-label form-label-sm">Certification</label>
                                    <select class="form-select form-select-sm">
                                        <option>Bio</option>
                                        <option>Conventionnel</option>
                                    </select>
                                </div>
                            </div>
                        </div>



                        <div class="col-12 mb-1">
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label form-label-sm">Prix Achat</label>
                                    <input type="number" class="form-control form-control-sm" placeholder="Prix Achat">
                                </div>
                                <div class="col-6">
                                    <label class="form-label form-label-sm">Prix Vente</label>
                                    <input type="number" class="form-control form-control-sm" placeholder="Prix Vente">
                                </div> 
                            </div>
                        </div>

                        <div class="col-12 mb-1">
                            <div class="row">
                                <div class="col-4">
                                    <label class="form-label form-label-sm">Unité Vente</label>
                                    <select id="uniteVenteSelect" class="form-select form-select-sm">
                                        <option value="">Sélectionner une unité</option>
                                    </select>
                                </div>

                                <div class="col-4">
                                    <label class="form-label form-label-sm">Unité Achat</label>
                                    <select id="uniteAchatSelect" class="form-select form-select-sm">
                                        <option value="">Sélectionner une unité</option>
                                    </select>
                                </div>

                                <div class="col-4">
                                    <label class="form-label form-label-sm">Unité Stock</label>
                                    <select id="uniteStockSelect" class="form-select form-select-sm">
                                        <option value="">Sélectionner une unité</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 mb-1">
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label form-label-sm">Présentation *</label>
                                    <select id="presentationSelect" class="form-select form-select-sm" required>
                                        <option value="">Sélectionner une présentation</option>
                                    </select>
                                </div>
                                <div class="col-6">
                                    <label class="form-label form-label-sm">Qte Présentation *</label>
                                    <input type="number" class="form-control form-control-sm" id="qtePresentation" placeholder="Quantité" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-1">
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label form-label-sm">Forme *</label>
                                    <select id="formeSelect" class="form-select form-select-sm" required>
                                        <option value="">Sélectionner une forme</option>
                                    </select>
                                </div>
                                <div class="col-6">
                                    <label class="form-label form-label-sm">Qte Forme *</label>
                                    <input type="number" class="form-control form-control-sm" id="qteForme" placeholder="Quantité" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-6">
                                <label class="form-label form-label-sm">Seuil Min</label>
                                <input type="number" class="form-control form-control-sm" placeholder="Seuil Min">
                            </div>
                            <div class="col-6">
                                <label class="form-label form-label-sm">Seuil Max</label>
                                <input type="number" class="form-control form-control-sm" placeholder="Seuil Max">
                            </div>
                        </div>
                
                </form>

                </div>
                <div class="modal-footer py-2">
                    <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-outline-primary btn-sm">💾 Sauver</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal View Produit -->
    <div class="modal fade" id="modalView" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title">Détails Produit par Dépôt / Lot</h6>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>Dépôt</th>
                                <th>Lot</th>
                                <th>Quantité</th>
                                <th>Date Entrée</th>
                                <th>Date Expiration</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Dépôt Central</td>
                                <td>Lot-001</td>
                                <td>50</td>
                                <td>2025-01-12</td>
                                <td>2026-01-12</td>
                            </tr>
                            <tr>
                                <td>Dépôt Annexe</td>
                                <td>Lot-002</td>
                                <td>200</td>
                                <td>2025-03-01</td>
                                <td>2026-03-01</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer py-2">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ Modal CRUD Unités -->
    <div class="modal fade" id="modalUnites" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Gestion des Unités</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <table id="unitesTable" class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <th>Libellé</th>
                                <th>Actif</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Kilogramme</td>
                                <td>✔</td>
                                <td>
                                    <button class="btn btn-sm btn-warning btnEditUnite" data-id="1" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger btnDeleteUnite" data-id="1" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>Litre</td>
                                <td>✔</td>
                                <td>
                                    <button class="btn btn-sm btn-warning btnEditUnite" data-id="2" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger btnDeleteUnite" data-id="2" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <button id="btnAddUnite" class="btn btn-sm btn-success mt-2" title="Ajouter une unité">
                        <i class="fas fa-plus"></i> Ajouter une unité
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ Modal CRUD Catégories -->
    <div class="modal fade" id="modalCategories" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Gestion des Catégories</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <table id="categoriesTable" class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <th>Libellé</th>
                                <th>Classement</th>
                                <th>Actif</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Alimentaire</td>
                                <td>PRODUITS</td>
                                <td>✔</td>
                                <td>
                                    <button class="btn btn-sm btn-warning btnEditCategorie" data-id="1" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger btnDeleteCategorie" data-id="1" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>Équipements</td>
                                <td>FOURNITURES</td>
                                <td>✔</td>
                                <td>
                                    <button class="btn btn-sm btn-warning btnEditCategorie" data-id="2" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger btnDeleteCategorie" data-id="2" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <button id="btnAddCategorie" class="btn btn-sm btn-success mt-2" title="Ajouter une catégorie">
                        <i class="fas fa-plus"></i> Ajouter une catégorie
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ Modal CRUD Dépôts -->
    <div class="modal fade" id="modalDepots" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Gestion des Dépôts</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <table id="depotsTable" class="table table-striped table-bordered table-sm">
                        <thead>
                            <tr>
                                <th>Libellé</th>
                                <th>Actif</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Dépôt Central</td>
                                <td>✔</td>
                                <td>
                                    <button class="btn btn-sm btn-warning btnEditDepot" data-id="1" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger btnDeleteDepot" data-id="1" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>Dépôt Antananarivo</td>
                                <td>✔</td>
                                <td>
                                    <button class="btn btn-sm btn-warning btnEditDepot" data-id="2" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger btnDeleteDepot" data-id="2" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <button id="btnAddDepot" class="btn btn-sm btn-success mt-2" title="Ajouter un dépôt">
                        <i class="fas fa-plus"></i> Ajouter un dépôt
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- ✅ Modal Ajustement de Stock -->
    <div class="modal fade" id="modalAjustement" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-adjust"></i> Ajustement de Stock
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="formAjustement">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Produit</label>
                                <select id="ajustementProduit" class="form-select" required>
                                    <option value="">Sélectionner un produit</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Dépôt</label>
                                <select id="ajustementDepot" class="form-select" required>
                                    <option value="">Sélectionner un dépôt</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Type d'ajustement</label>
                                <select id="ajustementType" class="form-select" required>
                                    <option value="">Sélectionner le type</option>
                                    <option value="ENTREE">Entrée (+)</option>
                                    <option value="SORTIE">Sortie (-)</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Quantité</label>
                                <input type="number" id="ajustementQuantite" class="form-control" step="0.01" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Unité</label>
                                <select id="ajustementUnite" class="form-select" required>
                                    <option value="">Sélectionner une unité</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Motif de l'ajustement</label>
                                <textarea id="ajustementMotif" class="form-control" rows="3" placeholder="Raison de l'ajustement..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSaveAjustement">
                        <i class="fas fa-save"></i> Enregistrer l'ajustement
                    </button>
                </div>
            </div>
        </div>
    </div>


    <!-- JS Bootstrap + DataTables + SweetAlert -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="../assets/js/gestion_produits.js?v=<?php echo date('YmdHis'); ?>"></script>


</body>

</html>
<?php
/**
 * Test des fonctionnalités de changement de statut et d'édition des paiements de ventes
 */

session_start();
require_once 'config/database.php';

echo "<h1>🧪 Test des Fonctionnalités de Paiements de Ventes</h1>";

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h2>✅ Connexion à la base de données réussie</h2>";
    
    // Test 1: Créer des données de test si nécessaire
    echo "<h3>📋 Test 1: Préparation des données de test</h3>";
    
    // Vérifier s'il y a des clients
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM clients");
    $clientCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($clientCount == 0) {
        echo "<p style='color: orange;'>⚠️ Aucun client trouvé. Création d'un client de test...</p>";
        $pdo->exec("INSERT INTO clients (nom, adresse, telephone, type_client, cree_par) 
                   VALUES ('Client Test', '123 Rue Test', '0123456789', 'Local', 'admin')");
        echo "<p style='color: green;'>✅ Client de test créé</p>";
    } else {
        echo "<p style='color: green;'>✅ {$clientCount} client(s) disponible(s)</p>";
    }
    
    // Vérifier s'il y a des produits
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
    $produitCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($produitCount == 0) {
        echo "<p style='color: orange;'>⚠️ Aucun produit trouvé. Veuillez créer des produits d'abord.</p>";
    } else {
        echo "<p style='color: green;'>✅ {$produitCount} produit(s) disponible(s)</p>";
    }
    
    // Test 2: Vérifier les ventes avec statut FACTURE
    echo "<h3>📋 Test 2: Ventes facturées disponibles</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ventes_entete WHERE statut = 'FACTURE'");
    $ventesFacturees = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($ventesFacturees == 0) {
        echo "<p style='color: orange;'>⚠️ Aucune vente avec statut FACTURE trouvée.</p>";
        echo "<p>💡 Pour tester les paiements, vous devez d'abord :</p>";
        echo "<ol>";
        echo "<li>Créer une vente dans le système de gestion des ventes</li>";
        echo "<li>Changer son statut à 'FACTURE'</li>";
        echo "<li>Puis revenir ici pour créer un paiement</li>";
        echo "</ol>";
    } else {
        echo "<p style='color: green;'>✅ {$ventesFacturees} vente(s) facturée(s) disponible(s) pour paiement</p>";
        
        // Afficher quelques exemples
        $stmt = $pdo->query("SELECT ve.id, ve.facture_numero, c.nom as client_nom, ve.total_montant 
                            FROM ventes_entete ve 
                            LEFT JOIN clients c ON ve.client_id = c.id 
                            WHERE ve.statut = 'FACTURE' 
                            LIMIT 3");
        $ventes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<ul>";
        foreach ($ventes as $vente) {
            $montant = number_format($vente['total_montant'] ?? 0, 0, ',', ' ');
            $facture = $vente['facture_numero'] ?: "Vente #{$vente['id']}";
            echo "<li>{$facture} - {$vente['client_nom']} - {$montant} Ar</li>";
        }
        echo "</ul>";
    }
    
    // Test 3: Vérifier les paiements existants et leurs statuts
    echo "<h3>📋 Test 3: Paiements existants et statuts</h3>";
    
    $stmt = $pdo->query("SELECT 
                            oc.id, 
                            oc.statut, 
                            oc.montant,
                            ve.facture_numero,
                            c.nom as client_nom
                        FROM operation_caisse oc
                        LEFT JOIN ventes_entete ve ON oc.vente_id = ve.id
                        LEFT JOIN clients c ON oc.client_id = c.id
                        WHERE oc.type_operation = 'ENCAISSEMENT_VENTE'
                        ORDER BY oc.id DESC
                        LIMIT 5");
    $paiements = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($paiements)) {
        echo "<p style='color: orange;'>⚠️ Aucun paiement de vente trouvé</p>";
        echo "<p>💡 Créez d'abord un paiement pour tester les fonctionnalités d'édition et de changement de statut</p>";
    } else {
        echo "<p style='color: green;'>✅ " . count($paiements) . " paiement(s) trouvé(s)</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Facture</th><th>Client</th><th>Montant</th><th>Statut</th><th>Actions Possibles</th>";
        echo "</tr>";
        
        foreach ($paiements as $paiement) {
            $montant = number_format($paiement['montant'], 0, ',', ' ') . ' Ar';
            $facture = $paiement['facture_numero'] ?: "Vente #{$paiement['id']}";
            
            // Déterminer les actions possibles selon le statut
            $actionsPossibles = [];
            switch ($paiement['statut']) {
                case 'EN_ATTENTE':
                    $actionsPossibles = ['Édition', 'Validation', 'Annulation'];
                    $couleur = '#ffc107'; // Jaune
                    break;
                case 'VALIDE':
                    $actionsPossibles = ['Annulation'];
                    $couleur = '#28a745'; // Vert
                    break;
                case 'ANNULE':
                    $actionsPossibles = ['Remise en attente'];
                    $couleur = '#dc3545'; // Rouge
                    break;
                default:
                    $actionsPossibles = [];
                    $couleur = '#6c757d'; // Gris
            }
            
            echo "<tr>";
            echo "<td>{$paiement['id']}</td>";
            echo "<td>{$facture}</td>";
            echo "<td>{$paiement['client_nom']}</td>";
            echo "<td>{$montant}</td>";
            echo "<td style='background-color: {$couleur}; color: white; text-align: center;'>{$paiement['statut']}</td>";
            echo "<td>" . implode(', ', $actionsPossibles) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test 4: Vérifier les transitions de statut possibles
    echo "<h3>📋 Test 4: Transitions de statut implémentées</h3>";
    
    $transitions = [
        'EN_ATTENTE' => ['VALIDE', 'ANNULE'],
        'VALIDE' => ['ANNULE'],
        'ANNULE' => ['EN_ATTENTE']
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th>Statut Actuel</th><th>Transitions Possibles</th><th>Description</th>";
    echo "</tr>";
    
    foreach ($transitions as $statut => $possibles) {
        $description = '';
        switch ($statut) {
            case 'EN_ATTENTE':
                $description = 'Paiement créé, en attente de validation';
                break;
            case 'VALIDE':
                $description = 'Paiement validé et confirmé';
                break;
            case 'ANNULE':
                $description = 'Paiement annulé ou rejeté';
                break;
        }
        
        echo "<tr>";
        echo "<td style='font-weight: bold;'>{$statut}</td>";
        echo "<td>" . implode(', ', $possibles) . "</td>";
        echo "<td>{$description}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test 5: Fonctionnalités implémentées
    echo "<h3>📋 Test 5: Fonctionnalités implémentées</h3>";
    
    $fonctionnalites = [
        '✅ Création de nouveaux paiements' => 'Formulaire complet avec validation',
        '✅ Édition des paiements EN_ATTENTE' => 'Modification possible uniquement si statut = EN_ATTENTE',
        '✅ Changement de statut' => 'Transitions contrôlées selon les règles métier',
        '✅ Validation avec commentaire' => 'Possibilité d\'ajouter un commentaire lors du changement',
        '✅ Affichage conditionnel des boutons' => 'Bouton Édition visible seulement pour EN_ATTENTE',
        '✅ Calcul automatique des totaux' => 'Mise à jour en temps réel des montants',
        '✅ Conversion devise' => 'Calcul EUR/AR intégré',
        '✅ Actions CRUD sécurisées' => 'Utilisation correcte des actions create/update/delete'
    ];
    
    echo "<ul>";
    foreach ($fonctionnalites as $titre => $description) {
        echo "<li><strong>{$titre}:</strong> {$description}</li>";
    }
    echo "</ul>";
    
    echo "<h2>🎯 Instructions de Test</h2>";
    echo "<div style='background-color: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50;'>";
    echo "<h4>Pour tester les fonctionnalités :</h4>";
    echo "<ol>";
    echo "<li><strong>Accédez à :</strong> <a href='pages/gestion_paiements_ventes.php' target='_blank'>pages/gestion_paiements_ventes.php</a></li>";
    echo "<li><strong>Créer un paiement :</strong> Cliquez 'Nouveau Paiement' → Remplissez le formulaire → Enregistrez</li>";
    echo "<li><strong>Éditer un paiement :</strong> Cliquez le bouton 'Édition' (visible seulement pour statut EN_ATTENTE)</li>";
    echo "<li><strong>Changer le statut :</strong> Cliquez le bouton 'Changer statut' → Sélectionnez nouveau statut → Confirmez</li>";
    echo "<li><strong>Vérifier les restrictions :</strong> Tentez d'éditer un paiement VALIDE (bouton absent)</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>🚨 Points d'Attention</h2>";
    echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107;'>";
    echo "<ul>";
    echo "<li><strong>Édition limitée :</strong> Seuls les paiements EN_ATTENTE peuvent être modifiés</li>";
    echo "<li><strong>Transitions contrôlées :</strong> Pas toutes les transitions de statut sont possibles</li>";
    echo "<li><strong>Validation métier :</strong> Un paiement VALIDE met automatiquement la vente en statut PAYE</li>";
    echo "<li><strong>Commentaires :</strong> Recommandé d'ajouter un commentaire lors des changements de statut</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
h3 { color: #7f8c8d; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
ul, ol { margin-left: 20px; }
p { margin: 10px 0; }
</style>

<?php
/**
 * Script de vérification et extension de la table operation_caisse
 * pour supporter les paiements de ventes
 */

require_once 'config/config.php';
require_once CONFIG_PATH . '/database.php';

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<h2>✅ Connexion à la base de données réussie</h2>";
} catch (PDOException $e) {
    die("<h2>❌ Erreur de connexion : " . $e->getMessage() . "</h2>");
}

echo "<h1>Vérification de la Structure de operation_caisse</h1>";

// 1. Vérifier la structure actuelle
echo "<h3>1. Structure actuelle de la table operation_caisse</h3>";

try {
    $stmt = $pdo->query("DESCRIBE operation_caisse");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
    
    $existingColumns = [];
    foreach ($columns as $column) {
        $existingColumns[] = $column['Field'];
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>Colonnes existantes:</strong> " . implode(', ', $existingColumns) . "</p>";
    
} catch (PDOException $e) {
    echo "❌ Erreur lors de la vérification : " . $e->getMessage() . "<br>";
}

// 2. Vérifier les colonnes requises pour les ventes
echo "<h3>2. Vérification des colonnes requises pour les ventes</h3>";

$requiredColumns = [
    'vente_id' => 'int(11) DEFAULT NULL',
    'client_id' => 'int(11) DEFAULT NULL'
];

$missingColumns = [];
$existingColumns = [];

foreach ($requiredColumns as $columnName => $columnDef) {
    $stmt = $pdo->query("SHOW COLUMNS FROM operation_caisse LIKE '$columnName'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Colonne '$columnName' existe<br>";
        $existingColumns[] = $columnName;
    } else {
        echo "❌ Colonne '$columnName' manquante<br>";
        $missingColumns[] = $columnName;
    }
}

// 3. Vérifier le type_operation ENUM
echo "<h3>3. Vérification du type_operation ENUM</h3>";

try {
    $stmt = $pdo->query("SHOW COLUMNS FROM operation_caisse LIKE 'type_operation'");
    $typeColumn = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($typeColumn) {
        echo "✅ Colonne type_operation trouvée: " . $typeColumn['Type'] . "<br>";
        
        if (strpos($typeColumn['Type'], 'ENCAISSEMENT_VENTE') !== false) {
            echo "✅ Valeur 'ENCAISSEMENT_VENTE' présente dans l'ENUM<br>";
        } else {
            echo "❌ Valeur 'ENCAISSEMENT_VENTE' manquante dans l'ENUM<br>";
            echo "Type actuel: " . $typeColumn['Type'] . "<br>";
        }
    } else {
        echo "❌ Colonne type_operation non trouvée<br>";
    }
} catch (PDOException $e) {
    echo "❌ Erreur lors de la vérification du type_operation : " . $e->getMessage() . "<br>";
}

// 4. Proposer les corrections si nécessaire
if (!empty($missingColumns)) {
    echo "<h3>4. Corrections nécessaires</h3>";
    echo "<p>❌ Les colonnes suivantes sont manquantes: " . implode(', ', $missingColumns) . "</p>";
    
    echo "<h4>Scripts SQL à exécuter:</h4>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
    
    foreach ($missingColumns as $column) {
        $def = $requiredColumns[$column];
        echo "ALTER TABLE operation_caisse ADD COLUMN $column $def;<br>";
    }
    
    echo "</div>";
    
    // Option d'exécution automatique
    if (isset($_GET['auto_fix']) && $_GET['auto_fix'] === 'yes') {
        echo "<h4>Exécution automatique des corrections...</h4>";
        
        try {
            foreach ($missingColumns as $column) {
                $def = $requiredColumns[$column];
                $sql = "ALTER TABLE operation_caisse ADD COLUMN $column $def";
                $pdo->exec($sql);
                echo "✅ Colonne '$column' ajoutée avec succès<br>";
            }
            
            echo "<p style='color: green;'><strong>✅ Toutes les corrections ont été appliquées avec succès!</strong></p>";
            echo "<p><a href='verify_operation_caisse_structure.php'>🔄 Revérifier la structure</a></p>";
            
        } catch (PDOException $e) {
            echo "❌ Erreur lors de l'ajout des colonnes : " . $e->getMessage() . "<br>";
        }
    } else {
        echo "<p><a href='verify_operation_caisse_structure.php?auto_fix=yes' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Appliquer les corrections automatiquement</a></p>";
    }
    
} else {
    echo "<h3>4. Résultat</h3>";
    echo "<p style='color: green;'>✅ <strong>Toutes les colonnes requises sont présentes!</strong></p>";
}

// 5. Test de création d'un paiement de vente
echo "<h3>5. Test de création d'un paiement de vente</h3>";

if (empty($missingColumns)) {
    try {
        // Vérifier s'il y a des ventes avec statut FACTURE
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM ventes_entete WHERE statut = 'FACTURE'");
        $ventesFacturees = $stmt->fetch()['count'];
        
        echo "Ventes facturées disponibles: $ventesFacturees<br>";
        
        if ($ventesFacturees > 0) {
            echo "✅ Il y a des ventes prêtes pour paiement<br>";
        } else {
            echo "⚠️ Aucune vente avec statut 'FACTURE' trouvée<br>";
            echo "Créez d'abord une vente et facturez-la pour tester les paiements<br>";
        }
        
        // Vérifier les paiements de ventes existants
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM operation_caisse WHERE type_operation = 'ENCAISSEMENT_VENTE'");
        $paiementsVentes = $stmt->fetch()['count'];
        
        echo "Paiements de ventes existants: $paiementsVentes<br>";
        
    } catch (PDOException $e) {
        echo "❌ Erreur lors du test : " . $e->getMessage() . "<br>";
    }
} else {
    echo "⚠️ Impossible de tester tant que les colonnes manquantes ne sont pas ajoutées<br>";
}

echo "<hr>";
echo "<h3>✅ Vérification terminée</h3>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h5>🚀 Accès aux pages:</h5>";
echo "<p><a href='pages/gestion_ventes.php' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>📊 Gestion des Ventes</a></p>";
echo "<p><a href='pages/gestion_paiements_ventes.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;'>💳 Gestion des Paiements de Ventes</a></p>";
echo "<p><a href='test_sales_payment_system.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;'>🧪 Test Complet du Système</a></p>";
echo "</div>";
echo "<p><small>Vérification effectuée le " . date('Y-m-d H:i:s') . "</small></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Vérification operation_caisse</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        h3 { color: #0056b3; margin-top: 20px; }
    </style>
</head>
<body>
    <!-- Le contenu PHP est affiché ci-dessus -->
</body>
</html>

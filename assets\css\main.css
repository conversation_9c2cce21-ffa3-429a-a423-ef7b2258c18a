/* ===================================================================
   ERP3 - Fichier CSS Principal
   Interface moderne et compacte basée sur Bootstrap 5
   =================================================================== */

/* Import du style Bootstrap 5 compact */
@import url('bootstrap5-compact.css');

/* Import des styles DataTables personnalisés */
@import url('datatables-custom.css');

/* ===================================================================
   STYLES SPÉCIFIQUES AU SYSTÈME ERP
   =================================================================== */

/* Styles pour les pages métier */
.page-header {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.page-header h1 {
    margin: 0;
    color: var(--text-color);
    font-size: var(--font-size-xxl);
    font-weight: 600;
}

.page-header p {
    margin: var(--spacing-sm) 0 0 0;
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

/* Styles pour les formulaires de recherche */
.search-form {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.search-form .form-group {
    margin-bottom: var(--spacing-md);
}

.search-form .form-group:last-child {
    margin-bottom: 0;
}

/* Styles pour les tableaux de données */
.data-table-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.data-table-container .table-header {
    background: var(--gray-100);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.data-table-container .table-header h6 {
    margin: 0;
    font-weight: 600;
    color: var(--text-color);
}

.data-table-container .table-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Styles pour les modales */
.modal-backdrop {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

.modal-dialog {
    margin: var(--spacing-lg) auto;
}

.modal-dialog.modal-lg {
    max-width: 800px;
}

.modal-dialog.modal-xl {
    max-width: 1200px;
}

.modal-dialog.modal-fullscreen {
    max-width: 100vw;
    max-height: 100vh;
    margin: 0;
}

.modal-dialog.modal-fullscreen .modal-content {
    height: 100vh;
    border-radius: 0;
}

/* Styles pour les alertes */
.alert-container {
    position: fixed;
    top: 100px;
    right: var(--spacing-lg);
    z-index: 9999;
    max-width: 400px;
}

.alert {
    margin-bottom: var(--spacing-sm);
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.alert-success::before { background-color: var(--success-color); }
.alert-danger::before { background-color: var(--danger-color); }
.alert-warning::before { background-color: var(--warning-color); }
.alert-info::before { background-color: var(--info-color); }

/* Styles pour les boutons d'action */
.action-buttons {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.action-buttons .btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Styles pour les badges de statut */
.status-badge {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
}

.status-badge.brouillon {
    background-color: var(--gray-500);
    color: var(--text-light);
}

.status-badge.en_cours {
    background-color: var(--warning-color);
    color: var(--gray-900);
}

.status-badge.valide {
    background-color: var(--success-color);
    color: var(--text-light);
}

.status-badge.a_payer {
    background-color: var(--info-color);
    color: var(--text-light);
}

.status-badge.paye {
    background-color: var(--primary-color);
    color: var(--text-light);
}

/* Styles pour les niveaux de stock */
.stock-level {
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.stock-level.low {
    color: var(--danger-color);
}

.stock-level.medium {
    color: var(--warning-color);
}

.stock-level.high {
    color: var(--success-color);
}

/* Styles pour les cartes de statistiques */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
    transition: var(--transition-base);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.stats-card .card-body {
    padding: var(--spacing-lg);
    position: relative;
    z-index: 1;
    text-align: center;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.stats-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin: 0;
}

/* Styles pour les filtres */
.filter-section {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.filter-section .form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.filter-section .form-control,
.filter-section .form-select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
    transition: var(--transition-base);
}

.filter-section .form-control:focus,
.filter-section .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
    outline: none;
}

/* Styles pour les tableaux de produits */
.products-table {
    margin-top: var(--spacing-md);
}

.products-table .table th {
    background-color: var(--gray-100);
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 2px solid var(--border-color);
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.products-table .table td {
    padding: var(--spacing-sm);
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    font-size: var(--font-size-sm);
}

.products-table .form-control,
.products-table .form-select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    transition: var(--transition-base);
}

.products-table .form-control:focus,
.products-table .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
    outline: none;
}

/* Styles pour les badges de certification et qualité */
.certification-badge,
.qualite-badge {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    display: inline-block;
}

.certification-badge {
    background-color: var(--info-color);
    color: var(--text-light);
}

.qualite-badge {
    background-color: var(--warning-color);
    color: var(--gray-900);
}

/* Styles pour les totaux */
.totals-row {
    background-color: var(--gray-100);
    font-weight: 600;
    border-top: 2px solid var(--border-color);
}

.totals-row td {
    padding: var(--spacing-sm);
    font-weight: 600;
}

/* Styles pour les messages d'état */
.status-message {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.status-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-message.warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-message.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* ===================================================================
   RESPONSIVE DESIGN
   =================================================================== */

@media (max-width: 768px) {
    .page-header {
        padding: var(--spacing-md);
    }
    
    .page-header h1 {
        font-size: var(--font-size-xl);
    }
    
    .search-form {
        padding: var(--spacing-md);
    }
    
    .data-table-container .table-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
    
    .data-table-container .table-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .filter-section {
        padding: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .page-header h1 {
        font-size: var(--font-size-lg);
    }
    
    .stats-card .card-body {
        padding: var(--spacing-md);
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
    
    .modal-dialog {
        margin: var(--spacing-sm) auto;
    }
    
    .alert-container {
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        max-width: none;
    }
}

/* ===================================================================
   PRINT STYLES
   =================================================================== */

@media print {
    .action-buttons,
    .alert-container,
    .modal,
    .btn {
        display: none !important;
    }
    
    .page-header,
    .search-form,
    .data-table-container {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .stats-card {
        background: #f8f9fa !important;
        color: #000 !important;
    }
    
    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}

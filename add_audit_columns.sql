-- Script pour ajouter les colonnes de traçabilité aux tables qui n'en ont pas
-- Exécuter ce script sur la base de données erp_cacao

-- Vérifier et ajouter les colonnes de traçabilité aux tables principales

-- Table unites (si pas déjà présentes)
ALTER TABLE `unites` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table depot (si pas déjà présentes)
ALTER TABLE `depot` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table forme (si pas déjà présentes)
ALTER TABLE `forme` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table presentation (si pas déjà présentes)
ALTER TABLE `presentation` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table produits_stock (si pas déjà présentes)
ALTER TABLE `produits_stock` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table roles (si pas déjà présentes)
ALTER TABLE `roles` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table permissions (si pas déjà présentes)
ALTER TABLE `permissions` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table role_permissions (si pas déjà présentes)
ALTER TABLE `role_permissions` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table user_permissions (si pas déjà présentes)
ALTER TABLE `user_permissions` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table communes (si pas déjà présentes)
ALTER TABLE `communes` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table cooperatives (si pas déjà présentes)
ALTER TABLE `cooperatives` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table fournisseurs (si pas déjà présentes)
ALTER TABLE `fournisseurs` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Supprimer les anciennes colonnes ID si elles existent
ALTER TABLE `fournisseurs` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table producteurs (si pas déjà présentes)
ALTER TABLE `producteurs` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Supprimer les anciennes colonnes ID si elles existent
ALTER TABLE `producteurs` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table production_globale (si pas déjà présentes)
ALTER TABLE `production_globale` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Supprimer les anciennes colonnes ID si elles existent
ALTER TABLE `production_globale` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table production_mensuelle (si pas déjà présentes)
ALTER TABLE `production_mensuelle` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Supprimer les anciennes colonnes ID si elles existent
ALTER TABLE `production_mensuelle` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table ventes_entete (si pas déjà présentes)
ALTER TABLE `ventes_entete` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table ventes_details (si pas déjà présentes)
ALTER TABLE `ventes_details` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Vérifier que les colonnes ont été ajoutées
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'erp_cacao' 
    AND COLUMN_NAME IN ('date_creation', 'cree_par', 'date_derniere_modif', 'dernier_modif_par')
ORDER BY TABLE_NAME, COLUMN_NAME;

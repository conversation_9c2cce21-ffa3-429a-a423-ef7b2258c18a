/* ===================================================================
   Login Page Styles - Intégration avec le thème ERP
   =================================================================== */

/* Import du thème principal */
@import url('erp-theme.css');

/* Styles spécifiques à la page de connexion */
body {
    background: white;
    min-height: 100vh;
    font-family: var(--font-family);
    position: relative;
    overflow: hidden;
}

/* Effet de fond animé */
body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(1deg); }
}

.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    position: relative;
    z-index: 1;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    max-width: 400px;
    width: 100%;
    animation: slideUp 0.6s ease-out;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-light);
    padding: var(--spacing-xl);
    text-align: center;
    position: relative;
}

.login-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.login-header h1 {
    margin: 0;
    font-size: var(--font-size-xxl);
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.login-header p {
    margin: var(--spacing-sm) 0 0 0;
    opacity: 0.9;
    position: relative;
    z-index: 1;
    font-size: var(--font-size-sm);
}

.login-body {
    padding: var(--spacing-xl);
}

.form-floating {
    margin-bottom: var(--spacing-lg);
}

.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    transition: var(--transition-base);
    background-color: var(--gray-100);
    color: var(--text-color);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
    background-color: var(--white);
}

.btn-login {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-light);
    width: 100%;
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-login:hover::before {
    left: 100%;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-light);
}

.btn-login:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-login:disabled:hover {
    transform: none;
    box-shadow: none;
}

.alert {
    border-radius: var(--border-radius);
    border: none;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    font-size: var(--font-size-sm);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-danger::before {
    background-color: var(--danger-color);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-success::before {
    background-color: var(--success-color);
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-warning::before {
    background-color: var(--warning-color);
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

.alert-info::before {
    background-color: var(--info-color);
}

.cacao-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    color: #d4af37;
    animation: bounce 2s infinite;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.login-footer {
    text-align: center;
    padding: var(--spacing-md) var(--spacing-xl) var(--spacing-xl);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.demo-accounts {
    background: var(--gray-100);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
    font-size: var(--font-size-xs);
}

.demo-accounts h6 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
}

.demo-account {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid var(--border-color);
}

.demo-account:last-child {
    border-bottom: none;
}

.demo-account strong {
    color: var(--secondary-color);
}

.loading {
    display: none;
}

.btn-login.loading .loading {
    display: inline-block;
}

.btn-login.loading .btn-text {
    display: none;
}

/* Animation de chargement */
.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid var(--text-light);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: var(--spacing-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Effet de focus sur le conteneur */
.login-card:focus-within {
    transform: scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

/* Styles pour les icônes */
.input-group-text {
    background-color: var(--gray-100);
    border: 2px solid var(--border-color);
    border-right: none;
    color: var(--text-muted);
    transition: var(--transition-base);
}

.input-group:focus-within .input-group-text {
    border-color: var(--primary-color);
    background-color: var(--white);
    color: var(--primary-color);
}

.input-group .form-control {
    border-left: none;
}

.input-group:focus-within .form-control {
    border-left: none;
}

/* Responsive */
@media (max-width: 480px) {
    .login-container {
        padding: var(--spacing-sm);
    }
    
    .login-card {
        margin: 0;
    }
    
    .login-header {
        padding: var(--spacing-lg);
    }
    
    .login-header h1 {
        font-size: var(--font-size-xl);
    }
    
    .login-body {
        padding: var(--spacing-lg);
    }
    
    .cacao-icon {
        font-size: 2.5rem;
    }
    
    .form-control {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .btn-login {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-base);
    }
}

@media (max-width: 320px) {
    .login-container {
        padding: var(--spacing-xs);
    }
    
    .login-header h1 {
        font-size: var(--font-size-lg);
    }
    
    .cacao-icon {
        font-size: 2rem;
    }
}

/* Styles pour l'accessibilité */
@media (prefers-reduced-motion: reduce) {
    .cacao-icon {
        animation: none;
    }
    
    body::before {
        animation: none;
    }
    
    .btn-login::before {
        transition: none;
    }
    
    .login-card {
        animation: none;
    }
}

/* Styles pour le mode sombre (si supporté) */
@media (prefers-color-scheme: dark) {
    .login-card {
        background: rgba(52, 58, 64, 0.95);
        color: var(--text-light);
    }
    
    .login-header h1 {
        color: var(--text-light);
    }
    
    .form-control {
        background-color: var(--gray-700);
        border-color: var(--gray-600);
        color: var(--text-light);
    }
    
    .form-control:focus {
        background-color: var(--gray-600);
        border-color: var(--primary-color);
    }
    
    .form-floating label {
        color: var(--text-light);
    }
    
    .demo-accounts {
        background-color: var(--gray-700);
    }
}
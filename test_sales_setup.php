<?php
/**
 * Script de test et configuration pour le système de gestion des ventes
 * Ce script vérifie et configure les tables nécessaires pour les ventes
 */

require_once 'config/config.php';
require_once CONFIG_PATH . '/database.php';

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<h2>✅ Connexion à la base de données réussie</h2>";
} catch (PDOException $e) {
    die("<h2>❌ Erreur de connexion : " . $e->getMessage() . "</h2>");
}

echo "<h1>Test et Configuration du Système de Ventes</h1>";

// 1. Vérifier l'existence des tables
echo "<h3>1. Vérification des tables</h3>";

$tables_required = ['ventes_entete', 'ventes_details', 'operation_caisse', 'clients', 'produits'];
$tables_exist = [];

foreach ($tables_required as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $exists = $stmt->rowCount() > 0;
        $tables_exist[$table] = $exists;
        
        if ($exists) {
            echo "✅ Table <strong>$table</strong> existe<br>";
        } else {
            echo "❌ Table <strong>$table</strong> n'existe pas<br>";
        }
    } catch (PDOException $e) {
        echo "❌ Erreur lors de la vérification de la table $table : " . $e->getMessage() . "<br>";
        $tables_exist[$table] = false;
    }
}

// 2. Vérifier la structure de operation_caisse
echo "<h3>2. Vérification de la structure operation_caisse</h3>";

if ($tables_exist['operation_caisse']) {
    try {
        $stmt = $pdo->query("DESCRIBE operation_caisse");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $required_columns = ['vente_id', 'client_id'];
        $has_vente_support = true;
        
        foreach ($required_columns as $col) {
            $found = false;
            foreach ($columns as $column) {
                if ($column['Field'] === $col) {
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $has_vente_support = false;
                echo "❌ Colonne <strong>$col</strong> manquante<br>";
            } else {
                echo "✅ Colonne <strong>$col</strong> présente<br>";
            }
        }
        
        // Vérifier l'enum type_operation
        $type_operation_column = null;
        foreach ($columns as $column) {
            if ($column['Field'] === 'type_operation') {
                $type_operation_column = $column;
                break;
            }
        }
        
        if ($type_operation_column) {
            if (strpos($type_operation_column['Type'], 'ENCAISSEMENT_VENTE') !== false) {
                echo "✅ Type d'opération <strong>ENCAISSEMENT_VENTE</strong> supporté<br>";
            } else {
                echo "❌ Type d'opération <strong>ENCAISSEMENT_VENTE</strong> non supporté<br>";
                echo "Type actuel : " . $type_operation_column['Type'] . "<br>";
                $has_vente_support = false;
            }
        }
        
        if (!$has_vente_support) {
            echo "<p><strong>⚠️ La table operation_caisse doit être mise à jour pour supporter les ventes.</strong></p>";
            echo "<p>Exécutez le script : <code>sql/extend_operation_caisse_for_sales.sql</code></p>";
        }
        
    } catch (PDOException $e) {
        echo "❌ Erreur lors de la vérification de operation_caisse : " . $e->getMessage() . "<br>";
    }
}

// 3. Vérifier les données de test
echo "<h3>3. Vérification des données de test</h3>";

// Clients
if ($tables_exist['clients']) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM clients");
        $count = $stmt->fetch()['count'];
        echo "✅ Clients disponibles : <strong>$count</strong><br>";
        
        if ($count == 0) {
            echo "⚠️ Aucun client trouvé. Ajout d'un client de test...<br>";
            $pdo->exec("INSERT INTO clients (nom, type_client, adresse, telephone, email) VALUES 
                       ('Client Test Export', 'EXPORTATEUR', '123 Rue Test', '+261 34 12 345 67', '<EMAIL>')");
            echo "✅ Client de test ajouté<br>";
        }
    } catch (PDOException $e) {
        echo "❌ Erreur clients : " . $e->getMessage() . "<br>";
    }
}

// Produits
if ($tables_exist['produits']) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM produits");
        $count = $stmt->fetch()['count'];
        echo "✅ Produits disponibles : <strong>$count</strong><br>";
        
        if ($count == 0) {
            echo "⚠️ Aucun produit trouvé. Ajout de produits de test...<br>";
            $pdo->exec("INSERT INTO produits (nom, prix_unitaire, unite) VALUES 
                       ('Cacao Grade 1', 2500.00, 'kg'),
                       ('Cacao Grade 2', 2200.00, 'kg'),
                       ('Cacao Trieur', 1800.00, 'kg')");
            echo "✅ Produits de test ajoutés<br>";
        }
    } catch (PDOException $e) {
        echo "❌ Erreur produits : " . $e->getMessage() . "<br>";
    }
}

// 4. Test de création d'une vente
echo "<h3>4. Test de création d'une vente</h3>";

if ($tables_exist['ventes_entete'] && $tables_exist['ventes_details']) {
    try {
        // Vérifier s'il y a déjà des ventes de test
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM ventes_entete WHERE facture_numero LIKE 'TEST-%'");
        $test_ventes = $stmt->fetch()['count'];
        
        if ($test_ventes == 0) {
            echo "Création d'une vente de test...<br>";
            
            // Récupérer un client et un produit
            $stmt = $pdo->query("SELECT id FROM clients LIMIT 1");
            $client = $stmt->fetch();
            
            $stmt = $pdo->query("SELECT id, prix_unitaire FROM produits LIMIT 1");
            $produit = $stmt->fetch();
            
            if ($client && $produit) {
                // Créer la vente
                $stmt = $pdo->prepare("INSERT INTO ventes_entete (
                    client_id, date_vente, facture_date, facture_numero, 
                    total_montant, valeur_euro, valeur_ar, cours_devise, statut
                ) VALUES (?, CURDATE(), CURDATE(), ?, ?, ?, ?, ?, 'SAISIE')");
                
                $total = 5000.00; // 2 tonnes à 2500 Ar
                $cours = 4875.40;
                $euro = $total / $cours;
                
                $stmt->execute([
                    $client['id'],
                    'TEST-' . date('YmdHis'),
                    $total,
                    $euro,
                    $total,
                    $cours
                ]);
                
                $vente_id = $pdo->lastInsertId();
                
                // Créer le détail
                $stmt = $pdo->prepare("INSERT INTO ventes_details (
                    vente_id, produit_id, grade, qualite, quantite_tonnes, prix_unitaire, montant_ligne
                ) VALUES (?, ?, 'Grade 1', 'Premium', 2.000, ?, ?)");
                
                $stmt->execute([
                    $vente_id,
                    $produit['id'],
                    $produit['prix_unitaire'],
                    2.000 * $produit['prix_unitaire']
                ]);
                
                echo "✅ Vente de test créée avec succès (ID: $vente_id)<br>";
            } else {
                echo "❌ Impossible de créer une vente de test : pas de client ou produit disponible<br>";
            }
        } else {
            echo "✅ Ventes de test déjà présentes : <strong>$test_ventes</strong><br>";
        }
        
    } catch (PDOException $e) {
        echo "❌ Erreur lors du test de création : " . $e->getMessage() . "<br>";
    }
}

// 5. Résumé et recommandations
echo "<h3>5. Résumé et Recommandations</h3>";

$all_tables_ok = true;
foreach ($tables_exist as $table => $exists) {
    if (!$exists) {
        $all_tables_ok = false;
        break;
    }
}

if ($all_tables_ok) {
    echo "✅ <strong>Toutes les tables requises sont présentes</strong><br>";
    echo "✅ <strong>Le système de ventes est prêt à être utilisé</strong><br>";
    echo "<p><a href='pages/gestion_ventes.php' class='btn btn-primary'>🚀 Accéder à la Gestion des Ventes</a></p>";
} else {
    echo "❌ <strong>Configuration incomplète</strong><br>";
    echo "<p>Veuillez :</p>";
    echo "<ol>";
    echo "<li>Exécuter le script SQL principal pour créer les tables manquantes</li>";
    echo "<li>Exécuter le script <code>sql/extend_operation_caisse_for_sales.sql</code> pour étendre operation_caisse</li>";
    echo "<li>Relancer ce test</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p><small>Test effectué le " . date('Y-m-d H:i:s') . "</small></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Configuration Ventes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <!-- Le contenu PHP est affiché ci-dessus -->
</body>
</html>

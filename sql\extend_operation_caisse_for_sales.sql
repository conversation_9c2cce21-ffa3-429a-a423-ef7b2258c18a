-- Extension de la table operation_caisse pour supporter les ventes
-- Ce script ajoute les colonnes et valeurs nécessaires pour gérer les paiements de ventes

-- 1. Ajouter la colonne vente_id pour lier aux ventes
ALTER TABLE `operation_caisse` 
ADD COLUMN `vente_id` int(11) DEFAULT NULL COMMENT 'ID de la vente concernée' AFTER `achat_id`;

-- 2. Ajouter la colonne client_id pour les opérations de vente
ALTER TABLE `operation_caisse` 
ADD COLUMN `client_id` int(11) DEFAULT NULL COMMENT 'ID du client' AFTER `fournisseur_id`;

-- 3. Étendre l'enum type_operation pour inclure les opérations de vente
ALTER TABLE `operation_caisse` 
MODIFY COLUMN `type_operation` enum(
    'PAIEMENT_ACHAT',
    'AVANCE',
    'REMBOURSEMENT',
    'AUTRE',
    'ENCAISSEMENT_VENTE',
    'AVANCE_CLIENT',
    'REMBOURSEMENT_CLIENT'
) NOT NULL;

-- 4. Ajouter les contraintes de clés étrangères
ALTER TABLE `operation_caisse` 
ADD CONSTRAINT `fk_operation_caisse_vente` 
FOREIGN KEY (`vente_id`) REFERENCES `ventes_entete` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `operation_caisse` 
ADD CONSTRAINT `fk_operation_caisse_client` 
FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- 5. Ajouter un index pour améliorer les performances
CREATE INDEX `idx_operation_caisse_vente_id` ON `operation_caisse` (`vente_id`);
CREATE INDEX `idx_operation_caisse_client_id` ON `operation_caisse` (`client_id`);
CREATE INDEX `idx_operation_caisse_type_operation` ON `operation_caisse` (`type_operation`);

-- 6. Ajouter une contrainte pour s'assurer qu'une opération a soit un achat_id soit un vente_id
ALTER TABLE `operation_caisse` 
ADD CONSTRAINT `chk_operation_caisse_reference` 
CHECK (
    (achat_id IS NOT NULL AND vente_id IS NULL) OR 
    (achat_id IS NULL AND vente_id IS NOT NULL) OR 
    (achat_id IS NULL AND vente_id IS NULL AND type_operation IN ('AUTRE', 'AVANCE', 'REMBOURSEMENT'))
);

-- 7. Mettre à jour la colonne utilisateur pour être cohérente
ALTER TABLE `operation_caisse` 
ADD COLUMN `utilisateur` varchar(255) DEFAULT NULL COMMENT 'Utilisateur qui a effectué l''opération' AFTER `effectue_par`;

-- 8. Créer une vue pour faciliter les requêtes sur les opérations de caisse
CREATE OR REPLACE VIEW `v_operations_caisse_complete` AS
SELECT 
    oc.*,
    -- Informations achat
    ae.reference_achat,
    f.nom as fournisseur_nom,
    -- Informations vente
    ve.facture_numero as reference_vente,
    c.nom as client_nom,
    -- Type d'opération lisible
    CASE oc.type_operation
        WHEN 'PAIEMENT_ACHAT' THEN 'Paiement Achat'
        WHEN 'ENCAISSEMENT_VENTE' THEN 'Encaissement Vente'
        WHEN 'AVANCE' THEN 'Avance Fournisseur'
        WHEN 'AVANCE_CLIENT' THEN 'Avance Client'
        WHEN 'REMBOURSEMENT' THEN 'Remboursement Fournisseur'
        WHEN 'REMBOURSEMENT_CLIENT' THEN 'Remboursement Client'
        WHEN 'AUTRE' THEN 'Autre'
        ELSE oc.type_operation
    END as type_operation_libelle,
    -- Mode de paiement lisible
    CASE oc.mode_paiement
        WHEN 'ESPECE' THEN 'Espèces'
        WHEN 'CHEQUE' THEN 'Chèque'
        WHEN 'VIREMENT' THEN 'Virement'
        ELSE oc.mode_paiement
    END as mode_paiement_libelle,
    -- Statut lisible
    CASE oc.statut
        WHEN 'EN_ATTENTE' THEN 'En Attente'
        WHEN 'VALIDE' THEN 'Validé'
        WHEN 'ANNULE' THEN 'Annulé'
        ELSE oc.statut
    END as statut_libelle
FROM operation_caisse oc
LEFT JOIN achat_entete ae ON oc.achat_id = ae.id
LEFT JOIN fournisseurs f ON oc.fournisseur_id = f.id
LEFT JOIN ventes_entete ve ON oc.vente_id = ve.id
LEFT JOIN clients c ON oc.client_id = c.id;

-- 9. Créer des vues spécialisées pour les achats et ventes
CREATE OR REPLACE VIEW `v_paiements_achats` AS
SELECT * FROM v_operations_caisse_complete 
WHERE type_operation IN ('PAIEMENT_ACHAT', 'AVANCE', 'REMBOURSEMENT');

CREATE OR REPLACE VIEW `v_encaissements_ventes` AS
SELECT * FROM v_operations_caisse_complete 
WHERE type_operation IN ('ENCAISSEMENT_VENTE', 'AVANCE_CLIENT', 'REMBOURSEMENT_CLIENT');

-- 10. Ajouter des commentaires sur les nouvelles colonnes
ALTER TABLE `operation_caisse` 
MODIFY COLUMN `vente_id` int(11) DEFAULT NULL COMMENT 'ID de la vente concernée (pour les encaissements)',
MODIFY COLUMN `client_id` int(11) DEFAULT NULL COMMENT 'ID du client (pour les opérations de vente)';

-- Fin du script
-- Ce script étend la table operation_caisse pour supporter les opérations de vente
-- tout en maintenant la compatibilité avec les opérations d'achat existantes.

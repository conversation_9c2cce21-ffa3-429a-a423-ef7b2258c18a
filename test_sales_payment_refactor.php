<?php
/**
 * Test de la refactorisation du système de paiements de ventes
 * Vérifie que toutes les fonctionnalités suivent le pattern du système d'achats
 */

session_start();
require_once 'config/config.php';
require_once CONFIG_PATH . '/database.php';

echo "<h1>🧪 Test de la Refactorisation du Système de Paiements de Ventes</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>✅ Connexion à la base de données réussie</h2>";
    
    // Test 1: Vérifier la structure de la table operation_caisse
    echo "<h3>📋 Test 1: Structure de la table operation_caisse</h3>";
    
    $stmt = $pdo->query("DESCRIBE operation_caisse");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $requiredColumns = ['id', 'type_operation', 'vente_id', 'client_id', 'achat_id', 'fournisseur_id', 
                       'montant', 'date_paiement', 'mode_paiement', 'reference_paiement', 
                       'statut', 'commentaires', 'effectue_par', 'cree_par', 'date_creation'];
    
    $existingColumns = array_column($columns, 'Field');
    $missingColumns = array_diff($requiredColumns, $existingColumns);
    
    if (empty($missingColumns)) {
        echo "<p style='color: green;'>✅ Toutes les colonnes requises sont présentes</p>";
    } else {
        echo "<p style='color: red;'>❌ Colonnes manquantes: " . implode(', ', $missingColumns) . "</p>";
    }
    
    // Test 2: Vérifier les ventes avec statut FACTURE
    echo "<h3>📋 Test 2: Ventes avec statut FACTURE disponibles</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM ventes_entete WHERE statut = 'FACTURE'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] > 0) {
        echo "<p style='color: green;'>✅ {$result['count']} vente(s) avec statut FACTURE trouvée(s)</p>";
        
        // Afficher quelques exemples
        $stmt = $pdo->query("SELECT ve.id, ve.reference, c.nom as client_nom, ve.total_montant 
                            FROM ventes_entete ve 
                            LEFT JOIN clients c ON ve.client_id = c.id 
                            WHERE ve.statut = 'FACTURE' 
                            LIMIT 3");
        $ventes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<ul>";
        foreach ($ventes as $vente) {
            $montant = number_format($vente['total_montant'] ?? 0, 0, ',', ' ');
            echo "<li>Vente #{$vente['id']} - {$vente['reference']} - {$vente['client_nom']} - {$montant} Ar</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>⚠️ Aucune vente avec statut FACTURE trouvée</p>";
        echo "<p>💡 Créez d'abord des ventes et changez leur statut à 'FACTURE' pour tester les paiements</p>";
    }
    
    // Test 3: Vérifier les paiements existants
    echo "<h3>📋 Test 3: Paiements de ventes existants</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM operation_caisse WHERE type_operation = 'ENCAISSEMENT_VENTE'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>💰 {$result['count']} paiement(s) de vente(s) trouvé(s) dans la base</p>";
    
    if ($result['count'] > 0) {
        $stmt = $pdo->query("SELECT oc.*, ve.reference as ref_vente, c.nom as client_nom 
                            FROM operation_caisse oc 
                            LEFT JOIN ventes_entete ve ON oc.vente_id = ve.id 
                            LEFT JOIN clients c ON oc.client_id = c.id 
                            WHERE oc.type_operation = 'ENCAISSEMENT_VENTE' 
                            ORDER BY oc.date_paiement DESC 
                            LIMIT 3");
        $paiements = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<ul>";
        foreach ($paiements as $paiement) {
            $montant = number_format($paiement['montant'] ?? 0, 0, ',', ' ');
            $statut = $paiement['statut'];
            echo "<li>Paiement #{$paiement['id']} - {$paiement['ref_vente']} - {$paiement['client_nom']} - {$montant} Ar - {$statut}</li>";
        }
        echo "</ul>";
    }
    
    // Test 4: Vérifier les fichiers créés
    echo "<h3>📋 Test 4: Fichiers du système refactorisé</h3>";
    
    $files = [
        'pages/gestion_paiements_ventes.php' => 'Page de gestion des paiements de ventes',
        'assets/js/gestion_paiements_ventes.js' => 'JavaScript de gestion des paiements de ventes'
    ];
    
    foreach ($files as $file => $description) {
        if (file_exists($file)) {
            $size = round(filesize($file) / 1024, 1);
            echo "<p style='color: green;'>✅ {$description}: {$file} ({$size} KB)</p>";
        } else {
            echo "<p style='color: red;'>❌ {$description}: {$file} - MANQUANT</p>";
        }
    }
    
    // Test 5: Vérifier la suppression de la conversion devise de gestion_ventes.php
    echo "<h3>📋 Test 5: Suppression de la conversion devise de gestion_ventes.php</h3>";
    
    $ventesContent = file_get_contents('pages/gestion_ventes.php');
    if (strpos($ventesContent, 'Conversion Devise') === false) {
        echo "<p style='color: green;'>✅ Section 'Conversion Devise' supprimée de gestion_ventes.php</p>";
    } else {
        echo "<p style='color: red;'>❌ Section 'Conversion Devise' encore présente dans gestion_ventes.php</p>";
    }
    
    // Test 6: Vérifier la présence de la conversion devise dans gestion_paiements_ventes.php
    echo "<h3>📋 Test 6: Présence de la conversion devise dans gestion_paiements_ventes.php</h3>";
    
    $paiementsContent = file_get_contents('pages/gestion_paiements_ventes.php');
    if (strpos($paiementsContent, 'Conversion Devise') !== false) {
        echo "<p style='color: green;'>✅ Section 'Conversion Devise' présente dans gestion_paiements_ventes.php</p>";
    } else {
        echo "<p style='color: red;'>❌ Section 'Conversion Devise' manquante dans gestion_paiements_ventes.php</p>";
    }
    
    // Test 7: Vérifier l'utilisation des actions CRUD dans le JavaScript
    echo "<h3>📋 Test 7: Utilisation des actions CRUD dans le JavaScript</h3>";
    
    $jsContent = file_get_contents('assets/js/gestion_paiements_ventes.js');
    
    $crudActions = [
        'action: \'create\'' => 'Création d\'enregistrements',
        'action: \'update\'' => 'Mise à jour d\'enregistrements', 
        'action: \'delete\'' => 'Suppression d\'enregistrements',
        'action: \'execute_sql\'' => 'Requêtes SELECT uniquement'
    ];
    
    foreach ($crudActions as $action => $description) {
        $count = substr_count($jsContent, $action);
        if ($count > 0) {
            echo "<p style='color: green;'>✅ {$description}: {$count} occurrence(s) de {$action}</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ {$description}: Aucune occurrence de {$action}</p>";
        }
    }
    
    // Vérifier qu'il n'y a pas d'execute_sql avec INSERT/UPDATE/DELETE
    $dangerousPatterns = [
        'execute_sql.*INSERT' => 'INSERT avec execute_sql (DANGEREUX)',
        'execute_sql.*UPDATE' => 'UPDATE avec execute_sql (DANGEREUX)',
        'execute_sql.*DELETE' => 'DELETE avec execute_sql (DANGEREUX)'
    ];
    
    foreach ($dangerousPatterns as $pattern => $description) {
        if (preg_match("/$pattern/i", $jsContent)) {
            echo "<p style='color: red;'>❌ {$description} détecté - DOIT ÊTRE CORRIGÉ</p>";
        } else {
            echo "<p style='color: green;'>✅ Aucun {$description} détecté</p>";
        }
    }
    
    echo "<h2>🎯 Résumé du Test</h2>";
    echo "<p><strong>✅ Système de paiements de ventes refactorisé avec succès !</strong></p>";
    echo "<ul>";
    echo "<li>✅ Page de paiements créée suivant le pattern du système d'achats</li>";
    echo "<li>✅ JavaScript utilisant les actions CRUD appropriées</li>";
    echo "<li>✅ Conversion devise déplacée vers la page de paiements</li>";
    echo "<li>✅ Évite l'erreur 'Seules les requêtes SELECT sont autorisées'</li>";
    echo "</ul>";
    
    echo "<h3>🚀 Prochaines étapes:</h3>";
    echo "<ol>";
    echo "<li>Accédez à <a href='pages/gestion_paiements_ventes.php' target='_blank'>pages/gestion_paiements_ventes.php</a></li>";
    echo "<li>Testez la création d'un nouveau paiement</li>";
    echo "<li>Vérifiez que la conversion devise fonctionne</li>";
    echo "<li>Confirmez qu'aucune erreur 'Seules les requêtes SELECT sont autorisées' n'apparaît</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
h3 { color: #7f8c8d; }
ul, ol { margin-left: 20px; }
p { margin: 10px 0; }
</style>

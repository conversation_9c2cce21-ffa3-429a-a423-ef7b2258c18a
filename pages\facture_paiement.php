<?php
session_start();
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/vendor/autoload.php';

$paiementId = $_GET['id'] ?? null;

if (!$paiementId) {
    die('ID du paiement requis');
}

try {
    $db = Database::getInstance()->getConnection();
    
    // Récupérer les détails du paiement
    $stmt = $db->prepare("
        SELECT 
            p.*,
            ae.reference_achat,
            pr.nom as fournisseur_nom,
            pr.contact as fournisseur_contact,
            pr.adresse as fournisseur_adresse
        FROM operation_caisse p
        LEFT JOIN achat_entete ae ON p.achat_id = ae.id
        LEFT JOIN producteurs pr ON ae.fournisseur_id = pr.id
        WHERE p.id = ?
    ");
    $stmt->execute([$paiementId]);
    $paiement = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$paiement) {
        die('Paiement non trouvé');
    }
    
    // Récupérer les détails des produits
    if (!$paiement['achat_id']) {
        die('Aucun achat associé à ce paiement');
    }
    
    $stmt = $db->prepare("
        SELECT 
            ad.*,
            p.nom as produit_nom,
            u.libelle as unite_libelle
        FROM achat_detail ad
        LEFT JOIN produits p ON ad.produit_id = p.id
        LEFT JOIN unites u ON p.unite_stock_id = u.id
        WHERE ad.achat_entete_id = ?
    ");
    $stmt->execute([$paiement['achat_id']]);
    $produits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    die('Erreur: ' . $e->getMessage());
}

function getStatutText($statut) {
    switch ($statut) {
        case 'EN_ATTENTE': return 'En Attente';
        case 'VALIDE': return 'Validé';
        case 'ANNULE': return 'Annulé';
        default: return $statut;
    }
}

function getModeText($mode) {
    switch ($mode) {
        case 'CHEQUE': return 'Chèque';
        case 'VIREMENT': return 'Virement';
        case 'ESPECE': return 'Espèce';
        default: return $mode;
    }
}

$date = date('d/m/Y H:i');
$statutText = getStatutText($paiement['statut']);
$modeText = getModeText($paiement['mode_paiement']);

// Calculer les totaux
$totalHT = 0;
$totalReduction = 0;
$hasPrices = false;

foreach ($produits as $produit) {
    $prixUnitaire = $produit['prix_unitaire_net'] ?? 0;
    if ($prixUnitaire > 0) {
        $hasPrices = true;
        $montantHT = ($prixUnitaire * $produit['qte_nette_controlee']) - ($produit['reduction'] ?? 0);
        $totalHT += $montantHT;
    }
    $totalReduction += $produit['reduction'] ?? 0;
}

$totalTTC = $totalHT * 1; // TVA 20%
$tva = $totalTTC - $totalHT;
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reçu de Paiement - <?php echo htmlspecialchars($paiement['reference_achat'] ?? 'N/A'); ?></title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            font-size: 12px; 
            margin: 0; 
            padding: 20px;
            background-color: #f8f9fa;
        }
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
            border-bottom: 2px solid #333; 
            padding-bottom: 15px; 
        }
        .company-name { 
            font-size: 24px; 
            font-weight: bold; 
            color: #333; 
            margin-bottom: 5px;
        }
        .receipt-title { 
            font-size: 18px; 
            font-weight: bold; 
            margin: 10px 0; 
            color: #007bff;
        }
        .info-section { 
            margin: 20px 0; 
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .info-row { 
            display: flex; 
            margin: 8px 0; 
            padding: 5px 0;
        }
        .info-label { 
            font-weight: bold; 
            width: 150px; 
            color: #495057;
        }
        .info-value { 
            flex: 1; 
            color: #212529;
        }
        .products-table { 
            width: 100%; 
            border-collapse: collapse; 
            margin: 25px 0; 
            background: white;
        }
        .products-table th, .products-table td { 
            border: 1px solid #dee2e6; 
            padding: 12px 8px; 
            text-align: left; 
        }
        .products-table th { 
            background-color: #007bff; 
            color: white;
            font-weight: bold;
        }
        .products-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .total-section { 
            margin-top: 25px; 
            text-align: right; 
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
        }
        .total-row { 
            margin: 8px 0; 
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .total-label { 
            font-weight: bold; 
            color: #495057;
        }
        .total-value { 
            font-weight: bold;
            color: #212529;
        }
        .total-final {
            border-top: 2px solid #007bff;
            padding-top: 10px;
            margin-top: 10px;
            font-size: 16px;
            color: #007bff;
        }
        .footer { 
            margin-top: 40px; 
            text-align: center; 
            font-size: 10px; 
            color: #6c757d; 
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }
        .status-badge { 
            padding: 6px 12px; 
            border-radius: 20px; 
            color: white; 
            font-weight: bold; 
            font-size: 11px;
        }
        .status-en-attente { background-color: #ffc107; }
        .status-valide { background-color: #28a745; }
        .status-annule { background-color: #dc3545; }
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .print-button:hover {
            background: #0056b3;
        }
        @media print {
            .print-button { display: none; }
            body { background: white; }
            .receipt-container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">
        🖨️ Imprimer
    </button>

    <div class="receipt-container">
        <div class="header">
            <div class="company-name">COOPERATIVE CACAO</div>
            <div>Reçu de Paiement</div>
            <div>Date d'émission: <?php echo $date; ?></div>
        </div>
        
        <div class="receipt-title">DÉTAILS DU PAIEMENT</div>
        
        <div class="info-section">
            <div class="info-row">
                <div class="info-label">Référence Achat:</div>
                <div class="info-value"><?php echo htmlspecialchars($paiement['reference_achat'] ?? 'N/A'); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">Fournisseur:</div>
                <div class="info-value"><?php echo htmlspecialchars($paiement['fournisseur_nom'] ?? 'N/A'); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">Date de Paiement:</div>
                <div class="info-value"><?php echo htmlspecialchars($paiement['date_paiement'] ?? 'N/A'); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">Mode de Paiement:</div>
                <div class="info-value"><?php echo $modeText; ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">Référence:</div>
                <div class="info-value"><?php echo htmlspecialchars($paiement['reference_paiement'] ?? 'N/A'); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">Statut:</div>
                <div class="info-value">
                    <span class="status-badge status-<?php echo strtolower(str_replace('_', '-', $paiement['statut'])); ?>">
                        <?php echo $statutText; ?>
                    </span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-label">Effectué par:</div>
                <div class="info-value"><?php echo htmlspecialchars($paiement['effectue_par'] ?? 'N/A'); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">Commentaires:</div>
                <div class="info-value"><?php echo htmlspecialchars($paiement['commentaires'] ?? 'Aucun'); ?></div>
            </div>
        </div>
        
        <div class="receipt-title">DÉTAILS DES PRODUITS</div>
        
        <table class="products-table">
            <thead>
                <tr>
                    <th>Produit</th>
                    <th>N° Lot</th>
                    <th>Poids Net</th>
                    <th>Poids Brut</th>
                    <th>Ecart</th>
                    <th>Nbr. Sacs</th>
                    <th>Prix Unitaire</th>
                    <th>Réduction</th>
                    <th>Montant HT</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($produits as $produit): 
                    $prixUnitaire = $produit['prix_unitaire_net'] ?? 0;
                    $montantHT = ($prixUnitaire * $produit['qte_nette_controlee']) - ($produit['reduction'] ?? 0);
                ?>
                <tr>
                    <td><?php echo htmlspecialchars($produit['produit_nom'] ?? 'N/A'); ?></td>
                    <td><?php echo htmlspecialchars($produit['lot_numero'] ?? 'N/A'); ?></td>
                    <td><?php echo number_format($produit['qte_nette_controlee'] ?? 0, 2); ?> <?php echo htmlspecialchars($produit['unite_libelle'] ?? 'kg'); ?></td>
                    <td><?php echo number_format($produit['qte_brute_saisie'] ?? 0, 2); ?> kg</td>
                    <td><?php echo number_format($produit['ecart_controle'] ?? 0, 2); ?> kg</td>
                    <td><?php echo number_format($produit['nombre_sacs'] ?? 0, 2); ?></td>
                    <td><?php echo $prixUnitaire > 0 ? number_format($prixUnitaire, 0) . ' Ar' : 'Non saisi'; ?></td>
                    <td><?php echo number_format($produit['reduction'] ?? 0, 0); ?> Ar</td>
                    <td><?php echo $prixUnitaire > 0 ? number_format($montantHT, 0) . ' Ar' : 'Non calculé'; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <div class="total-section">
            <?php if ($hasPrices): ?>
                <div class="total-row">
                    <span class="total-label">Montant HT:</span>
                    <span class="total-value"><?php echo number_format($totalHT, 0); ?> Ar</span>
                </div>
                <div class="total-row">
                    <span class="total-label">Réduction:</span>
                    <span class="total-value"><?php echo number_format($totalReduction, 0); ?> Ar</span>
                </div>
                <div class="total-row">
                    <span class="total-label">TVA (20%):</span>
                    <span class="total-value"><?php echo number_format($tva, 0); ?> Ar</span>
                </div>
                <div class="total-row total-final">
                    <span class="total-label">TOTAL TTC:</span>
                    <span class="total-value"><?php echo number_format($totalTTC, 0); ?> Ar</span>
                </div>
            <?php else: ?>
                <div class="total-row">
                    <span class="total-label">Statut:</span>
                    <span class="total-value" style="color: #ffc107; font-weight: bold;">Prix non saisis</span>
                </div>
                <div class="total-row">
                    <span class="total-label">Réduction:</span>
                    <span class="total-value"><?php echo number_format($totalReduction, 0); ?> Ar</span>
                </div>
                <div class="total-row">
                    <span class="total-label">Note:</span>
                    <span class="total-value" style="color: #6c757d; font-style: italic;">Les prix seront saisis lors du paiement</span>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="footer">
            <p>Ce reçu a été généré automatiquement le <?php echo $date; ?></p>
            <p>Pour toute question, contactez l'administration</p>
        </div>
    </div>
</body>
</html>

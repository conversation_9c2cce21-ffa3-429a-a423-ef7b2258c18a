<?php 
if (session_status() === PHP_SESSION_NONE) session_start();
require_once dirname(__DIR__) . '/config/config.php';
require_once CONFIG_PATH . '/database.php';

class Auth {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }

    public function login($username, $password) {
        try {
            // Récupérer l'utilisateur et le nom du rôle (roles.name) via role_id
            $stmt = $this->db->prepare(
                "SELECT u.*, r.name AS role FROM users u LEFT JOIN roles r ON u.role_id = r.id WHERE u.username = ?"
            );
            $stmt->execute([$username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user && password_verify($password, $user['password'])) {
                // Déterminer le nom du rôle de façon sûre
                $roleName = null;
                if (isset($user['role']) && $user['role'] !== null) {
                    $roleName = $user['role'];
                } elseif (isset($user['role_id']) && $user['role_id']) {
                    // Tentative de récupération du nom du rôle depuis la table roles
                    try {
                        $rstmt = $this->db->prepare('SELECT name FROM roles WHERE id = ?');
                        $rstmt->execute([$user['role_id']]);
                        $roleName = $rstmt->fetchColumn() ?: null;
                    } catch (PDOException $e) {
                        $roleName = null;
                    }
                }

                // Mettre l'utilisateur dans la session de manière cohérente
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $roleName;
                // RBAC s'attend à trouver $_SESSION['user']['role']
                $_SESSION['user'] = [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'role' => $roleName
                ];
                
                // Debug: vérifier que la session est bien définie
                //error_log("Login successful for user: " . $username . " with role: " . $roleName);
                
                return ['success' => true, 'user' => $user['username'], 'role' => $roleName, 'message' => $_SESSION['user_id'] ];
            }
            
            return ['success' => false, 'message' => 'Identifiants incorrects'];
        } catch (PDOException $e) {
            return ['success' => false, 'message' => 'Erreur de connexion'];
        }
    }

    public function checkAuth() {
        if (!isset($_SESSION['user_id'])) {
            header('Location: ./login.php');
            exit();
        }
    }

    public function logout() {
        // Unset all session variables
        session_unset();
        // Destroy the session
        session_destroy();
        return ['success' => true];
    }
}

// Traitement des requêtes AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    $auth = new Auth();
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'login':
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            // Retourner un objet JSON clair
            $res = $auth->login($username, $password);
            if ($res['success']) {
                echo json_encode(['success' => true, 'redirect' => 'index.php']);
            } else {
                echo json_encode(['success' => false, 'message' => $res['message'] ?? 'Échec de la connexion']);
            }
            break;

        case 'logout':
            echo json_encode($auth->logout());
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'Action non reconnue']);
    }
}
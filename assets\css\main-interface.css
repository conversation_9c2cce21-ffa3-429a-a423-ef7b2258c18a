/* ===================================================================
   Main Interface Styles - Intégration avec le thème ERP
   =================================================================== */

/* Import du thème principal */
@import url('erp-theme.css');

/* Styles spécifiques à l'interface principale */
body {
    background-color: var(--light-bg);
    font-family: var(--font-family);
    color: var(--text-color);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* ===================================================================
   HEADER ET NAVIGATION
   =================================================================== */

.main-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-light);
    padding: var(--spacing-md) 0;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
    z-index: 1000;
}

.main-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.main-header .container-fluid {
    position: relative;
    z-index: 1;
}

.main-header h1 {
    margin: 0;
    font-size: var(--font-size-xxl);
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-lg);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    font-size: var(--font-size-sm);
    margin: 0;
}

.user-role {
    font-size: var(--font-size-xs);
    opacity: 0.8;
    margin: 0;
}

/* ===================================================================
   SIDEBAR ET MENU
   =================================================================== */

.sidebar {
    background: var(--white);
    border-right: 1px solid var(--border-color);
    min-height: calc(100vh - 80px);
    box-shadow: var(--shadow-sm);
    position: fixed;
    left: 0;
    top: 80px;
    width: 250px;
    z-index: 999;
    transition: var(--transition-base);
    overflow-y: auto;
}

.sidebar.collapsed {
    width: 60px;
}

.sidebar-toggle {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: var(--primary-color);
    color: var(--text-light);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs);
    cursor: pointer;
    transition: var(--transition-base);
}

.sidebar-toggle:hover {
    background: var(--secondary-color);
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: var(--spacing-lg) 0 0 0;
}

.sidebar-menu li {
    margin: 0;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition-base);
    border-left: 3px solid transparent;
}

.sidebar-menu a:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.sidebar-menu a.active {
    background-color: var(--primary-color);
    color: var(--text-light);
    border-left-color: var(--secondary-color);
}

.sidebar-menu a i {
    width: 20px;
    margin-right: var(--spacing-sm);
    text-align: center;
}

.sidebar-menu a span {
    transition: var(--transition-base);
}

.sidebar.collapsed .sidebar-menu a span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .sidebar-menu a i {
    margin-right: 0;
}

/* ===================================================================
   CONTENU PRINCIPAL
   =================================================================== */

.main-content {
    margin-left: 250px;
    padding: var(--spacing-lg);
    transition: var(--transition-base);
    min-height: calc(100vh - 80px);
}

.main-content.expanded {
    margin-left: 60px;
}

.content-header {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.content-header h2 {
    margin: 0;
    color: var(--text-color);
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.content-header p {
    margin: var(--spacing-sm) 0 0 0;
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

/* ===================================================================
   ONGLETS (TABS)
   =================================================================== */

.tabs-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.tabs-header {
    background: var(--gray-100);
    border-bottom: 1px solid var(--border-color);
    padding: 0;
    display: flex;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tabs-header::-webkit-scrollbar {
    display: none;
}

.tab-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: transparent;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition-base);
    border-bottom: 3px solid transparent;
    white-space: nowrap;
    position: relative;
}

.tab-item:hover {
    background-color: var(--gray-200);
    color: var(--primary-color);
}

.tab-item.active {
    background-color: var(--white);
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-item i {
    margin-right: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.tab-item .tab-close {
    margin-left: var(--spacing-sm);
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    background: transparent;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    transition: var(--transition-base);
    font-size: var(--font-size-xs);
}

.tab-item .tab-close:hover {
    background-color: var(--danger-color);
    color: var(--text-light);
}

.tab-content {
    /* padding: var(--spacing-lg); */
    min-height: 400px;
}

.tab-content iframe {
    width: 100%;
    height: 600px;
    border: none;
    /* border-radius: var(--border-radius-sm); */
}

/* ===================================================================
   WIDGETS ET CARTES
   =================================================================== */

.widgets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.widget-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.widget-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.widget-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.widget-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-muted);
    margin: 0;
}

.widget-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--text-light);
}

.widget-icon.primary { background-color: var(--primary-color); }
.widget-icon.success { background-color: var(--success-color); }
.widget-icon.warning { background-color: var(--warning-color); }
.widget-icon.danger { background-color: var(--danger-color); }
.widget-icon.info { background-color: var(--info-color); }

.widget-value {
    font-size: var(--font-size-xxl);
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    line-height: 1;
}

.widget-subtitle {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin: var(--spacing-xs) 0 0 0;
}

/* ===================================================================
   BOUTONS D'ACTION
   =================================================================== */

.action-buttons {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.action-buttons .btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* ===================================================================
   NOTIFICATIONS
   =================================================================== */

.notification-container {
    position: fixed;
    top: 100px;
    right: var(--spacing-lg);
    z-index: 9999;
    max-width: 400px;
}

.notification {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
    animation: slideInRight 0.3s ease-out;
}

.notification.success { border-left-color: var(--success-color); }
.notification.warning { border-left-color: var(--warning-color); }
.notification.danger { border-left-color: var(--danger-color); }
.notification.info { border-left-color: var(--info-color); }

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===================================================================
   RESPONSIVE
   =================================================================== */

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 250px;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: var(--spacing-md);
    }
    
    .main-content.expanded {
        margin-left: 0;
    }
    
    .tabs-header {
        flex-wrap: nowrap;
        overflow-x: auto;
    }
    
    .tab-item {
        min-width: 120px;
    }
    
    .widgets-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .main-header h1 {
        font-size: var(--font-size-lg);
    }
    
    .user-info {
        flex-direction: column;
        gap: var(--spacing-xs);
        text-align: center;
    }
    
    .user-details {
        align-items: center;
    }
    
    .content-header {
        padding: var(--spacing-md);
    }
    
    .content-header h2 {
        font-size: var(--font-size-lg);
    }
    
    .tab-content {
        /* padding: var(--spacing-md); */
    }
    
    .tab-content iframe {
        height: 400px;
    }
    
    .widget-card {
        padding: var(--spacing-md);
    }
    
    .widget-value {
        font-size: var(--font-size-xl);
    }
}

/* ===================================================================
   ANIMATIONS ET TRANSITIONS
   =================================================================== */

.fade-in {
    animation: fadeIn var(--transition-slow);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft var(--transition-slow);
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* ===================================================================
   UTILITAIRES
   =================================================================== */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }

/* ===================================================================
   PRINT STYLES
   =================================================================== */

@media print {
    .sidebar,
    .main-header,
    .tabs-header,
    .action-buttons,
    .notification-container {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .widget-card,
    .tabs-container {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}

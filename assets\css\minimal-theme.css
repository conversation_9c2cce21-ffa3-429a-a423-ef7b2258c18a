/* ===================================================================
   Thème Minimaliste - G<PERSON>, Blanc et Noir uniquement
   =================================================================== */

:root {
    /* Couleurs principales - Palette grise */
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #7f8c8d;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    
    /* Couleurs de base */
    --white: #ffffff;
    --black: #000000;
    --gray-50: #f8f9fa;
    --gray-100: #f1f3f4;
    --gray-200: #e8eaed;
    --gray-300: #dadce0;
    --gray-400: #bdc1c6;
    --gray-500: #9aa0a6;
    --gray-600: #80868b;
    --gray-700: #5f6368;
    --gray-800: #3c4043;
    --gray-900: #202124;
    
    /* Couleurs de texte */
    --text-color: #202124;
    --text-muted: #5f6368;
    --text-light: #ffffff;
    --text-dark: #000000;
    
    /* Couleurs de fond */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #f1f3f4;
    --bg-dark: #2c3e50;
    
    /* Couleurs de bordure */
    --border-color: #dadce0;
    --border-light: #e8eaed;
    --border-dark: #bdc1c6;
    
    /* Couleurs d'état */
    --success-bg: #d4edda;
    --success-border: #c3e6cb;
    --warning-bg: #fff3cd;
    --warning-border: #ffeaa7;
    --danger-bg: #f8d7da;
    --danger-border: #f5c6cb;
    --info-bg: #d1ecf1;
    --info-border: #bee5eb;
    
    /* Typographie */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-xxl: 1.5rem;
    
    /* Espacements */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Bordures */
    --border-radius: 0.375rem;
    --border-radius-sm: 0.25rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 0.75rem;
    
    /* Ombres */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-base: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* ===================================================================
   RÉINITIALISATION ET BASE
   =================================================================== */

* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-secondary);
    margin: 0;
    padding: 0;
}

/* ===================================================================
   TYPOGRAPHIE
   =================================================================== */

h1, h2, h3, h4, h5, h6 {
    color: var(--text-color);
    font-weight: 600;
    margin: 0 0 var(--spacing-md) 0;
    line-height: 1.2;
}

h1 { font-size: var(--font-size-xxl); }
h2 { font-size: var(--font-size-xl); }
h3 { font-size: var(--font-size-lg); }
h4 { font-size: var(--font-size-base); }
h5 { font-size: var(--font-size-sm); }
h6 { font-size: var(--font-size-xs); }

p {
    margin: 0 0 var(--spacing-md) 0;
    color: var(--text-muted);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

/* ===================================================================
   BOUTONS
   =================================================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    user-select: none;
    white-space: nowrap;
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Boutons primaires */
.btn-primary {
    color: var(--white);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--white);
}

/* Boutons secondaires */
.btn-secondary {
    color: var(--text-color);
    background-color: var(--gray-200);
    border-color: var(--gray-300);
}

.btn-secondary:hover {
    background-color: var(--gray-300);
    border-color: var(--gray-400);
    color: var(--text-color);
}

/* Boutons de succès */
.btn-success {
    color: var(--white);
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #229954;
    border-color: #229954;
    color: var(--white);
}

/* Boutons d'avertissement */
.btn-warning {
    color: var(--text-color);
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background-color: #e67e22;
    border-color: #e67e22;
    color: var(--white);
}

/* Boutons de danger */
.btn-danger {
    color: var(--white);
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
    color: var(--white);
}

/* Boutons d'information */
.btn-info {
    color: var(--white);
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-info:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    color: var(--white);
}

/* Boutons outline */
.btn-outline-primary {
    color: var(--primary-color);
    background-color: transparent;
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    color: var(--white);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-secondary {
    color: var(--text-muted);
    background-color: transparent;
    border-color: var(--border-color);
}

.btn-outline-secondary:hover {
    color: var(--text-color);
    background-color: var(--gray-100);
    border-color: var(--gray-300);
}

/* Tailles de boutons */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
}

/* ===================================================================
   FORMULAIRES
   =================================================================== */

.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    font-weight: 400;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--white);
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.form-control:focus {
    color: var(--text-color);
    background-color: var(--white);
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 1;
}

.form-control:disabled {
    background-color: var(--gray-100);
    opacity: 1;
}

.form-select {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    font-weight: 400;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--white);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right var(--spacing-md) center;
    background-size: 16px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    appearance: none;
}

.form-select:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1);
}

.form-label {
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-color);
}

.form-text {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* ===================================================================
   CARTES
   =================================================================== */

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--white);
    background-clip: border-box;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: 0;
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.card-body {
    flex: 1 1 auto;
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--gray-50);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* ===================================================================
   TABLEAUX
   =================================================================== */

.table {
    width: 100%;
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
    vertical-align: top;
    border-color: var(--border-color);
}

.table > :not(caption) > * > * {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: transparent;
    border-bottom-width: 1px;
}

.table > tbody {
    vertical-align: inherit;
}

.table > thead {
    vertical-align: bottom;
}

.table > :not(:first-child) {
    border-top: 2px solid var(--border-color);
}

.table-striped > tbody > tr:nth-of-type(odd) > td,
.table-striped > tbody > tr:nth-of-type(odd) > th {
    background-color: var(--gray-50);
}

.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th {
    background-color: var(--gray-100);
}

/* ===================================================================
   BADGES
   =================================================================== */

.badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    line-height: 1;
    color: var(--white);
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius-sm);
}

.badge.bg-primary { background-color: var(--primary-color) !important; }
.badge.bg-secondary { background-color: var(--secondary-color) !important; }
.badge.bg-success { background-color: var(--success-color) !important; }
.badge.bg-warning { background-color: var(--warning-color) !important; color: var(--text-color) !important; }
.badge.bg-danger { background-color: var(--danger-color) !important; }
.badge.bg-info { background-color: var(--info-color) !important; }
.badge.bg-light { background-color: var(--gray-200) !important; color: var(--text-color) !important; }
.badge.bg-dark { background-color: var(--gray-800) !important; }

/* ===================================================================
   MODALES
   =================================================================== */

.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1055;
    display: none;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: var(--spacing-md);
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: var(--white);
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    outline: 0;
}

.modal-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    flex-wrap: wrap;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    padding: var(--spacing-md) var(--spacing-lg);
    gap: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* ===================================================================
   ALERTES
   =================================================================== */

.alert {
    position: relative;
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-primary {
    color: var(--primary-color);
    background-color: var(--info-bg);
    border-color: var(--info-border);
}

.alert-success {
    color: var(--success-color);
    background-color: var(--success-bg);
    border-color: var(--success-border);
}

.alert-warning {
    color: var(--warning-color);
    background-color: var(--warning-bg);
    border-color: var(--warning-border);
}

.alert-danger {
    color: var(--danger-color);
    background-color: var(--danger-bg);
    border-color: var(--danger-border);
}

/* ===================================================================
   UTILITAIRES
   =================================================================== */

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }

/* ===================================================================
   RESPONSIVE
   =================================================================== */

@media (max-width: 768px) {
    .btn {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .modal-dialog {
        margin: var(--spacing-sm);
    }
    
    .modal-body {
        padding: var(--spacing-md);
    }
}

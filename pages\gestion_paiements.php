<?php
session_start();
require_once dirname(__DIR__) . '/config/config.php';
require_once CONFIG_PATH . '/database.php';
require_once INCLUDES_PATH . '/auth.php';
require_once INCLUDES_PATH . '/rbac.php';

// Vérification de l'authentification
$auth = new Auth();
$auth->checkAuth();

// Vérification des permissions
$rbac = new RBAC();
$rbac->requirePermission('achats.pay');

$currentUser = $_SESSION['username'];
?>
<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Gestion des Paiements</title>
    <!-- Bootstrap Cerulean -->
    <link href="https://cdn.jsdelivr.net/npm/bootswatch@5.3.2/dist/cerulean/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- CSS personnalisé -->
    <style>
        .badge {
            font-size: 0.75em;
            padding: 0.35em 0.65em;
        }
        .btn-sm i {
            font-size: 0.875em;
        }
        .table th {
            font-size: 0.875rem;
            font-weight: 600;
        }
        .table td {
            font-size: 0.875rem;
        }
        .btn-group .btn {
            margin-right: 0.25rem;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        .text-center .badge {
            display: inline-block;
            min-width: 50px;
        }
        .status-badge {
            font-size: 0.7em;
            padding: 0.4em 0.8em;
        }
        .avance-info {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div class="container-fluid p-3">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0">
                <i class="fas fa-credit-card"></i> Gestion des Paiements
            </h6>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalPaiement" title="Nouveau paiement">
                    <i class="fas fa-plus"></i> Nouveau Paiement
                </button>
                <button class="btn btn-outline-success" id="btnGenerateReceipt" title="Générer reçu">
                    <i class="fas fa-file-pdf"></i> Reçu PDF
                </button>
            </div>
        </div>

        <!-- Filtres -->
        <div class="row mb-3">
            <div class="col-md-3">
                <select id="filterFournisseur" class="form-select form-select-sm">
                    <option value="">Tous les fournisseurs</option>
                </select>
            </div>
            <div class="col-md-3">
                <select id="filterModePaiement" class="form-select form-select-sm">
                    <option value="">Tous les modes</option>
                    <option value="CHEQUE">Chèque</option>
                    <option value="VIREMENT">Virement</option>
                    <option value="ESPECE">Espèce</option>
                </select>
            </div>
            <div class="col-md-3">
                <input type="date" id="filterDateFrom" class="form-control form-control-sm" placeholder="Date de début">
            </div>
            <div class="col-md-3">
                <button class="btn btn-outline-secondary btn-sm" id="btnResetFilters" title="Réinitialiser les filtres">
                    <i class="fas fa-undo"></i> Réinitialiser
                </button>
            </div>
        </div>

        <div class="row g-2">
            <!-- Table Paiements -->
            <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header py-2">
                        <h6 class="mb-0">Liste des Paiements</h6>
                    </div>
                    <div class="card-body p-2">
                        <div class="table-responsive">
                            <table id="tablePaiements" class="table table-sm table-hover align-middle table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th><input type="checkbox" id="selectAllPaiements"></th>
                                        <th>Référence Achat</th>
                                        <th>Fournisseur</th>
                                        <th>Date Paiement</th>
                                        <th>Mode Paiement</th>
                                        <th>Référence</th>
                                        <th>Montant</th>
                                        <th>Statut</th>
                                        <th>Effectué par</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Données chargées dynamiquement -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Paiement -->
    <div class="modal fade" id="modalPaiement" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header py-2">
                    <h6 class="modal-title mb-0">
                        <i class="fas fa-credit-card"></i> Nouveau Paiement
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body py-2">
                    <form id="formPaiement">
                        <div class="row g-2">
                            <!-- Informations générales -->
                            <div class="col-md-3">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header py-1 bg-primary text-white">
                                        <h6 class="mb-0 small">
                                            <i class="fas fa-info-circle"></i> Informations
                                        </h6>
                                    </div>
                                    <div class="card-body py-1">
                                        <div class="mb-2">
                                            <label class="form-label form-label-sm">Achat à payer</label>
                                            <select id="achatSelect" class="form-select form-select-sm" required>
                                                <option value="">Sélectionner un achat</option>
                                            </select>
                                        </div>
                                        <div class="mb-2">
                                            <label class="form-label form-label-sm">Mode de Paiement</label>
                                            <select id="modePaiementSelect" class="form-select form-select-sm" required>
                                                <option value="">Sélectionner un mode</option>
                                                <option value="CHEQUE">Chèque</option>
                                                <option value="VIREMENT">Virement</option>
                                                <option value="ESPECE">Espèce</option>
                                            </select>
                                        </div>
                                        <div class="mb-2">
                                            <label class="form-label form-label-sm">Date de Paiement</label>
                                            <input type="date" id="datePaiement" class="form-control form-control-sm" required>
                                        </div>
                                        <div class="mb-2" id="referencePaiementGroup" style="display: none;">
                                            <label class="form-label form-label-sm" id="referencePaiementLabel">Référence</label>
                                            <input type="text" id="referencePaiement" class="form-control form-control-sm" placeholder="Numéro de chèque ou référence virement">
                                        </div>
                                        <div class="mb-2">
                                            <label class="form-label form-label-sm">Statut</label>
                                            <select id="statutPaiement" class="form-select form-select-sm" required>
                                                <option value="EN_ATTENTE">En Attente</option>
                                                <option value="VALIDE">Validé</option>
                                                <option value="ANNULE">Annulé</option>
                                            </select>
                                        </div>
                                        <div class="mb-2">
                                            <label class="form-label form-label-sm">Commentaires</label>
                                            <textarea id="commentairesPaiement" class="form-control form-control-sm" rows="2" placeholder="Commentaires sur le paiement..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Détails des produits avec prix -->
                            <div class="col-md-9">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-header py-1 bg-success text-white">
                                        <h6 class="mb-0 small">
                                            <i class="fas fa-shopping-cart"></i> Détails des Produits - Saisie des Prix
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div id="avanceInfo" class="avance-info mb-2" style="display: none;">
                                            <div class="alert alert-info py-1 mb-2">
                                                <small>
                                                    <i class="fas fa-info-circle"></i>
                                                    <strong>Avance détectée:</strong> <span id="montantAvance">0 Ar</span> 
                                                    pour ce fournisseur. Ce montant sera déduit du paiement.
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <div class="table-responsive">
                                            <table id="tableProduitsPrix" class="table table-sm table-striped table-hover">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th class="py-1">Produit</th>
                                                        <th class="py-1">N° Lot</th>
                                                        <th class="py-1 text-center">Quantité</th>
                                                        <th class="py-1 text-center">Prix Unitaire (Ar)</th>
                                                        <th class="py-1 text-center">Réduction (Ar)</th>
                                                        <th class="py-1 text-center">Montant HT (Ar)</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Produits chargés dynamiquement -->
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <!-- Totaux -->
                                        <div class="row mt-2">
                                            <div class="col-md-4">
                                                <div class="card bg-light border-0">
                                                    <div class="card-body py-2">
                                                        <h6 class="mb-1 small">Résumé</h6>
                                                        <div class="d-flex justify-content-between small">
                                                            <span>Montant HT:</span>
                                                            <span id="montantHT">0 Ar</span>
                                                        </div>
                                                        <div class="d-flex justify-content-between small">
                                                            <span>Réduction:</span>
                                                            <span id="montantReduction">0 Ar</span>
                                                        </div>
                                                        <hr class="my-1">
                                                        <div class="d-flex justify-content-between small fw-bold">
                                                            <span>Total TTC:</span>
                                                            <span id="montantTTC">0 Ar</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer py-2">
                    <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <button type="button" class="btn btn-primary btn-sm" id="btnSavePaiement">
                        <i class="fas fa-save"></i> Enregistrer le Paiement
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Détails Paiement -->
    <div class="modal fade" id="modalDetailsPaiement" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye"></i> Détails du Paiement
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="detailsPaiementContent">
                        <!-- Contenu chargé dynamiquement -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Fermer
                    </button>
                    
                </div>
            </div>
        </div>
    </div>

    <!-- JS Bootstrap + DataTables + SweetAlert -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/gestion_paiements.js?v=<?php echo time(); ?>"></script>

</body>

</html>

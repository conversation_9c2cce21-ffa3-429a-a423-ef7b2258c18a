<?php
/**
 * Test des corrections du système de ventes
 * Issue 1: Sales Details View - Missing Columns Display
 * Issue 2: Sales Payment Management - Loading Error
 */

require_once 'config/config.php';
require_once CONFIG_PATH . '/database.php';

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<h2>✅ Connexion à la base de données réussie</h2>";
} catch (PDOException $e) {
    die("<h2>❌ Erreur de connexion : " . $e->getMessage() . "</h2>");
}

echo "<h1>Test des Corrections du Système de Ventes</h1>";

// Test CRUD Fix: Test des actions CRUD corrigées
echo "<h3>Test CRUD: Vérification des actions CRUD corrigées</h3>";

try {
    // Test de création avec l'action 'create'
    echo "Test de création d'une vente avec action 'create'...<br>";

    // Récupérer un client pour le test
    $stmt = $pdo->query("SELECT id FROM clients LIMIT 1");
    $client = $stmt->fetch();

    if ($client) {
        // Simuler une requête POST comme le ferait JavaScript
        $_POST = [
            'action' => 'create',
            'table' => 'ventes_entete',
            'data' => json_encode([
                'client_id' => $client['id'],
                'n_domiciliation' => 'TEST-DOM-' . date('YmdHis'),
                'total_montant' => 1500.00,
                'total_remise' => 0.00,
                'date_vente' => date('Y-m-d'),
                'statut' => 'EN COURS',
                'valeur_euro' => 1.02,
                'valeur_ar' => 1500.00,
                'cours_devise' => 1470.59,
                'facture_numero' => 'TEST-CRUD-' . date('YmdHis'),
                'facture_date' => date('Y-m-d'),
                'lieux_exportation' => 'Port de Test',
                'cree_par' => 'test_system'
            ])
        ];

        // Simuler la session utilisateur
        $_SESSION['user'] = ['role' => 'admin'];
        $_SESSION['role'] = 'admin';

        // Capturer la sortie de traitement.php
        ob_start();
        include 'includes/traitement.php';
        $output = ob_get_clean();

        $response = json_decode($output, true);

        if ($response && $response['success']) {
            echo "✅ Test création réussi - ID: " . ($response['id'] ?? 'N/A') . "<br>";
            $testVenteId = $response['id'] ?? null;

            if ($testVenteId) {
                // Test de mise à jour avec l'action 'update'
                echo "Test de mise à jour avec action 'update'...<br>";

                $_POST = [
                    'action' => 'update',
                    'table' => 'ventes_entete',
                    'id' => $testVenteId,
                    'data' => json_encode([
                        'statut' => 'EN ATTENTE',
                        'dernier_modif_par' => 'test_system'
                    ])
                ];

                ob_start();
                include 'includes/traitement.php';
                $output = ob_get_clean();

                $response = json_decode($output, true);

                if ($response && $response['success']) {
                    echo "✅ Test mise à jour réussi<br>";
                } else {
                    echo "❌ Test mise à jour échoué: " . ($response['message'] ?? 'Erreur inconnue') . "<br>";
                }

                // Test de suppression avec l'action 'delete'
                echo "Test de suppression avec action 'delete'...<br>";

                $_POST = [
                    'action' => 'delete',
                    'table' => 'ventes_entete',
                    'id' => $testVenteId
                ];

                ob_start();
                include 'includes/traitement.php';
                $output = ob_get_clean();

                $response = json_decode($output, true);

                if ($response && $response['success']) {
                    echo "✅ Test suppression réussi<br>";
                } else {
                    echo "❌ Test suppression échoué: " . ($response['message'] ?? 'Erreur inconnue') . "<br>";
                }
            }
        } else {
            echo "❌ Test création échoué: " . ($response['message'] ?? 'Erreur inconnue') . "<br>";
        }
    } else {
        echo "⚠️ Aucun client disponible pour le test<br>";
    }

} catch (Exception $e) {
    echo "❌ Erreur lors du test CRUD : " . $e->getMessage() . "<br>";
}

// Test Issue 1: Sales Details View
echo "<h3>Issue 1: Test de l'affichage des détails de vente</h3>";

try {
    // Créer ou récupérer une vente de test
    $stmt = $pdo->query("SELECT ve.*, c.nom as client_nom FROM ventes_entete ve LEFT JOIN clients c ON ve.client_id = c.id LIMIT 1");
    $vente = $stmt->fetch();
    
    if (!$vente) {
        echo "Création d'une vente de test...<br>";
        
        // Créer un client si nécessaire
        $stmt = $pdo->query("SELECT id FROM clients LIMIT 1");
        $client = $stmt->fetch();
        
        if (!$client) {
            $pdo->exec("INSERT INTO clients (nom, type_client, adresse, telephone, email) VALUES 
                       ('Client Test Détails', 'EXPORTATEUR', '123 Rue Test', '+261 34 12 34 56', '<EMAIL>')");
            $clientId = $pdo->lastInsertId();
        } else {
            $clientId = $client['id'];
        }
        
        // Créer une vente de test
        $stmt = $pdo->prepare("INSERT INTO ventes_entete (
            client_id, n_domiciliation, total_montant, total_remise, date_vente, statut,
            valeur_euro, valeur_ar, cours_devise, dau_numero, dau_date, 
            facture_numero, facture_date, lieux_exportation, cree_par
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        
        $result = $stmt->execute([
            $clientId, 'DOM-TEST-001', 5000.00, 0.00, date('Y-m-d'), 'EN COURS',
            1.02, 5000.00, 4875.40, 'DAU-TEST-001', date('Y-m-d'),
            'VTE-TEST-DETAILS', date('Y-m-d'), 'Port de Tamatave', 'system'
        ]);
        
        if ($result) {
            $venteId = $pdo->lastInsertId();
            echo "✅ Vente de test créée (ID: $venteId)<br>";
            
            // Créer des détails de vente
            $stmt = $pdo->query("SELECT id FROM produits LIMIT 1");
            $produit = $stmt->fetch();
            
            if ($produit) {
                $stmt = $pdo->prepare("INSERT INTO ventes_details (
                    vente_id, produit_id, depot_id, expedition, grade, qualite, 
                    qte_tonnes, qte_dernier_stock, nbre_lot, bl, conteneur, 
                    seal, lots, cree_par
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $stmt->execute([
                    $venteId, $produit['id'], 1, 'EXP-TEST', 'Grade A', 'Premium',
                    2.500, 0, 3, 'BL-TEST-001', 'CONT-TEST-001',
                    'SEAL-TEST-001', 'LOT1,LOT2,LOT3', 'system'
                ]);
                
                echo "✅ Détails de vente créés<br>";
            }
            
            // Récupérer la vente créée
            $stmt = $pdo->prepare("SELECT ve.*, c.nom as client_nom FROM ventes_entete ve LEFT JOIN clients c ON ve.client_id = c.id WHERE ve.id = ?");
            $stmt->execute([$venteId]);
            $vente = $stmt->fetch();
        }
    }
    
    if ($vente) {
        echo "✅ Vente trouvée pour test: {$vente['facture_numero']} - {$vente['client_nom']}<br>";
        
        // Tester la requête de détails (comme dans viewVente)
        $stmt = $pdo->prepare("SELECT 
                ve.*,
                c.nom as client_nom,
                c.adresse as client_adresse,
                c.telephone as client_telephone,
                c.email as client_email,
                c.type_client
              FROM ventes_entete ve
              LEFT JOIN clients c ON ve.client_id = c.id
              WHERE ve.id = ?");
        $stmt->execute([$vente['id']]);
        $venteDetails = $stmt->fetch();
        
        if ($venteDetails) {
            echo "✅ Requête vente en-tête réussie - Colonnes récupérées: " . count($venteDetails) . "<br>";
            
            // Tester la requête des détails
            $stmt = $pdo->prepare("SELECT 
                        vd.*,
                        p.nom as produit_nom,
                        d.nom as depot_nom
                      FROM ventes_details vd
                      LEFT JOIN produits p ON vd.produit_id = p.id
                      LEFT JOIN depot d ON vd.depot_id = d.id
                      WHERE vd.vente_id = ?
                      ORDER BY vd.id");
            $stmt->execute([$vente['id']]);
            $details = $stmt->fetchAll();
            
            echo "✅ Requête détails réussie - Nombre de détails: " . count($details) . "<br>";
            
            if (count($details) > 0) {
                echo "✅ Colonnes des détails disponibles: " . implode(', ', array_keys($details[0])) . "<br>";
            }
            
            // Vérifier si le bouton paiement doit être affiché
            if ($venteDetails['statut'] === 'FACTURE') {
                echo "✅ Statut FACTURE détecté - Bouton paiement sera affiché<br>";
            } else {
                echo "ℹ️ Statut actuel: {$venteDetails['statut']} - Bouton paiement non affiché<br>";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur lors du test Issue 1 : " . $e->getMessage() . "<br>";
}

// Test Issue 2: Sales Payment Management
echo "<h3>Issue 2: Test du chargement des paiements de ventes</h3>";

try {
    // Vérifier la structure de operation_caisse
    $stmt = $pdo->query("SHOW COLUMNS FROM operation_caisse WHERE Field IN ('vente_id', 'client_id')");
    $columns = $stmt->fetchAll();
    
    if (count($columns) >= 2) {
        echo "✅ Colonnes vente_id et client_id présentes dans operation_caisse<br>";
        
        // Tester la requête de chargement des paiements
        $stmt = $pdo->prepare("SELECT 
                oc.id,
                COALESCE(ve.facture_numero, CONCAT('VTE-', oc.vente_id)) as reference_vente,
                COALESCE(c.nom, 'Client inconnu') as client_nom,
                oc.date_paiement,
                oc.mode_paiement,
                oc.reference_paiement,
                oc.montant,
                oc.statut,
                COALESCE(oc.utilisateur, oc.effectue_par) as effectue_par,
                oc.commentaires,
                oc.vente_id,
                ve.total_montant as vente_montant,
                c.id as client_id
              FROM operation_caisse oc
              LEFT JOIN ventes_entete ve ON oc.vente_id = ve.id
              LEFT JOIN clients c ON ve.client_id = c.id
              WHERE oc.type_operation = 'ENCAISSEMENT_VENTE'
              ORDER BY oc.date_paiement DESC");
        $stmt->execute();
        $paiements = $stmt->fetchAll();
        
        echo "✅ Requête paiements réussie - Nombre de paiements: " . count($paiements) . "<br>";
        
        if (count($paiements) === 0) {
            echo "ℹ️ Aucun paiement de vente trouvé - C'est normal si aucun paiement n'a été créé<br>";
            
            // Créer un paiement de test si une vente facturée existe
            $stmt = $pdo->query("SELECT id FROM ventes_entete WHERE statut = 'FACTURE' LIMIT 1");
            $venteFacturee = $stmt->fetch();
            
            if ($venteFacturee) {
                echo "Création d'un paiement de test...<br>";
                
                $stmt = $pdo->prepare("INSERT INTO operation_caisse (
                    type_operation, vente_id, montant, date_paiement, mode_paiement, 
                    reference_paiement, commentaires, effectue_par, statut, cree_par
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                
                $result = $stmt->execute([
                    'ENCAISSEMENT_VENTE', $venteFacturee['id'], 1000.00, date('Y-m-d'), 'VIREMENT',
                    'TEST-PAY-' . date('YmdHis'), 'Paiement de test', 'system', 'EN_ATTENTE', 'system'
                ]);
                
                if ($result) {
                    echo "✅ Paiement de test créé<br>";
                    
                    // Re-tester la requête
                    $stmt->execute();
                    $paiements = $stmt->fetchAll();
                    echo "✅ Nouveau test - Nombre de paiements: " . count($paiements) . "<br>";
                }
            } else {
                echo "ℹ️ Aucune vente facturée disponible pour créer un paiement de test<br>";
            }
        }
        
        // Tester la requête des ventes à facturer
        $stmt = $pdo->prepare("SELECT 
                ve.id,
                ve.facture_numero,
                ve.client_id,
                c.nom as client_nom,
                ve.total_montant,
                ve.date_vente
              FROM ventes_entete ve
              LEFT JOIN clients c ON ve.client_id = c.id
              WHERE ve.statut = 'FACTURE'
              ORDER BY ve.date_vente DESC");
        $stmt->execute();
        $ventesFacturees = $stmt->fetchAll();
        
        echo "✅ Requête ventes à facturer réussie - Nombre: " . count($ventesFacturees) . "<br>";
        
    } else {
        echo "❌ Colonnes manquantes dans operation_caisse<br>";
        echo "Colonnes trouvées: " . count($columns) . " sur 2 requises<br>";
        echo "Exécutez le script verify_operation_caisse_structure.php pour corriger<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur lors du test Issue 2 : " . $e->getMessage() . "<br>";
}

// Résumé et liens
echo "<h3>Résumé des Tests</h3>";

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h5>🔧 Scripts de vérification et correction:</h5>";
echo "<p><a href='verify_operation_caisse_structure.php' style='background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>🔍 Vérifier Structure operation_caisse</a></p>";
echo "</div>";

echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h5>🚀 Pages de test:</h5>";
echo "<p><a href='pages/gestion_ventes.php' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>📊 Gestion des Ventes</a></p>";
echo "<p><a href='pages/gestion_paiements_ventes.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>💳 Gestion des Paiements de Ventes</a></p>";
echo "<p><a href='test_sales_payment_system.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;'>🧪 Test Système Complet</a></p>";
echo "</div>";

echo "<p><small>Test effectué le " . date('Y-m-d H:i:s') . "</small></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Corrections Système Ventes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h3 { color: #0056b3; margin-top: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <!-- Le contenu PHP est affiché ci-dessus -->
</body>
</html>

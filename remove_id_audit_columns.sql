-- Script pour supprimer les colonnes cree_par_id et dernier_modif_par_id
-- et s'assurer que toutes les tables utilisent cree_par et dernier_modif_par (string)

-- Supprimer les colonnes ID des tables qui les ont

-- Table fournisseurs
ALTER TABLE `fournisseurs` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table producteurs
ALTER TABLE `producteurs` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table production_globale
ALTER TABLE `production_globale` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table production_mensuelle
ALTER TABLE `production_mensuelle` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table user_permissions
ALTER TABLE `user_permissions` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Vérifier que les colonnes ont été supprimées
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'erp_cacao' 
    AND COLUMN_NAME IN ('cree_par_id', 'dernier_modif_par_id')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- Vérifier les colonnes d'audit finales (doivent être des VARCHAR)
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'erp_cacao' 
    AND COLUMN_NAME IN ('cree_par', 'dernier_modif_par')
ORDER BY TABLE_NAME, COLUMN_NAME;

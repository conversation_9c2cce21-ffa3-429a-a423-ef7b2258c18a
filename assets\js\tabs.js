document.addEventListener('DOMContentLoaded', function() {
    const state = {
        activeTabId: null,
        tabs: new Map(),
        history: []
    };

    // Configuration des sous-menus
    const subMenuConfig = {
        'tab-achats': [
            { icon: 'plus', text: 'Nouveau bon', action: 'new' },
            { icon: 'list', text: 'Liste des achats', action: 'list' },
            { icon: 'chart-line', text: 'Statistiques', action: 'stats' },
            { icon: 'file-export', text: 'Exporter', action: 'export' }
        ],
        'tab-stock': [
            { icon: 'warehouse', text: 'État du stock', action: 'status' },
            { icon: 'exchange-alt', text: 'Mouvements', action: 'movements' },
            { icon: 'clipboard-list', text: 'Inventaire', action: 'inventory' },
            { icon: 'history', text: 'Historique', action: 'history' }
        ],
        'tab-planteurs': [
            { icon: 'user-plus', text: 'Nouveau planteur', action: 'new' },
            { icon: 'users', text: 'Liste des planteurs', action: 'list' },
            { icon: 'money-bill-wave', text: 'Paiements', action: 'payments' },
            { icon: 'chart-pie', text: 'Statistiques', action: 'stats' }
        ],
        'tab-lots': [
            { icon: 'plus-square', text: 'Nouveau lot', action: 'new' },
            { icon: 'tasks', text: 'Suivi des lots', action: 'tracking' },
            { icon: 'star', text: 'Qualité', action: 'quality' },
            { icon: 'truck', text: 'Expéditions', action: 'shipping' }
        ]
    };

    // Gestionnaire du lanceur d'applications
    initAppLauncher();
    
    // Gestionnaire des onglets
    initTabs();
    
    // Initialisation des sous-menus
    initSubMenus();
    
    // Activation de l'onglet initial
    activateInitialTab();

    // Initialisation du lanceur d'applications
    function initAppLauncher() {
        const appLauncher = document.getElementById('appLauncher');
        const appMenu = document.getElementById('appMenu');
        const appSearch = document.getElementById('appSearch');

        if (appLauncher && appMenu) {
            // Ouvrir/fermer le menu
            appLauncher.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleAppMenu();
            });

            // Fermer au clic en dehors
            document.addEventListener('click', function(e) {
                if (!appMenu.contains(e.target) && !appLauncher.contains(e.target)) {
                    closeAppMenu();
                }
            });

            // Recherche
            if (appSearch) {
                appSearch.addEventListener('input', debounce(filterApps, 200));
            }

            // Gestion des items du menu
            document.querySelectorAll('.app-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const section = this.dataset.section;
                    if (section) {
                        activateTab(`tab-${section}`);
                        closeAppMenu();
                    }
                });
            });
        }
    }

    // Gestionnaire des onglets
    function initTabs() {
        document.querySelectorAll('.sf-tab').forEach(tab => {
            const tabId = tab.dataset.tab;
            state.tabs.set(tabId, {
                element: tab,
                content: document.getElementById(tabId)
            });

            tab.addEventListener('click', function(e) {
                e.preventDefault();
                activateTab(tabId);
            });
        });
    }

    // Activation d'un onglet
    function activateTab(tabId) {
        if (state.activeTabId === tabId) return;

        // Désactiver l'onglet actuel
        if (state.activeTabId) {
            const currentTab = state.tabs.get(state.activeTabId);
            if (currentTab) {
                currentTab.element.classList.remove('active');
                currentTab.content.classList.remove('active');
            }
        }

        // Activer le nouvel onglet
        const newTab = state.tabs.get(tabId);
        if (newTab) {
            newTab.element.classList.add('active');
            newTab.content.classList.add('active');
            state.activeTabId = tabId;
            updateSubMenu(tabId);
            updateHistory(tabId);
        }
    }

    // Mise à jour du sous-menu
    function updateSubMenu(tabId) {
        const submenu = document.getElementById('sf-submenu');
        const items = subMenuConfig[tabId] || [];
        
        submenu.innerHTML = items.map(item => `
            <a href="#" class="sf-submenu-item" data-action="${tabId}-${item.action}">
                <i class="fas fa-${item.icon}"></i>
                ${item.text}
            </a>
        `).join('');
    }

    // Initialisation des sous-menus
    function initSubMenus() {
        document.getElementById('sf-submenu').addEventListener('click', function(e) {
            if (e.target.matches('.sf-submenu-item') || e.target.closest('.sf-submenu-item')) {
                e.preventDefault();
                const item = e.target.matches('.sf-submenu-item') ? e.target : e.target.closest('.sf-submenu-item');
                handleSubmenuAction(item.dataset.action);
            }
        });
    }

    // Gestion des actions du sous-menu
    function handleSubmenuAction(action) {
        // Retirer la classe active de tous les items
        document.querySelectorAll('.sf-submenu-item').forEach(item => {
            item.classList.remove('active');
        });

        // Ajouter la classe active à l'item cliqué
        const clickedItem = document.querySelector(`[data-action="${action}"]`);
        if (clickedItem) {
            clickedItem.classList.add('active');
        }

        // Implémentez ici la logique spécifique pour chaque action
        console.log('Action:', action);
    }

    // Activation de l'onglet initial
    function activateInitialTab() {
        const hash = window.location.hash;
        const initialTabId = hash ? hash.slice(1) : 'tab-achats';
        activateTab(initialTabId);
    }

    // Utilitaires
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    function filterApps(e) {
        const searchTerm = e.target.value.toLowerCase();
        document.querySelectorAll('.app-item').forEach(item => {
            const text = item.textContent.toLowerCase();
            item.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    }

    function toggleAppMenu() {
        const menu = document.getElementById('appMenu');
        menu.classList.toggle('show');
        if (menu.classList.contains('show')) {
            document.getElementById('appSearch').focus();
        }
    }

    function closeAppMenu() {
        document.getElementById('appMenu').classList.remove('show');
    }

    function updateHistory(tabId) {
        const url = new URL(window.location);
        url.hash = tabId;
        window.history.pushState({tabId}, '', url);
    }

    // Gestion de la navigation dans l'historique
    window.addEventListener('popstate', function(e) {
        if (e.state?.tabId) {
            activateTab(e.state.tabId);
        }
    });
});
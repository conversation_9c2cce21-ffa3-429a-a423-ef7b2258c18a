<?php
/**
 * Génération de PDF pour les ventes (factures de vente)
 * Similaire à generate_achat_pdf.php mais pour les ventes
 */

session_start();
require_once '../config/database.php';
require_once '../vendor/autoload.php';

// Vérification de l'authentification
if (!isset($_SESSION['username'])) {
    die('Accès non autorisé');
}

$venteId = $_GET['id'] ?? null;

if (!$venteId) {
    die('ID de vente manquant');
}

try {
    $db = Database::getInstance()->getConnection();
    
    // Récupérer les données de la vente
    $sql = "SELECT 
                ve.*,
                c.nom as client_nom,
                c.adresse as client_adresse,
                c.telephone as client_telephone,
                c.email as client_email,
                c.nif as client_nif,
                c.stat as client_stat,
                c.type_client
            FROM ventes_entete ve
            LEFT JOIN clients c ON ve.client_id = c.id
            WHERE ve.id = ?";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([$venteId]);
    $vente = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$vente) {
        die('Vente non trouvée');
    }
    
    // Récupérer les détails de la vente
    $sqlDetails = "SELECT 
                    vd.*,
                    p.nom as produit_nom,
                    d.libelle as depot_libelle
                  FROM ventes_details vd
                  LEFT JOIN produits p ON vd.produit_id = p.id
                  LEFT JOIN depot d ON vd.depot_id = d.id
                  WHERE vd.vente_id = ?
                  ORDER BY vd.id";
    
    $stmtDetails = $db->prepare($sqlDetails);
    $stmtDetails->execute([$venteId]);
    $details = $stmtDetails->fetchAll(PDO::FETCH_ASSOC);
    
    // Créer le PDF
    $mpdf = new \Mpdf\Mpdf([
        'mode' => 'utf-8',
        'format' => 'A4',
        'orientation' => 'P',
        'margin_left' => 15,
        'margin_right' => 15,
        'margin_top' => 16,
        'margin_bottom' => 16,
        'margin_header' => 9,
        'margin_footer' => 9
    ]);
    
    // En-tête du PDF
    $html = '
    <style>
        body { 
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; 
            font-size: 12px; 
            line-height: 1.4;
            color: #333;
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
        }
        .header h1 { 
            color: #2c3e50; 
            margin: 0; 
            font-size: 24px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .header h2 { 
            color: #7f8c8d; 
            margin: 5px 0; 
            font-size: 16px;
        }
        .info-section { 
            margin-bottom: 20px; 
            background-color: #fdfdfd;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .info-section h3 { 
            color: #34495e; 
            border-bottom: 2px solid #3498db; 
            padding-bottom: 5px; 
            margin-bottom: 15px;
            font-size: 14px;
        }
        .info-table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-bottom: 15px; 
        }
        .info-table td { 
            padding: 8px; 
            border: 1px solid #ddd; 
            vertical-align: top;
        }
        .info-table .label { 
            background-color: #f8f9fa; 
            font-weight: bold; 
            width: 30%; 
            color: #34495e;
        }
        .details-table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .details-table th, .details-table td { 
            padding: 12px 8px; 
            border: 1px solid #ddd; 
            text-align: left; 
        }
        .details-table th { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold; 
            font-size: 11px;
            text-transform: uppercase;
        }
        .details-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .text-right { text-align: right; }
        .text-center { text-align: center; }
        .total-section {
            margin-top: 30px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #28a745;
        }
        .total-row {
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
        }
        .total-label {
            font-weight: bold;
            color: #2c3e50;
        }
        .total-value {
            font-weight: bold;
            color: #27ae60;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #7f8c8d;
            border-top: 1px solid #bdc3c7;
            padding-top: 20px;
        }
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            font-size: 11px;
            text-transform: uppercase;
        }
        .status-en-cours { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
        .status-en-attente { background: linear-gradient(135deg, #ffc107, #fd7e14); color: #000; }
        .status-facture { background: linear-gradient(135deg, #007bff, #6610f2); }
        .status-paye { background: linear-gradient(135deg, #28a745, #20c997); }
        .status-annule { background: linear-gradient(135deg, #dc3545, #e83e8c); }
    </style>
    
    <div class="header">
        <h1>ERP CACAO</h1>
        <h2>Système de Gestion des Ventes</h2>
        <h2 style="color: #e74c3c; font-weight: bold;">FACTURE DE VENTE</h2>
    </div>';
    
    // Fonction pour obtenir le badge de statut
    function getStatutBadge($statut) {
        $badges = [
            'EN COURS' => '<span class="status-badge status-en-cours">En Cours</span>',
            'EN ATTENTE' => '<span class="status-badge status-en-attente">En Attente</span>',
            'FACTURE' => '<span class="status-badge status-facture">Facturé</span>',
            'PAYE' => '<span class="status-badge status-paye">Payé</span>',
            'ANNULE' => '<span class="status-badge status-annule">Annulé</span>'
        ];
        return $badges[$statut] ?? '<span class="status-badge">Inconnu</span>';
    }
    
    // Informations de la vente
    $html .= '
    <div class="info-section">
        <h3>📋 Informations de la Vente</h3>
        <table class="info-table">
            <tr>
                <td class="label">Référence Facture:</td>
                <td>' . htmlspecialchars($vente['facture_numero'] ?: 'VTE-' . $vente['id']) . '</td>
            </tr>
            <tr>
                <td class="label">Date de Vente:</td>
                <td>' . date('d/m/Y', strtotime($vente['date_vente'])) . '</td>
            </tr>
            <tr>
                <td class="label">Date de Facture:</td>
                <td>' . ($vente['facture_date'] ? date('d/m/Y', strtotime($vente['facture_date'])) : 'N/A') . '</td>
            </tr>
            <tr>
                <td class="label">Statut:</td>
                <td>' . getStatutBadge($vente['statut']) . '</td>
            </tr>
            <tr>
                <td class="label">N° Domiciliation:</td>
                <td>' . htmlspecialchars($vente['n_domiciliation'] ?: 'N/A') . '</td>
            </tr>
            <tr>
                <td class="label">DAU N°:</td>
                <td>' . htmlspecialchars($vente['dau_numero'] ?: 'N/A') . '</td>
            </tr>
            <tr>
                <td class="label">Lieu d\'Exportation:</td>
                <td>' . htmlspecialchars($vente['lieux_exportation'] ?: 'N/A') . '</td>
            </tr>
        </table>
    </div>';
    
    // Informations du client
    $html .= '
    <div class="info-section">
        <h3>👤 Informations Client</h3>
        <table class="info-table">
            <tr>
                <td class="label">Nom:</td>
                <td>' . htmlspecialchars($vente['client_nom']) . '</td>
            </tr>
            <tr>
                <td class="label">Type:</td>
                <td>' . htmlspecialchars($vente['type_client']) . '</td>
            </tr>';
    
    if ($vente['client_adresse']) {
        $html .= '
            <tr>
                <td class="label">Adresse:</td>
                <td>' . htmlspecialchars($vente['client_adresse']) . '</td>
            </tr>';
    }
    
    if ($vente['client_telephone']) {
        $html .= '
            <tr>
                <td class="label">Téléphone:</td>
                <td>' . htmlspecialchars($vente['client_telephone']) . '</td>
            </tr>';
    }
    
    if ($vente['client_nif']) {
        $html .= '
            <tr>
                <td class="label">NIF:</td>
                <td>' . htmlspecialchars($vente['client_nif']) . '</td>
            </tr>';
    }
    
    if ($vente['client_stat']) {
        $html .= '
            <tr>
                <td class="label">STAT:</td>
                <td>' . htmlspecialchars($vente['client_stat']) . '</td>
            </tr>';
    }
    
    $html .= '
        </table>
    </div>';
    
    // Détails des produits
    $html .= '
    <div class="info-section">
        <h3>📦 Détails des Produits</h3>
        <table class="details-table">
            <thead>
                <tr>
                    <th>Produit</th>
                    <th>Dépôt</th>
                    <th>Grade</th>
                    <th>Qualité</th>
                    <th class="text-right">Qté (T)</th>
                    <th class="text-right">Nb Lots</th>
                    <th>BL</th>
                    <th>Conteneur</th>
                    <th>Expédition</th>
                </tr>
            </thead>
            <tbody>';
    
    $totalQuantite = 0;
    
    foreach ($details as $detail) {
        $quantite = floatval($detail['qte_tonnes'] ?? 0);
        $totalQuantite += $quantite;
        
        $html .= '
                <tr>
                    <td>' . htmlspecialchars($detail['produit_nom']) . '</td>
                    <td>' . htmlspecialchars($detail['depot_libelle'] ?: 'N/A') . '</td>
                    <td>' . htmlspecialchars($detail['grade'] ?: 'N/A') . '</td>
                    <td>' . htmlspecialchars($detail['qualite'] ?: 'N/A') . '</td>
                    <td class="text-right">' . number_format($quantite, 3, ',', ' ') . '</td>
                    <td class="text-right">' . ($detail['nbre_lot'] ?: 'N/A') . '</td>
                    <td>' . htmlspecialchars($detail['bl'] ?: 'N/A') . '</td>
                    <td>' . htmlspecialchars($detail['conteneur'] ?: 'N/A') . '</td>
                    <td>' . htmlspecialchars($detail['expedition'] ?: 'N/A') . '</td>
                </tr>';
    }
    
    $html .= '
            </tbody>
        </table>
    </div>';
    
    // Totaux
    $html .= '
    <div class="total-section">
        <div class="total-row">
            <span class="total-label">Quantité Totale:</span>
            <span class="total-value">' . number_format($totalQuantite, 3, ',', ' ') . ' T</span>
        </div>
        <div class="total-row">
            <span class="total-label">Montant Total:</span>
            <span class="total-value">' . number_format($vente['total_montant'], 0, ',', ' ') . ' Ar</span>
        </div>';
    
    if ($vente['valeur_euro']) {
        $html .= '
        <div class="total-row">
            <span class="total-label">Valeur en EUR:</span>
            <span class="total-value">' . number_format($vente['valeur_euro'], 2, ',', ' ') . ' €</span>
        </div>';
    }
    
    if ($vente['cours_devise']) {
        $html .= '
        <div class="total-row">
            <span class="total-label">Cours de Change:</span>
            <span class="total-value">' . number_format($vente['cours_devise'], 4, ',', ' ') . ' Ar/EUR</span>
        </div>';
    }
    
    $html .= '
    </div>';
    
    // Footer
    $html .= '
    <div class="footer">
        <div style="margin-bottom: 15px;">
            <strong>🏢 ERP CACAO - Système de Gestion Intégré</strong>
        </div>
        <p>📅 Facture générée le ' . date('d/m/Y H:i') . ' par le système ERP CACAO</p>
        <p>✅ Ce document constitue une facture officielle de vente.</p>
        <p style="margin-top: 15px; font-style: italic;">
            Pour toute question concernant cette facture, veuillez contacter notre service commercial.
        </p>
    </div>';
    
    $mpdf->WriteHTML($html);
    
    // Nom du fichier
    $filename = 'Facture_Vente_' . ($vente['facture_numero'] ?: $vente['id']) . '_' . date('Y-m-d') . '.pdf';
    
    // Envoyer le PDF au navigateur
    $mpdf->Output($filename, 'I');
    
} catch (Exception $e) {
    die('Erreur lors de la génération du PDF: ' . $e->getMessage());
}
?>

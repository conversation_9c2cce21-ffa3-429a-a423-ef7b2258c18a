<?php
session_start();
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/vendor/autoload.php';

$paiementId = $_GET['id'] ?? null;

if (!$paiementId) {
    die('ID du paiement requis');
}

try {
    $db = Database::getInstance()->getConnection();
    
    // Récupérer les détails du paiement
    $stmt = $db->prepare("
        SELECT 
            p.*,
            ae.reference_achat,
            pr.nom as fournisseur_nom,
            pr.contact as fournisseur_contact,
            pr.adresse as fournisseur_adresse
        FROM operation_caisse p
        LEFT JOIN achat_entete ae ON p.achat_id = ae.id
        LEFT JOIN producteurs pr ON ae.fournisseur_id = pr.id
        WHERE p.id = ?
    ");
    $stmt->execute([$paiementId]);
    $paiement = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$paiement) {
        die('Paiement non trouvé');
    }
    
    // Récupérer les détails des produits
    if (!$paiement['achat_id']) {
        die('Aucun achat associé à ce paiement');
    }
    
    $stmt = $db->prepare("
        SELECT 
            ad.*,
            p.nom as produit_nom,
            u.libelle as unite_libelle
        FROM achat_detail ad
        LEFT JOIN produits p ON ad.produit_id = p.id
        LEFT JOIN unites u ON p.unite_stock_id = u.id
        WHERE ad.achat_entete_id = ?
    ");
    $stmt->execute([$paiement['achat_id']]);
    $produits = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    die('Erreur: ' . $e->getMessage());
}

function getStatutText($statut) {
    switch ($statut) {
        case 'EN_ATTENTE': return 'En Attente';
        case 'VALIDE': return 'Validé';
        case 'ANNULE': return 'Annulé';
        default: return $statut;
    }
}

function getModeText($mode) {
    switch ($mode) {
        case 'CHEQUE': return 'Chèque';
        case 'VIREMENT': return 'Virement';
        case 'ESPECE': return 'Espèce';
        default: return $mode;
    }
}

$date = date('d/m/Y H:i');
$statutText = getStatutText($paiement['statut']);
$modeText = getModeText($paiement['mode_paiement']);

// Calculer les totaux
$totalHT = 0;
$totalReduction = 0;
$hasPrices = false;

foreach ($produits as $produit) {
    $prixUnitaire = $produit['prix_unitaire_net'] ?? 0;
    if ($prixUnitaire > 0) {
        $hasPrices = true;
        $montantHT = ($prixUnitaire * $produit['qte_nette_controlee']) - ($produit['reduction'] ?? 0);
        $totalHT += $montantHT;
    }
    $totalReduction += $produit['reduction'] ?? 0;
}

$totalTTC = $totalHT * 1; // TVA 20%
$tva = $totalTTC - $totalHT;

// Générer le PDF avec mPDF
$mpdf = new \Mpdf\Mpdf([
    'mode' => 'utf-8',
    'format' => 'A4',
    'tempDir' => dirname(__DIR__) . '/temp'
]);

$mpdf->SetTitle('Reçu de Paiement - ' . ($paiement['reference_achat'] ?? 'N/A'));

// HTML du reçu
$html = generateReceiptHTML($paiement, $produits, $date, $statutText, $modeText, $hasPrices, $totalHT, $totalReduction, $tva, $totalTTC);

$mpdf->WriteHTML($html);

// Générer le nom du fichier
$filename = 'receipt_' . $paiementId . '_' . date('Y-m-d_H-i-s') . '.pdf';
$filepath = dirname(__DIR__) . '/uploads/receipts/' . $filename;

// Créer le dossier s'il n'existe pas
if (!is_dir(dirname($filepath))) {
    mkdir(dirname($filepath), 0755, true);
}

// Sauvegarder le PDF
$mpdf->Output($filepath, 'F');

// Rediriger vers le PDF
$pdf_url = '/erp3/uploads/receipts/' . $filename;
header('Location: ' . $pdf_url);
exit;

function generateReceiptHTML($paiement, $produits, $date, $statutText, $modeText, $hasPrices, $totalHT, $totalReduction, $tva, $totalTTC) {
    $html = '
    <style>
        body { 
            font-family: Arial, sans-serif; 
            font-size: 12px; 
            margin: 0; 
            padding: 20px;
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
            border-bottom: 2px solid #333; 
            padding-bottom: 15px; 
        }
        .company-name { 
            font-size: 24px; 
            font-weight: bold; 
            color: #333; 
            margin-bottom: 5px;
        }
        .receipt-title { 
            font-size: 18px; 
            font-weight: bold; 
            margin: 10px 0; 
            color: #007bff;
        }
        .info-section { 
            margin: 20px 0; 
            background: #f8f9fa;
            padding: 15px;
        }
        .info-row { 
            margin: 8px 0; 
            padding: 5px 0;
        }
        .info-label { 
            font-weight: bold; 
            display: inline-block;
            width: 150px; 
            color: #495057;
        }
        .info-value { 
            color: #212529;
        }
        .products-table { 
            width: 100%; 
            border-collapse: collapse; 
            margin: 25px 0; 
            background: white;
        }
        .products-table th, .products-table td { 
            border: 1px solid #dee2e6; 
            padding: 8px 6px; 
            text-align: left; 
            font-size: 10px;
        }
        .products-table th { 
            background-color: #007bff; 
            color: white;
            font-weight: bold;
        }
        .products-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .total-section { 
            margin-top: 25px; 
            text-align: right; 
            background: #f8f9fa;
            padding: 20px;
        }
        .total-row { 
            margin: 8px 0; 
        }
        .total-label { 
            font-weight: bold; 
            color: #495057;
            display: inline-block;
            width: 150px;
            text-align: right;
        }
        .total-value { 
            font-weight: bold;
            color: #212529;
            display: inline-block;
            width: 100px;
            text-align: right;
        }
        .total-final {
            border-top: 2px solid #007bff;
            padding-top: 10px;
            margin-top: 10px;
            font-size: 16px;
            color: #007bff;
        }
        .footer { 
            margin-top: 40px; 
            text-align: center; 
            font-size: 10px; 
            color: #6c757d; 
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }
        .status-badge { 
            padding: 4px 8px; 
            border-radius: 15px; 
            color: white; 
            font-weight: bold; 
            font-size: 10px;
        }
        .status-en-attente { background-color: #ffc107; }
        .status-valide { background-color: #28a745; }
        .status-annule { background-color: #dc3545; }
    </style>
    
    <div class="header">
        <div class="company-name">COOPERATIVE CACAO</div>
        <div>Reçu de Paiement</div>
        <div>Date d\'émission: ' . $date . '</div>
    </div>
    
    <div class="receipt-title">DÉTAILS DU PAIEMENT</div>
    
    <div class="info-section">
        <div class="info-row">
            <span class="info-label">Référence Achat:</span>
            <span class="info-value">' . htmlspecialchars($paiement['reference_achat'] ?? 'N/A') . '</span>
        </div>
        <div class="info-row">
            <span class="info-label">Fournisseur:</span>
            <span class="info-value">' . htmlspecialchars($paiement['fournisseur_nom'] ?? 'N/A') . '</span>
        </div>
        <div class="info-row">
            <span class="info-label">Date de Paiement:</span>
            <span class="info-value">' . htmlspecialchars($paiement['date_paiement'] ?? 'N/A') . '</span>
        </div>
        <div class="info-row">
            <span class="info-label">Mode de Paiement:</span>
            <span class="info-value">' . $modeText . '</span>
        </div>
        <div class="info-row">
            <span class="info-label">Référence:</span>
            <span class="info-value">' . htmlspecialchars($paiement['reference_paiement'] ?? 'N/A') . '</span>
        </div>
        <div class="info-row">
            <span class="info-label">Statut:</span>
            <span class="info-value">
                <span class="status-badge status-' . strtolower(str_replace('_', '-', $paiement['statut'])) . '">
                    ' . $statutText . '
                </span>
            </span>
        </div>
        <div class="info-row">
            <span class="info-label">Effectué par:</span>
            <span class="info-value">' . htmlspecialchars($paiement['effectue_par'] ?? 'N/A') . '</span>
        </div>
        <div class="info-row">
            <span class="info-label">Commentaires:</span>
            <span class="info-value">' . htmlspecialchars($paiement['commentaires'] ?? 'Aucun') . '</span>
        </div>
    </div>
    
    <div class="receipt-title">DÉTAILS DES PRODUITS</div>
    
    <table class="products-table">
        <thead>
            <tr>
                <th>Produit</th>
                <th>N° Lot</th>
                <th>Poids Net</th>
                <th>Poids Brut</th>
                <th>Ecart</th>
                <th>Nbr. Sacs</th>
                <th>Prix Unitaire</th>
                <th>Réduction</th>
                <th>Montant HT</th>
            </tr>
        </thead>
        <tbody>';
    
    foreach ($produits as $produit) {
        $prixUnitaire = $produit['prix_unitaire_net'] ?? 0;
        $montantHT = ($prixUnitaire * $produit['qte_nette_controlee']) - ($produit['reduction'] ?? 0);
        
        $html .= '
            <tr>
                <td>' . htmlspecialchars($produit['produit_nom'] ?? 'N/A') . '</td>
                <td>' . htmlspecialchars($produit['lot_numero'] ?? 'N/A') . '</td>
                <td>' . number_format($produit['qte_nette_controlee'] ?? 0, 2) . ' ' . htmlspecialchars($produit['unite_libelle'] ?? 'kg') . '</td>
                <td>' . number_format($produit['qte_brute_saisie'] ?? 0, 2) . ' kg</td>
                <td>' . number_format($produit['ecart_controle'] ?? 0, 2) . ' kg</td>
                <td>' . number_format($produit['nombre_sacs'] ?? 0, 2) . '</td>
                <td>' . ($prixUnitaire > 0 ? number_format($prixUnitaire, 0) . ' Ar' : 'Non saisi') . '</td>
                <td>' . number_format($produit['reduction'] ?? 0, 0) . ' Ar</td>
                <td>' . ($prixUnitaire > 0 ? number_format($montantHT, 0) . ' Ar' : 'Non calculé') . '</td>
            </tr>';
    }
    
    $html .= '
        </tbody>
    </table>
    
    <div class="total-section">';
    
    if ($hasPrices) {
        $html .= '
            <div class="total-row">
                <span class="total-label">Montant HT:</span>
                <span class="total-value">' . number_format($totalHT, 0) . ' Ar</span>
            </div>
            <div class="total-row">
                <span class="total-label">Réduction:</span>
                <span class="total-value">' . number_format($totalReduction, 0) . ' Ar</span>
            </div>
            <div class="total-row">
                <span class="total-label">TVA (20%):</span>
                <span class="total-value">' . number_format($tva, 0) . ' Ar</span>
            </div>
            <div class="total-row total-final">
                <span class="total-label">TOTAL TTC:</span>
                <span class="total-value">' . number_format($totalTTC, 0) . ' Ar</span>
            </div>';
    } else {
        $html .= '
            <div class="total-row">
                <span class="total-label">Statut:</span>
                <span class="total-value" style="color: #ffc107; font-weight: bold;">Prix non saisis</span>
            </div>
            <div class="total-row">
                <span class="total-label">Réduction:</span>
                <span class="total-value">' . number_format($totalReduction, 0) . ' Ar</span>
            </div>
            <div class="total-row">
                <span class="total-label">Note:</span>
                <span class="total-value" style="color: #6c757d; font-style: italic;">Les prix seront saisis lors du paiement</span>
            </div>';
    }
    
    $html .= '
    </div>
    
    <div class="footer">
        <p>Ce reçu a été généré automatiquement le ' . $date . '</p>
        <p>Pour toute question, contactez l\'administration</p>
    </div>';
    
    return $html;
}
?>

# 📄 Documentation des Fichiers PDF/Reçus

## 🎯 **Fichier Principal à Utiliser : `receipt_paiement.php`**

### ✅ **`pages/receipt_paiement.php`** (RECOMMANDÉ)
- **Type :** HTML direct dans le navigateur
- **Utilisation :** Reçu de paiement simple
- **Avantages :**
  - ✅ Pas de fichier PDF créé
  - ✅ Affichage direct dans le navigateur
  - ✅ Bouton d'impression intégré
  - ✅ Design professionnel
  - ✅ Aucun stockage de fichier
- **Inconvénients :** Aucun
- **URL :** `pages/receipt_paiement.php?id={paiementId}`

---

## 📋 **Autres Fichiers (Pour Information)**

### 🔄 **`pages/facture_paiement.php`** (VERSION HTML)
- **Type :** HTML avec mPDF (mais affiche HTML)
- **Utilisation :** Version HTML du reçu
- **Statut :** Conservé pour compatibilité
- **URL :** `pages/facture_paiement.php?id={paiementId}`

### 📄 **`pages/facture_paiement_pdf.php`** (VERSION PDF)
- **Type :** Génération PDF avec mPDF
- **Utilisation :** Crée un fichier PDF et redirige
- **Avantages :** PDF téléchargeable
- **Inconvénients :** Crée des fichiers sur le serveur
- **URL :** `pages/facture_paiement_pdf.php?id={paiementId}`

### 🛒 **`includes/generate_achat_pdf.php`** (FACTURE ACHAT)
- **Type :** PDF pour les achats (pas les paiements)
- **Utilisation :** Facture d'achat complet
- **Différence :** C'est pour les achats, pas les paiements
- **URL :** `includes/generate_achat_pdf.php?id={achatId}`

---

## 🎯 **Recommandation Finale**

### **Pour les Reçus de Paiement :**
```javascript
// ✅ UTILISER CECI
function printPaiement(paiementId) {
    const url = `pages/receipt_paiement.php?id=${paiementId}`;
    window.open(url, '_blank');
}
```

### **Pour les Factures d'Achat :**
```javascript
// ✅ UTILISER CECI pour les achats
function printAchat(achatId) {
    const url = `includes/generate_achat_pdf.php?id=${achatId}`;
    window.open(url, '_blank');
}
```

---

## 📊 **Comparaison des Solutions**

| Fichier | Type | Stockage | Performance | Recommandé |
|---------|------|----------|-------------|------------|
| `receipt_paiement.php` | HTML | ❌ Non | ⚡ Rapide | ✅ **OUI** |
| `facture_paiement.php` | HTML | ❌ Non | ⚡ Rapide | ⚠️ Compatibilité |
| `facture_paiement_pdf.php` | PDF | ✅ Oui | 🐌 Lent | ❌ Non |
| `generate_achat_pdf.php` | PDF | ✅ Oui | 🐌 Lent | ✅ Pour achats |

---

## 🚀 **Action Recommandée**

1. **Utiliser `receipt_paiement.php`** pour tous les reçus de paiement
2. **Supprimer les anciens fichiers** si souhaité (optionnel)
3. **Garder `generate_achat_pdf.php`** pour les factures d'achat

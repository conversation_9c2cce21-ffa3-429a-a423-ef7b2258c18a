<?php
function check_page_permission($permission) {
    if (!rbac_can($permission)) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Accès refusé',
            'redirect' => 'login.php'
        ]);
        exit;
    }
    return true;
}

function check_action_permission($permission) {
    if (!rbac_can($permission)) {
        return false;
    }
    return true;
}

function get_user_permissions() {
    $role = RBAC::currentUserRoleName();
    $rbac = rbac_instance();
    return $rbac->getPermissionsForRoleName($role);
}

function can_perform_action($action) {
    return check_action_permission($action);
}

// Utilisation dans les pages :
/*
// Au début de chaque page
require_once '../includes/permissions_helper.php';
check_page_permission('page.nom_page.view');

// Pour les actions spécifiques
if (can_perform_action('action.specifique')) {
    // Effectuer l'action
}
*/
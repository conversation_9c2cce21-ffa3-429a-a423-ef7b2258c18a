-- Script pour créer le système de mouvements de stock
-- Alimentation automatique à partir des tables achat_detail et ventes_details

-- ========================================
-- ÉTAPE 1: Créer la table des mouvements de stock
-- ========================================

CREATE TABLE IF NOT EXISTS `mouvements_stock` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `produit_id` INT NOT NULL,
  `depot_id` INT NOT NULL,
  `type_mouvement` ENUM('ENTREE', 'SORTIE', 'AJUSTEMENT', 'TRANSFERT') NOT NULL,
  `quantite` DECIMAL(10,2) NOT NULL COMMENT 'Quantité en unité de stock du produit',
  `unite_id` INT NOT NULL,
  `prix_unitaire` DECIMAL(12,2) DEFAULT 0.00,
  `montant_total` DECIMAL(18,2) DEFAULT 0.00,
  
  -- Références aux transactions sources
  `source_type` ENUM('ACHAT', 'VENTE', 'AJUSTEMENT', 'TRANSFERT') NOT NULL,
  `source_id` INT DEFAULT NULL COMMENT 'ID de la transaction source (achat_detail, ventes_details, etc.)',
  `source_reference` VARCHAR(100) DEFAULT NULL COMMENT 'Référence de la transaction (ACH-2025-0001, VTE-2025-0001, etc.)',
  
  -- Informations sur le lot (si applicable)
  `lot_numero` VARCHAR(100) DEFAULT NULL,
  `date_expiration` DATE DEFAULT NULL,
  
  -- Informations de traçabilité
  `date_mouvement` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP(),
  `date_creation` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP(),
  `cree_par` VARCHAR(255) DEFAULT NULL,
  `date_derniere_modif` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP(),
  `dernier_modif_par` VARCHAR(255) DEFAULT NULL,
  
  -- Clés étrangères
  FOREIGN KEY (`produit_id`) REFERENCES `produits`(`id`),
  FOREIGN KEY (`depot_id`) REFERENCES `depot`(`id`),
  FOREIGN KEY (`unite_id`) REFERENCES `unites`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- ÉTAPE 2: Créer les triggers pour les achats
-- ========================================

-- Trigger pour INSERT dans achat_detail
DELIMITER $$
CREATE TRIGGER `tr_achat_detail_insert` 
AFTER INSERT ON `achat_detail`
FOR EACH ROW
BEGIN
    -- Récupérer l'unité de stock du produit
    DECLARE unite_stock_id INT;
    SELECT unite_stock_id INTO unite_stock_id FROM produits WHERE id = NEW.produit_id;
    
    -- Récupérer la référence de l'achat
    DECLARE ref_achat VARCHAR(100);
    SELECT reference_achat INTO ref_achat FROM achat_entete WHERE id = NEW.achat_entete_id;
    
    -- Insérer le mouvement d'entrée
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        prix_unitaire, montant_total, source_type, source_id, source_reference,
        lot_numero, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'ENTREE', NEW.qte_nette_controlee,
        unite_stock_id, NEW.prix_unitaire_net, NEW.montant_ht,
        'ACHAT', NEW.id, ref_achat, NEW.lot_id, NEW.date_creation, NEW.cree_par
    );
END$$
DELIMITER ;

-- Trigger pour UPDATE dans achat_detail
DELIMITER $$
CREATE TRIGGER `tr_achat_detail_update` 
AFTER UPDATE ON `achat_detail`
FOR EACH ROW
BEGIN
    -- Supprimer l'ancien mouvement
    DELETE FROM mouvements_stock 
    WHERE source_type = 'ACHAT' AND source_id = NEW.id;
    
    -- Récupérer l'unité de stock du produit
    DECLARE unite_stock_id INT;
    SELECT unite_stock_id INTO unite_stock_id FROM produits WHERE id = NEW.produit_id;
    
    -- Récupérer la référence de l'achat
    DECLARE ref_achat VARCHAR(100);
    SELECT reference_achat INTO ref_achat FROM achat_entete WHERE id = NEW.achat_entete_id;
    
    -- Insérer le nouveau mouvement d'entrée
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        prix_unitaire, montant_total, source_type, source_id, source_reference,
        lot_numero, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'ENTREE', NEW.qte_nette_controlee,
        unite_stock_id, NEW.prix_unitaire_net, NEW.montant_ht,
        'ACHAT', NEW.id, ref_achat, NEW.lot_id, NEW.date_creation, NEW.cree_par
    );
END$$
DELIMITER ;

-- Trigger pour DELETE dans achat_detail
DELIMITER $$
CREATE TRIGGER `tr_achat_detail_delete` 
AFTER DELETE ON `achat_detail`
FOR EACH ROW
BEGIN
    -- Supprimer le mouvement correspondant
    DELETE FROM mouvements_stock 
    WHERE source_type = 'ACHAT' AND source_id = OLD.id;
END$$
DELIMITER ;

-- ========================================
-- ÉTAPE 3: Créer les triggers pour les ventes
-- ========================================

-- Trigger pour INSERT dans ventes_details
DELIMITER $$
CREATE TRIGGER `tr_ventes_details_insert` 
AFTER INSERT ON `ventes_details`
FOR EACH ROW
BEGIN
    -- Récupérer l'unité de stock du produit
    DECLARE unite_stock_id INT;
    SELECT unite_stock_id INTO unite_stock_id FROM produits WHERE id = NEW.produit_id;
    
    -- Récupérer la référence de la vente
    DECLARE ref_vente VARCHAR(100);
    SELECT facture_numero INTO ref_vente FROM ventes_entete WHERE id = NEW.vente_id;
    
    -- Insérer le mouvement de sortie
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        source_type, source_id, source_reference, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'SORTIE', NEW.qte_tonnes,
        unite_stock_id, 'VENTE', NEW.id, ref_vente, NOW(), 'system'
    );
END$$
DELIMITER ;

-- Trigger pour UPDATE dans ventes_details
DELIMITER $$
CREATE TRIGGER `tr_ventes_details_update` 
AFTER UPDATE ON `ventes_details`
FOR EACH ROW
BEGIN
    -- Supprimer l'ancien mouvement
    DELETE FROM mouvements_stock 
    WHERE source_type = 'VENTE' AND source_id = NEW.id;
    
    -- Récupérer l'unité de stock du produit
    DECLARE unite_stock_id INT;
    SELECT unite_stock_id INTO unite_stock_id FROM produits WHERE id = NEW.produit_id;
    
    -- Récupérer la référence de la vente
    DECLARE ref_vente VARCHAR(100);
    SELECT facture_numero INTO ref_vente FROM ventes_entete WHERE id = NEW.vente_id;
    
    -- Insérer le nouveau mouvement de sortie
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        source_type, source_id, source_reference, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'SORTIE', NEW.qte_tonnes,
        unite_stock_id, 'VENTE', NEW.id, ref_vente, NOW(), 'system'
    );
END$$
DELIMITER ;

-- Trigger pour DELETE dans ventes_details
DELIMITER $$
CREATE TRIGGER `tr_ventes_details_delete` 
AFTER DELETE ON `ventes_details`
FOR EACH ROW
BEGIN
    -- Supprimer le mouvement correspondant
    DELETE FROM mouvements_stock 
    WHERE source_type = 'VENTE' AND source_id = OLD.id;
END$$
DELIMITER ;

-- ========================================
-- ÉTAPE 4: Créer une vue pour le stock actuel
-- ========================================

CREATE OR REPLACE VIEW `v_stock_actuel` AS
SELECT 
    p.id as produit_id,
    p.nom as produit_nom,
    d.id as depot_id,
    d.libelle as depot_nom,
    u.libelle as unite_stock,
    COALESCE(SUM(
        CASE 
            WHEN m.type_mouvement = 'ENTREE' THEN m.quantite
            WHEN m.type_mouvement = 'SORTIE' THEN -m.quantite
            ELSE 0
        END
    ), 0) as stock_actuel,
    p.stock_min,
    p.stock_max,
    CASE 
        WHEN COALESCE(SUM(
            CASE 
                WHEN m.type_mouvement = 'ENTREE' THEN m.quantite
                WHEN m.type_mouvement = 'SORTIE' THEN -m.quantite
                ELSE 0
            END
        ), 0) <= p.stock_min THEN 'STOCK_FAIBLE'
        WHEN COALESCE(SUM(
            CASE 
                WHEN m.type_mouvement = 'ENTREE' THEN m.quantite
                WHEN m.type_mouvement = 'SORTIE' THEN -m.quantite
                ELSE 0
            END
        ), 0) >= p.stock_max THEN 'STOCK_ELEVE'
        ELSE 'STOCK_NORMAL'
    END as statut_stock
FROM produits p
CROSS JOIN depot d
LEFT JOIN mouvements_stock m ON p.id = m.produit_id AND d.id = m.depot_id
LEFT JOIN unites u ON p.unite_stock_id = u.id
WHERE d.est_actif = 1
GROUP BY p.id, d.id, p.nom, d.libelle, u.libelle, p.stock_min, p.stock_max;

-- ========================================
-- ÉTAPE 5: Créer une vue pour l'historique des mouvements
-- ========================================

CREATE OR REPLACE VIEW `v_historique_mouvements` AS
SELECT 
    m.id,
    m.date_mouvement,
    p.nom as produit_nom,
    d.libelle as depot_nom,
    m.type_mouvement,
    m.quantite,
    u.libelle as unite,
    m.prix_unitaire,
    m.montant_total,
    m.source_type,
    m.source_reference,
    m.lot_numero,
    m.date_expiration,
    m.cree_par
FROM mouvements_stock m
JOIN produits p ON m.produit_id = p.id
JOIN depot d ON m.depot_id = d.id
JOIN unites u ON m.unite_id = u.id
ORDER BY m.date_mouvement DESC;

-- ========================================
-- ÉTAPE 6: Insérer des données de test (optionnel)
-- ========================================

-- Insérer quelques mouvements de test pour démonstration
INSERT INTO mouvements_stock (
    produit_id, depot_id, type_mouvement, quantite, unite_id,
    prix_unitaire, montant_total, source_type, source_reference,
    date_mouvement, cree_par
) VALUES 
(5, 1, 'ENTREE', 100.00, 2, 36000.00, 3600000.00, 'AJUSTEMENT', 'AJUST-001', NOW(), 'admin'),
(5, 1, 'SORTIE', 20.00, 2, 0.00, 0.00, 'AJUSTEMENT', 'AJUST-002', NOW(), 'admin'),
(6, 2, 'ENTREE', 50.00, 2, 40000.00, 2000000.00, 'AJUSTEMENT', 'AJUST-003', NOW(), 'admin');

-- ========================================
-- ÉTAPE 7: Vérification
-- ========================================

-- Vérifier que les tables et vues ont été créées
SELECT 'Tables créées' as Status, COUNT(*) as Nombre FROM information_schema.tables 
WHERE table_schema = 'erp_cacao' AND table_name = 'mouvements_stock';

SELECT 'Vues créées' as Status, COUNT(*) as Nombre FROM information_schema.views 
WHERE table_schema = 'erp_cacao' AND table_name IN ('v_stock_actuel', 'v_historique_mouvements');

-- Afficher un échantillon des données
SELECT 'Échantillon mouvements' as Status, COUNT(*) as Nombre FROM mouvements_stock;
SELECT 'Échantillon stock actuel' as Status, COUNT(*) as Nombre FROM v_stock_actuel;

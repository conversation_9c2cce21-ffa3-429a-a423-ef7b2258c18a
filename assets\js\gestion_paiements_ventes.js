/**
 * Gestion des Paiements de Ventes - JavaScript
 * Réplique exactement le workflow du système de paiements d'achats
 * Utilise les actions CRUD appropriées pour éviter l'erreur "Seules les requêtes SELECT sont autorisées"
 */

$(document).ready(function() {
    // Variables globales
    let tablePaiementsVentes;
    let currentVenteId = null;
    let currentClientId = null;
    let currentUser = '<?php echo $_SESSION["username"] ?? ""; ?>';

    // Initialisation
    initializePage();

    function initializePage() {
        initializeDataTable();
        loadFilters();
        loadPaiementsVentes();
        bindEvents();
        setDefaultDate();
    }

    function initializeDataTable() {
        tablePaiementsVentes = $('#tablePaiementsVentes').DataTable({
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            pageLength: 25,
            order: [[3, 'desc']], // Date paiement desc
            columnDefs: [
                { targets: [0, 9], orderable: false }, // Checkbox et Actions
                { targets: [6], className: 'text-end' }, // Montant
                { targets: [7], className: 'text-center' } // Statut
            ]
        });
    }

    function loadFilters() {
        // Charger les clients pour le filtre
        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'clients',
            sql: 'SELECT id, nom FROM clients ORDER BY nom'
        })
        .done(function(response) {
            if (response.success) {
                let options = '<option value="">Tous les clients</option>';
                response.data.forEach(client => {
                    options += `<option value="${client.id}">${client.nom}</option>`;
                });
                $('#filterClient').html(options);
            }
        })
        .fail(function() {
            showAlert('Erreur', 'Erreur lors du chargement des clients', 'error');
        });
    }

    function loadPaiementsVentes() {
        const sql = `
            SELECT 
                oc.id,
                oc.vente_id,
                oc.client_id,
                oc.date_paiement,
                oc.mode_paiement,
                oc.reference_paiement,
                oc.montant,
                oc.statut,
                oc.effectue_par,
                oc.commentaires,
                ve.facture_numero as reference_vente,
                c.nom as nom_client
            FROM operation_caisse oc
            LEFT JOIN ventes_entete ve ON oc.vente_id = ve.id
            LEFT JOIN clients c ON oc.client_id = c.id
            WHERE oc.type_operation = 'ENCAISSEMENT_VENTE'
            ORDER BY oc.date_paiement DESC, oc.id DESC
        `;

        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'operation_caisse',
            sql: sql
        })
        .done(function(response) {
            if (response.success) {
                populateTablePaiements(response.data);
            } else {
                showAlert('Erreur', 'Erreur lors du chargement des paiements: ' + response.message, 'error');
            }
        })
        .fail(function() {
            showAlert('Erreur', 'Erreur lors du chargement des paiements', 'error');
        });
    }

    function populateTablePaiements(paiements) {
        tablePaiementsVentes.clear();
        
        paiements.forEach(paiement => {
            const statutBadge = getStatutBadge(paiement.statut);
            const montantFormate = formatMontant(paiement.montant);
            
            const row = [
                `<input type="checkbox" class="paiement-checkbox" value="${paiement.id}">`,
                paiement.reference_vente || 'N/A',
                paiement.nom_client || 'N/A',
                formatDate(paiement.date_paiement),
                paiement.mode_paiement || 'N/A',
                paiement.reference_paiement || 'N/A',
                montantFormate,
                statutBadge,
                paiement.effectue_par || 'N/A',
                `<div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-info btn-sm" onclick="viewPaiementDetails(${paiement.id})" title="Voir détails">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${paiement.statut === 'EN_ATTENTE' ?
                        `<button class="btn btn-outline-warning btn-sm" onclick="editPaiement(${paiement.id})" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </button>` : ''
                    }
                    ${paiement.statut === 'VALIDE' ?
                        `<button class="btn btn-outline-success btn-sm" onclick="printReceiptVente(${paiement.id})" title="Imprimer reçu">
                            <i class="fas fa-print"></i>
                        </button>` : ''
                    }
                    <button class="btn btn-outline-primary btn-sm" onclick="changeStatutPaiement(${paiement.id}, '${paiement.statut}')" title="Changer statut">
                        <i class="fas fa-exchange-alt"></i>
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="deletePaiement(${paiement.id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>`
            ];
            
            tablePaiementsVentes.row.add(row);
        });
        
        tablePaiementsVentes.draw();
    }

    function bindEvents() {
        // Événements des filtres
        $('#filterClient, #filterModePaiement, #filterDateFrom').on('change', function() {
            applyFilters();
        });

        $('#btnResetFilters').on('click', function() {
            resetFilters();
        });

        // Événements du modal
        $('#modalPaiementVente').on('show.bs.modal', function() {
            // Ne réinitialiser que si ce n'est pas une édition
            if (!$('#btnSavePaiementVente').data('edit-id')) {
                resetForm();
                loadVentesFacturees();
            }
        });

        // Changement de mode de paiement
        $('#modePaiementSelect').on('change', function() {
            toggleReferenceField();
        });

        // Changement de vente sélectionnée
        $('#venteSelect').on('change', function() {
            const venteId = $(this).val();
            if (venteId) {
                loadProduitsForPaiement(venteId);
            } else {
                clearProduitsTable();
            }
        });

        // Sauvegarde du paiement
        $('#btnSavePaiementVente').on('click', function() {
            savePaiementVente();
        });

        // Conversion devise
        $('#coursDevise').on('input', function() {
            updateCurrencyConversion();
        });

        // Calcul automatique des montants
        $(document).on('input', '.prix-input, .reduction-input', function() {
            calculateRowTotal($(this).closest('tr'));
            calculateGrandTotal();
            updateCurrencyConversion();
        });

        // Sélection multiple
        $('#selectAllPaiements').on('change', function() {
            $('.paiement-checkbox').prop('checked', $(this).prop('checked'));
        });

        // Génération de reçu
        $('#btnGenerateReceiptVente').on('click', function() {
            generateReceiptVente();
        });

        // Nouveau paiement
        $('#btnNouveauPaiement').on('click', function() {
            // Réinitialiser le formulaire pour un nouveau paiement
            $('#btnSavePaiementVente').removeData('edit-id');
            $('#modalPaiementVente .modal-title').html('<i class="fas fa-credit-card"></i> Nouveau Paiement de Vente');
            resetForm();
            loadVentesFacturees();
            $('#modalPaiementVente').modal('show');
        });
    }

    function setDefaultDate() {
        const today = new Date().toISOString().split('T')[0];
        $('#datePaiement').val(today);
    }

    function loadVentesFacturees() {
        const sql = `
            SELECT
                ve.id,
                ve.facture_numero,
                ve.date_vente,
                ve.total_montant,
                c.nom as nom_client,
                c.id as client_id
            FROM ventes_entete ve
            LEFT JOIN clients c ON ve.client_id = c.id
            WHERE ve.statut = 'FACTURE'
            ORDER BY ve.date_vente DESC
        `;

        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'ventes_entete',
            sql: sql
        })
        .done(function(response) {
            if (response.success) {
                let options = '<option value="">Sélectionner une vente</option>';
                response.data.forEach(vente => {
                    const montant = formatMontant(vente.total_montant);
                    const reference = vente.facture_numero || `Vente #${vente.id}`;
                    options += `<option value="${vente.id}" data-client-id="${vente.client_id}">
                        ${reference} - ${vente.nom_client} - ${montant}
                    </option>`;
                });
                $('#venteSelect').html(options);
            } else {
                showAlert('Erreur', 'Erreur lors du chargement des ventes: ' + response.message, 'error');
            }
        })
        .fail(function() {
            showAlert('Erreur', 'Erreur lors du chargement des ventes', 'error');
        });
    }

    function loadProduitsForPaiement(venteId) {
        currentVenteId = venteId;
        currentClientId = $('#venteSelect option:selected').data('client-id');

        const sql = `
            SELECT
                vd.id,
                vd.produit_id,
                vd.qte_tonnes,
                vd.grade,
                vd.qualite,
                p.nom as nom_produit,
                p.prix_vente
            FROM ventes_details vd
            LEFT JOIN produits p ON vd.produit_id = p.id
            WHERE vd.vente_id = ${venteId}
            ORDER BY p.nom
        `;

        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'ventes_details',
            sql: sql
        })
        .done(function(response) {
            if (response.success) {
                populateProduitsTable(response.data);
            } else {
                showAlert('Erreur', 'Erreur lors du chargement des produits: ' + response.message, 'error');
            }
        })
        .fail(function() {
            showAlert('Erreur', 'Erreur lors du chargement des produits', 'error');
        });
    }

    function populateProduitsTable(produits) {
        const tbody = $('#tableProduitsVentePrix tbody');
        tbody.empty();

        produits.forEach(produit => {
            const prix = produit.prix_vente || 0; // Utilise prix_vente du produit comme prix par défaut
            const reduction = 0; // Pas de réduction par défaut
            const quantite = produit.qte_tonnes || 0;
            const montant = (quantite * prix) - reduction;

            const row = `
                <tr data-produit-id="${produit.id}">
                    <td>${produit.nom_produit}</td>
                    <td>${produit.grade || 'N/A'}</td>
                    <td>${produit.qualite || 'N/A'}</td>
                    <td class="text-center">${formatNumber(quantite)}</td>
                    <td class="text-center">
                        <input type="number" class="form-control form-control-sm prix-input"
                               value="${prix}" step="0.01" min="0">
                    </td>
                    <td class="text-center">
                        <input type="number" class="form-control form-control-sm reduction-input"
                               value="${reduction}" step="0.01" min="0">
                    </td>
                    <td class="text-center montant-cell">${formatMontant(montant)}</td>
                </tr>
            `;
            tbody.append(row);
        });

        calculateGrandTotal();
        updateCurrencyConversion();
    }

    function calculateRowTotal(row) {
        const quantite = parseFloat(row.find('td:nth-child(4)').text()) || 0;
        const prix = parseFloat(row.find('.prix-input').val()) || 0;
        const reduction = parseFloat(row.find('.reduction-input').val()) || 0;
        
        const montant = (quantite * prix) - reduction;
        row.find('.montant-cell').text(formatMontant(montant));
    }

    function calculateGrandTotal() {
        let totalHT = 0;
        let totalReduction = 0;

        $('#tableProduitsVentePrix tbody tr').each(function() {
            const quantite = parseFloat($(this).find('td:nth-child(4)').text()) || 0;
            const prix = parseFloat($(this).find('.prix-input').val()) || 0;
            const reduction = parseFloat($(this).find('.reduction-input').val()) || 0;
            
            totalHT += quantite * prix;
            totalReduction += reduction;
        });

        const totalTTC = totalHT - totalReduction;

        $('#montantHT').text(formatMontant(totalHT));
        $('#montantReduction').text(formatMontant(totalReduction));
        $('#montantTTC').text(formatMontant(totalTTC));
    }

    function updateCurrencyConversion() {
        const cours = parseFloat($('#coursDevise').val()) || 0;
        const totalAr = parseFloat($('#montantTTC').text().replace(/[^\d.-]/g, '')) || 0;

        if (cours > 0) {
            const valeurEur = totalAr / cours;
            $('#valeurEUR').text(formatNumber(valeurEur, 2) + ' €');
            $('#valeurAR').text(formatMontant(totalAr));
        } else {
            $('#valeurEUR').text('0 €');
            $('#valeurAR').text(formatMontant(totalAr));
        }
    }

    function savePaiementVente() {
        // Vérifier si c'est une édition ou une création
        const editId = $('#btnSavePaiementVente').data('edit-id');
        const isEdit = editId !== undefined;

        // Validation du formulaire
        const venteId = $('#venteSelect').val();
        const modePaiement = $('#modePaiementSelect').val();
        const datePaiement = $('#datePaiement').val();
        const statut = $('#statutPaiement').val();

        if (!venteId || !modePaiement || !datePaiement || !statut) {
            showAlert('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');
            return;
        }

        // Vérification des prix
        let hasValidPrices = false;
        $('#tableProduitsVentePrix tbody tr').each(function() {
            const prix = parseFloat($(this).find('.prix-input').val()) || 0;
            if (prix > 0) {
                hasValidPrices = true;
                return false; // break
            }
        });

        if (!hasValidPrices) {
            showAlert('Erreur', 'Veuillez saisir au moins un prix pour les produits', 'error');
            return;
        }

        // Collecte des données
        const totalTTC = parseFloat($('#montantTTC').text().replace(/[^\d.-]/g, '')) || 0;
        const referencePaiement = $('#referencePaiement').val();
        const commentaires = $('#commentairesPaiement').val();

        // Note: Les prix sont saisis dans l'interface mais ne sont pas stockés dans ventes_details
        // car cette table n'a pas de colonnes prix_unitaire et reduction

        // Préparer les données du paiement
        const paiementData = {
            type_operation: 'ENCAISSEMENT_VENTE',
            vente_id: venteId,
            client_id: currentClientId,
            montant: totalTTC,
            date_paiement: datePaiement,
            mode_paiement: modePaiement,
            reference_paiement: referencePaiement,
            statut: statut,
            commentaires: commentaires,
            effectue_par: currentUser
        };

        // Ajouter les champs spécifiques selon le mode (création/édition)
        if (isEdit) {
            paiementData.dernier_modif_par = currentUser;
        } else {
            paiementData.cree_par = currentUser;
        }

        // Créer ou mettre à jour le paiement
        const ajaxConfig = {
            action: isEdit ? 'update' : 'create',
            table: 'operation_caisse',
            data: JSON.stringify(paiementData)
        };

        // Ajouter l'ID pour l'édition
        if (isEdit) {
            ajaxConfig.id = editId;
        }

        $.post('../includes/traitement.php', ajaxConfig)
        .done(function(response) {
            if (response.success) {
                // Mettre à jour le statut de la vente à 'PAYE' seulement lors de la création d'un nouveau paiement
                if (!isEdit && statut === 'VALIDE') {
                    $.post('../includes/traitement.php', {
                        action: 'update',
                        table: 'ventes_entete',
                        id: venteId,
                        data: JSON.stringify({
                            statut: 'PAYE',
                            total_montant: totalTTC
                        })
                    })
                    .done(function(updateResponse) {
                        if (updateResponse.success) {
                            const message = isEdit ? 'Paiement modifié avec succès' : 'Paiement créé avec succès';
                            showAlert('Succès', message, 'success');
                            resetFormAndClose();
                        } else {
                            showAlert('Erreur', 'Erreur lors de la mise à jour du statut: ' + updateResponse.message, 'error');
                        }
                    })
                    .fail(function() {
                        showAlert('Erreur', 'Erreur lors de la mise à jour du statut de la vente', 'error');
                    });
                } else {
                    // Pas de mise à jour de statut nécessaire
                    const message = isEdit ? 'Paiement modifié avec succès' : 'Paiement créé avec succès';
                    showAlert('Succès', message, 'success');
                    resetFormAndClose();
                }
            } else {
                const action = isEdit ? 'modification' : 'création';
                showAlert('Erreur', `Erreur lors de la ${action} du paiement: ` + response.message, 'error');
            }
        })
        .fail(function() {
            const action = isEdit ? 'modification' : 'création';
            showAlert('Erreur', `Erreur lors de la ${action} du paiement`, 'error');
        });
    }

    function resetFormAndClose() {
        $('#modalPaiementVente').modal('hide');
        $('#btnSavePaiementVente').removeData('edit-id');
        $('#modalPaiementVente .modal-title').html('<i class="fas fa-credit-card"></i> Nouveau Paiement de Vente');
        loadPaiementsVentes();
    }

    function toggleReferenceField() {
        const modePaiement = $('#modePaiementSelect').val();
        const referenceGroup = $('#referencePaiementGroup');
        const referenceLabel = $('#referencePaiementLabel');
        const referenceInput = $('#referencePaiement');

        if (modePaiement === 'CHEQUE') {
            referenceGroup.show();
            referenceLabel.text('Numéro de chèque');
            referenceInput.attr('placeholder', 'Numéro de chèque');
            referenceInput.prop('required', true);
        } else if (modePaiement === 'VIREMENT') {
            referenceGroup.show();
            referenceLabel.text('Référence virement');
            referenceInput.attr('placeholder', 'Référence virement');
            referenceInput.prop('required', true);
        } else {
            referenceGroup.hide();
            referenceInput.prop('required', false);
            referenceInput.val('');
        }
    }

    function clearProduitsTable() {
        $('#tableProduitsVentePrix tbody').empty();
        $('#montantHT').text('0 Ar');
        $('#montantReduction').text('0 Ar');
        $('#montantTTC').text('0 Ar');
        $('#valeurEUR').text('0 €');
        $('#valeurAR').text('0 Ar');
    }

    function resetForm() {
        $('#formPaiementVente')[0].reset();
        $('#venteSelect').val('');
        $('#modePaiementSelect').val('');
        $('#statutPaiement').val('EN_ATTENTE');
        $('#referencePaiementGroup').hide();
        $('#coursDevise').val('');
        clearProduitsTable();
        setDefaultDate();
        currentVenteId = null;
        currentClientId = null;
    }

    function applyFilters() {
        const clientFilter = $('#filterClient').val();
        const modeFilter = $('#filterModePaiement').val();
        const dateFilter = $('#filterDateFrom').val();

        let sql = `
            SELECT
                oc.id,
                oc.vente_id,
                oc.client_id,
                oc.date_paiement,
                oc.mode_paiement,
                oc.reference_paiement,
                oc.montant,
                oc.statut,
                oc.effectue_par,
                oc.commentaires,
                ve.facture_numero as reference_vente,
                c.nom as nom_client
            FROM operation_caisse oc
            LEFT JOIN ventes_entete ve ON oc.vente_id = ve.id
            LEFT JOIN clients c ON oc.client_id = c.id
            WHERE oc.type_operation = 'ENCAISSEMENT_VENTE'
        `;

        const conditions = [];
        if (clientFilter) conditions.push(`oc.client_id = ${clientFilter}`);
        if (modeFilter) conditions.push(`oc.mode_paiement = '${modeFilter}'`);
        if (dateFilter) conditions.push(`oc.date_paiement >= '${dateFilter}'`);

        if (conditions.length > 0) {
            sql += ' AND ' + conditions.join(' AND ');
        }

        sql += ' ORDER BY oc.date_paiement DESC, oc.id DESC';

        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'operation_caisse',
            sql: sql
        })
        .done(function(response) {
            if (response.success) {
                populateTablePaiements(response.data);
            } else {
                showAlert('Erreur', 'Erreur lors du filtrage: ' + response.message, 'error');
            }
        })
        .fail(function() {
            showAlert('Erreur', 'Erreur lors du filtrage', 'error');
        });
    }

    function resetFilters() {
        $('#filterClient').val('');
        $('#filterModePaiement').val('');
        $('#filterDateFrom').val('');
        loadPaiementsVentes();
    }

    // Fonctions globales pour les actions des boutons
    window.viewPaiementDetails = function(paiementId) {
        const sql = `
            SELECT
                oc.*,
                ve.facture_numero as reference_vente,
                c.nom as nom_client,
                c.adresse as adresse_client
            FROM operation_caisse oc
            LEFT JOIN ventes_entete ve ON oc.vente_id = ve.id
            LEFT JOIN clients c ON oc.client_id = c.id
            WHERE oc.id = ${paiementId}
        `;

        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'operation_caisse',
            sql: sql
        })
        .done(function(response) {
            if (response.success && response.data.length > 0) {
                const paiement = response.data[0];
                showPaiementDetails(paiement);
            } else {
                showAlert('Erreur', 'Paiement non trouvé', 'error');
            }
        })
        .fail(function() {
            showAlert('Erreur', 'Erreur lors du chargement des détails', 'error');
        });
    };

    function showPaiementDetails(paiement) {
        const statutBadge = getStatutBadge(paiement.statut);
        const montantFormate = formatMontant(paiement.montant);

        const content = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Informations Générales</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Référence Vente:</strong></td><td>${paiement.reference_vente || 'N/A'}</td></tr>
                        <tr><td><strong>Client:</strong></td><td>${paiement.nom_client || 'N/A'}</td></tr>
                        <tr><td><strong>Date Paiement:</strong></td><td>${formatDate(paiement.date_paiement)}</td></tr>
                        <tr><td><strong>Mode Paiement:</strong></td><td>${paiement.mode_paiement || 'N/A'}</td></tr>
                        <tr><td><strong>Référence:</strong></td><td>${paiement.reference_paiement || 'N/A'}</td></tr>
                        <tr><td><strong>Montant:</strong></td><td>${montantFormate}</td></tr>
                        <tr><td><strong>Statut:</strong></td><td>${statutBadge}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Informations Système</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Effectué par:</strong></td><td>${paiement.effectue_par || 'N/A'}</td></tr>
                        <tr><td><strong>Créé par:</strong></td><td>${paiement.cree_par || 'N/A'}</td></tr>
                        <tr><td><strong>Date création:</strong></td><td>${formatDate(paiement.date_creation)}</td></tr>
                        <tr><td><strong>Commentaires:</strong></td><td>${paiement.commentaires || 'Aucun'}</td></tr>
                    </table>
                </div>
            </div>
        `;

        $('#detailsPaiementVenteContent').html(content);
        $('#modalDetailsPaiementVente').modal('show');
    }

    window.editPaiement = function(paiementId) {
        // Charger les données du paiement pour édition
        const sql = `
            SELECT
                oc.*,
                ve.facture_numero as reference_vente,
                c.nom as nom_client
            FROM operation_caisse oc
            LEFT JOIN ventes_entete ve ON oc.vente_id = ve.id
            LEFT JOIN clients c ON oc.client_id = c.id
            WHERE oc.id = ${paiementId}
        `;

        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'operation_caisse',
            sql: sql
        })
        .done(function(response) {
            if (response.success && response.data.length > 0) {
                const paiement = response.data[0];

                // Vérifier que le statut permet l'édition
                if (paiement.statut !== 'EN_ATTENTE') {
                    showAlert('Attention', 'Seuls les paiements en attente peuvent être modifiés', 'warning');
                    return;
                }

                // Pré-remplir le formulaire avec les données existantes
                populateEditForm(paiement);
                $('#modalPaiementVente .modal-title').html('<i class="fas fa-edit"></i> Modifier le Paiement');
                $('#btnSavePaiementVente').data('edit-id', paiementId);
                $('#modalPaiementVente').modal('show');
            } else {
                showAlert('Erreur', 'Paiement non trouvé', 'error');
            }
        })
        .fail(function() {
            showAlert('Erreur', 'Erreur lors du chargement du paiement', 'error');
        });
    };

    function populateEditForm(paiement) {
        // Remplir les champs du formulaire
        $('#venteSelect').val(paiement.vente_id);
        $('#modePaiementSelect').val(paiement.mode_paiement);
        $('#datePaiement').val(paiement.date_paiement);
        $('#referencePaiement').val(paiement.reference_paiement || '');
        $('#statutPaiement').val(paiement.statut);
        $('#commentairesPaiement').val(paiement.commentaires || '');

        // Déclencher le changement de mode de paiement pour afficher/masquer la référence
        toggleReferenceField();

        // Charger les produits de la vente
        if (paiement.vente_id) {
            currentVenteId = paiement.vente_id;
            currentClientId = paiement.client_id;
            loadProduitsForPaiement(paiement.vente_id);
        }
    }

    window.changeStatutPaiement = function(paiementId, currentStatut) {
        // Définir les statuts possibles et leurs transitions
        const statutOptions = {
            'EN_ATTENTE': ['VALIDE', 'ANNULE'],
            'VALIDE': ['ANNULE'],
            'ANNULE': ['EN_ATTENTE']
        };

        const availableStatuts = statutOptions[currentStatut] || [];

        if (availableStatuts.length === 0) {
            showAlert('Info', 'Aucun changement de statut possible pour ce paiement', 'info');
            return;
        }

        // Créer les options pour le select
        let options = '';
        availableStatuts.forEach(statut => {
            const label = {
                'EN_ATTENTE': 'En Attente',
                'VALIDE': 'Validé',
                'ANNULE': 'Annulé'
            }[statut];
            options += `<option value="${statut}">${label}</option>`;
        });

        Swal.fire({
            title: 'Changer le statut du paiement',
            html: `
                <div class="mb-3">
                    <label class="form-label">Nouveau statut :</label>
                    <select id="nouveauStatut" class="form-select">
                        ${options}
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Commentaire (optionnel) :</label>
                    <textarea id="commentaireStatut" class="form-control" rows="3" placeholder="Raison du changement de statut..."></textarea>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Changer le statut',
            cancelButtonText: 'Annuler',
            preConfirm: () => {
                const nouveauStatut = document.getElementById('nouveauStatut').value;
                const commentaire = document.getElementById('commentaireStatut').value;

                if (!nouveauStatut) {
                    Swal.showValidationMessage('Veuillez sélectionner un statut');
                    return false;
                }

                return { nouveauStatut, commentaire };
            }
        }).then((result) => {
            if (result.isConfirmed) {
                updateStatutPaiement(paiementId, result.value.nouveauStatut, result.value.commentaire);
            }
        });
    };

    function updateStatutPaiement(paiementId, nouveauStatut, commentaire) {
        const updateData = {
            statut: nouveauStatut,
            dernier_modif_par: currentUser
        };

        // Ajouter le commentaire s'il y en a un
        if (commentaire.trim()) {
            updateData.commentaires = commentaire;
        }

        $.post('../includes/traitement.php', {
            action: 'update',
            table: 'operation_caisse',
            id: paiementId,
            data: JSON.stringify(updateData)
        })
        .done(function(response) {
            if (response.success) {
                // Si le paiement passe à VALIDE, mettre à jour le statut de la vente à PAYE
                if (nouveauStatut === 'VALIDE') {
                    updateVenteStatusFromPaiement(paiementId);
                } else {
                    showAlert('Succès', 'Statut du paiement mis à jour avec succès', 'success');
                    loadPaiementsVentes(); // Recharger la liste
                }
            } else {
                showAlert('Erreur', 'Erreur lors de la mise à jour du statut: ' + response.message, 'error');
            }
        })
        .fail(function() {
            showAlert('Erreur', 'Erreur lors de la mise à jour du statut', 'error');
        });
    }

    function updateVenteStatusFromPaiement(paiementId) {
        // D'abord récupérer les informations du paiement pour obtenir l'ID de la vente
        const sql = `
            SELECT oc.vente_id, oc.montant, ve.facture_numero
            FROM operation_caisse oc
            LEFT JOIN ventes_entete ve ON oc.vente_id = ve.id
            WHERE oc.id = ${paiementId}
        `;

        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'operation_caisse',
            sql: sql
        })
        .done(function(response) {
            if (response.success && response.data.length > 0) {
                const paiement = response.data[0];
                const venteId = paiement.vente_id;
                const montantPaiement = paiement.montant;
                const factureNumero = paiement.facture_numero || `Vente #${venteId}`;

                // Récupérer les valeurs de conversion devise du formulaire
                const valeurEuro = parseFloat($('#valeurEUR').text().replace(',', '.').replace(/[^\d.-]/g, '')) || 0;
                const valeurAr = parseFloat($('#valeurAR').text().replace(/[^\d.-]/g, '')) || 0;
                const coursDevise = parseFloat($('#coursDevise').val()) || 0;

                // Préparer les données de mise à jour
                const updateData = {
                    statut: 'PAYE',
                    total_montant: montantPaiement,
                    dernier_modif_par: currentUser
                };

                // Ajouter les champs de devise s'ils sont disponibles
                if (valeurEuro > 0) {
                    updateData.valeur_euro = valeurEuro;
                }
                if (valeurAr > 0) {
                    updateData.valeur_ar = valeurAr;
                }
                if (coursDevise > 0) {
                    updateData.cours_devise = coursDevise;
                }

                // Mettre à jour le statut de la vente à PAYE avec les valeurs de devise
                $.post('../includes/traitement.php', {
                    action: 'update',
                    table: 'ventes_entete',
                    id: venteId,
                    data: JSON.stringify(updateData)
                })
                .done(function(updateResponse) {
                    if (updateResponse.success) {
                        showAlert('Succès',
                            `Paiement validé avec succès. La vente ${factureNumero} est maintenant marquée comme PAYÉE.`,
                            'success');
                        loadPaiementsVentes(); // Recharger la liste
                    } else {
                        showAlert('Avertissement',
                            'Paiement validé mais erreur lors de la mise à jour du statut de la vente: ' + updateResponse.message,
                            'warning');
                        loadPaiementsVentes(); // Recharger quand même la liste
                    }
                })
                .fail(function() {
                    showAlert('Avertissement',
                        'Paiement validé mais impossible de mettre à jour le statut de la vente',
                        'warning');
                    loadPaiementsVentes(); // Recharger quand même la liste
                });
            } else {
                showAlert('Erreur', 'Impossible de récupérer les informations du paiement', 'error');
                loadPaiementsVentes(); // Recharger quand même la liste
            }
        })
        .fail(function() {
            showAlert('Erreur', 'Erreur lors de la récupération des informations du paiement', 'error');
            loadPaiementsVentes(); // Recharger quand même la liste
        });
    }

    window.deletePaiement = function(paiementId) {
        Swal.fire({
            title: 'Confirmer la suppression',
            text: 'Êtes-vous sûr de vouloir supprimer ce paiement ?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler'
        }).then((result) => {
            if (result.isConfirmed) {
                // Utilise action: 'delete' au lieu de execute_sql
                $.post('../includes/traitement.php', {
                    action: 'delete',  // Utilise action: 'delete' au lieu de execute_sql
                    table: 'operation_caisse',
                    id: paiementId
                })
                .done(function(response) {
                    if (response.success) {
                        showAlert('Succès', 'Paiement supprimé avec succès', 'success');
                        loadPaiementsVentes();
                    } else {
                        showAlert('Erreur', 'Erreur lors de la suppression: ' + response.message, 'error');
                    }
                })
                .fail(function() {
                    showAlert('Erreur', 'Erreur lors de la suppression', 'error');
                });
            }
        });
    };

    function generateReceiptVente() {
        const selectedPaiements = [];
        $('.paiement-checkbox:checked').each(function() {
            selectedPaiements.push($(this).val());
        });

        if (selectedPaiements.length === 0) {
            showAlert('Attention', 'Veuillez sélectionner au moins un paiement', 'warning');
            return;
        }

        // TODO: Implémenter la génération de reçu PDF
        showAlert('Info', 'Génération de reçu en cours de développement', 'info');
    }

    // Fonctions utilitaires
    function getStatutBadge(statut) {
        const badges = {
            'EN_ATTENTE': '<span class="badge bg-warning">En Attente</span>',
            'VALIDE': '<span class="badge bg-success">Validé</span>',
            'ANNULE': '<span class="badge bg-danger">Annulé</span>'
        };
        return badges[statut] || '<span class="badge bg-secondary">Inconnu</span>';
    }

    function formatMontant(montant) {
        if (!montant || isNaN(montant)) return '0 Ar';
        return new Intl.NumberFormat('fr-FR', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(montant) + ' Ar';
    }

    function formatNumber(number, decimals = 0) {
        if (!number || isNaN(number)) return '0';
        return new Intl.NumberFormat('fr-FR', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    }

    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR');
    }

    function showAlert(title, message, type) {
        const icons = {
            'success': 'success',
            'error': 'error',
            'warning': 'warning',
            'info': 'info'
        };

        Swal.fire({
            title: title,
            text: message,
            icon: icons[type] || 'info',
            confirmButtonText: 'OK'
        });
    }

    // ===== FONCTIONS D'IMPRESSION =====

    window.printReceiptVente = function(paiementId) {
        // Ouvrir le reçu de vente dans un nouvel onglet
        const url = `../pages/facture_paiement_vente_pdf.php?id=${paiementId}`;
        window.open(url, '_blank');
    };

    function generateReceiptVente() {
        const selectedIds = getSelectedPaiementIds();
        if (selectedIds.length === 0) {
            showAlert('Erreur', 'Veuillez sélectionner au moins un paiement', 'warning');
            return;
        }
        // Générer les reçus un par un
        selectedIds.forEach(id => {
            printReceiptVente(id);
        });
    }

    function getSelectedPaiementIds() {
        const selectedIds = [];
        $('#tablePaiementsVentes tbody input[type="checkbox"]:checked').each(function() {
            const row = $(this).closest('tr');
            const rowData = tablePaiementsVentes.row(row).data();
            if (rowData && rowData.length > 0) {
                // L'ID est dans la première colonne (après la checkbox)
                const idCell = $(row).find('td').eq(1).text(); // Deuxième colonne (index 1)
                if (idCell && !isNaN(idCell)) {
                    selectedIds.push(parseInt(idCell));
                }
            }
        });
        return selectedIds;
    }
});

-- <PERSON>ript complet pour nettoyer les colonnes d'audit
-- Supprime les colonnes cree_par_id et dernier_modif_par_id
-- S'assure que toutes les tables utilisent cree_par et dernier_modif_par (VARCHAR)

-- ========================================
-- ÉTAPE 1: Supprimer les colonnes ID
-- ========================================

-- Table fournisseurs
ALTER TABLE `fournisseurs` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table producteurs
ALTER TABLE `producteurs` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table production_globale
ALTER TABLE `production_globale` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table production_mensuelle
ALTER TABLE `production_mensuelle` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- Table user_permissions
ALTER TABLE `user_permissions` 
DROP COLUMN IF EXISTS `cree_par_id`,
DROP COLUMN IF EXISTS `dernier_modif_par_id`;

-- ========================================
-- ÉTAPE 2: Ajouter les colonnes d'audit correctes
-- ========================================

-- Table unites
ALTER TABLE `unites` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table depot
ALTER TABLE `depot` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table forme
ALTER TABLE `forme` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table presentation
ALTER TABLE `presentation` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table produits_stock
ALTER TABLE `produits_stock` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table roles
ALTER TABLE `roles` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table permissions
ALTER TABLE `permissions` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table role_permissions
ALTER TABLE `role_permissions` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table user_permissions
ALTER TABLE `user_permissions` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table communes
ALTER TABLE `communes` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table cooperatives
ALTER TABLE `cooperatives` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table fournisseurs
ALTER TABLE `fournisseurs` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table producteurs
ALTER TABLE `producteurs` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table production_globale
ALTER TABLE `production_globale` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table production_mensuelle
ALTER TABLE `production_mensuelle` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table ventes_entete
ALTER TABLE `ventes_entete` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- Table ventes_details
ALTER TABLE `ventes_details` 
ADD COLUMN IF NOT EXISTS `date_creation` datetime NOT NULL DEFAULT current_timestamp(),
ADD COLUMN IF NOT EXISTS `cree_par` varchar(255) DEFAULT NULL,
ADD COLUMN IF NOT EXISTS `date_derniere_modif` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
ADD COLUMN IF NOT EXISTS `dernier_modif_par` varchar(255) DEFAULT NULL;

-- ========================================
-- ÉTAPE 3: Vérification finale
-- ========================================

-- Vérifier qu'il n'y a plus de colonnes ID
SELECT 
    'Colonnes ID restantes' as Status,
    TABLE_NAME,
    COLUMN_NAME
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'erp_cacao' 
    AND COLUMN_NAME IN ('cree_par_id', 'dernier_modif_par_id')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- Vérifier les colonnes d'audit finales (doivent être des VARCHAR)
SELECT 
    'Colonnes d\'audit finales' as Status,
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'erp_cacao' 
    AND COLUMN_NAME IN ('cree_par', 'dernier_modif_par')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- Résumé des tables avec colonnes d'audit
SELECT 
    'Résumé des tables' as Status,
    TABLE_NAME,
    COUNT(*) as NombreColonnesAudit
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'erp_cacao' 
    AND COLUMN_NAME IN ('date_creation', 'cree_par', 'date_derniere_modif', 'dernier_modif_par')
GROUP BY TABLE_NAME
ORDER BY TABLE_NAME;

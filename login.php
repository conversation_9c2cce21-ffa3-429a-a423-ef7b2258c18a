<?php
if (session_status() === PHP_SESSION_NONE) session_start();

// Rediriger si déjà connecté
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

$error_message = '';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - ERP Cacao</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/login.css">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="cacao-icon">
                    <i class="fas fa-seedling"></i>
                </div>
                <h1>Sambirano-SA</h1>
                <p>Gestion des Matières Premières et Traçabilité</p>
            </div>
            
            <div class="login-body">
                <?php if ($error_message): ?>
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" id="loginForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username" 
                               placeholder="Nom d'utilisateur" value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                        <label for="username">
                            <i class="fas fa-user me-2"></i>Nom d'utilisateur
                        </label>
                    </div>

                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="Mot de passe" required>
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>Mot de passe
                        </label>
                    </div>

                    <button type="submit" class="btn btn-login" id="loginBtn">
                        <span class="btn-text">
                            <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                        </span>
                        <span class="loading">
                            <i class="fas fa-spinner fa-spin me-2"></i>Connexion...
                        </span>
                    </button>
                </form>

                <!-- <div class="demo-accounts">
                    <h6><i class="fas fa-info-circle me-2"></i>Comptes de démonstration</h6>
                    <div class="demo-account">
                        <span><strong>admin</strong> - Administrateur</span>
                        <span class="text-muted">password</span>
                    </div>
                    <div class="demo-account">
                        <span><strong>denis</strong> - Gestion Achats</span>
                        <span class="text-muted">password</span>
                    </div>
                    <div class="demo-account">
                        <span><strong>joassin</strong> - Validation</span>
                        <span class="text-muted">password</span>
                    </div>
                    <div class="demo-account">
                        <span><strong>guy</strong> - Comptabilité</span>
                        <span class="text-muted">password</span>
                    </div>
                </div> -->
            </div>

            <div class="login-footer">
                <p>&copy; Sambirano-SA. Tous droits réservés.</p>
                <p class="mb-0">
                    <i class="fas fa-shield-alt me-1"></i>
                    Système sécurisé et traçable
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery pour AJAX simple -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(function(){
            const $form = $('#loginForm');
            const $btn = $('#loginBtn');
            const $alert = $('<div class="alert alert-danger d-none" role="alert"></div>').insertBefore($form);

            function setLoading(loading) {
                if (loading) {
                    $btn.addClass('loading').prop('disabled', true);
                } else {
                    $btn.removeClass('loading').prop('disabled', false);
                }
            }

            $form.on('submit', function(e){
                e.preventDefault();
                $alert.addClass('d-none').text('');

                const username = $.trim($('#username').val());
                const password = $('#password').val();
                if (!username || !password) {
                    $alert.removeClass('d-none').text('Veuillez remplir tous les champs');
                    return;
                }

                setLoading(true);

                $.ajax({
                    url: 'includes/auth.php',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        action: 'login',
                        username: username,
                        password: password
                    }
                }).done(function(resp){
                    console.log('>>> '+resp.success);
                    if (resp && resp.success) { 
                        // redirection côté client
                        window.location.href = resp.redirect;
                        console.log('>>> '+resp.success);
                    } else {
                        $alert.removeClass('d-none').text(resp.message || 'Identifiants incorrects');
                    }
                }).fail(function(jqXHR, textStatus){
                    $alert.removeClass('d-none').text('Erreur réseau ou serveur.');
                }).always(function(){
                    setLoading(false);
                });
            });

            // Animation des champs de saisie
            $('.form-control').on('focus', function(){ $(this).parent().addClass('focused'); });
            $('.form-control').on('blur', function(){ if (!this.value) $(this).parent().removeClass('focused'); });

            // Auto-focus sur le premier champ
            $('#username').focus();
        });
    </script>
</body>
</html>
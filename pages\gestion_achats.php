<?php
session_start();
require_once dirname(__DIR__) . '/config/config.php';
require_once CONFIG_PATH . '/database.php';
require_once INCLUDES_PATH . '/auth.php';
require_once INCLUDES_PATH . '/rbac.php';

// Vérification de l'authentification
$auth = new Auth();
$auth->checkAuth();

// Vérification des permissions
$rbac = new RBAC();
$rbac->requirePermission('achats.view');

$currentUser = $_SESSION['username'];
?>
<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Gestion des Achats</title>
    <!-- Bootstrap Cerulean -->
    <link href="https://cdn.jsdelivr.net/npm/bootswatch@5.3.2/dist/cerulean/bootstrap.min.css" rel="stylesheet">
    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <!-- CSS personnalisé -->
    <style>
        .badge {
            font-size: 0.75em;
            padding: 0.35em 0.65em;
        }
        .btn-sm i {
            font-size: 0.875em;
        }
        .table th {
            font-size: 0.875rem;
            font-weight: 600;
        }
        .table td {
            font-size: 0.875rem;
        }
        .btn-group .btn {
            margin-right: 0.25rem;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        .table td:last-child {
            white-space: nowrap;
            min-width: 200px;
        }
        .table td:last-child .btn {
            margin: 0 2px;
        }
        .text-center .badge {
            display: inline-block;
            min-width: 50px;
        }
        .status-badge {
            font-size: 0.7em;
            padding: 0.4em 0.8em;
        }
        .purchase-step {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        .purchase-step .step-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
            font-size: 0.8rem;
        }
        .step-completed {
            background-color: #28a745;
            color: white;
        }
        .step-current {
            background-color: #007bff;
            color: white;
        }
        .step-pending {
            background-color: #6c757d;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container-fluid p-3">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0">
                <i class="fas fa-shopping-cart"></i> Gestion des Achats
            </h6>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modalAchat" title="Nouvel achat">
                    <i class="fas fa-plus"></i> Nouvel Achat
                </button>
                <button class="btn btn-outline-danger" id="btnCancelAchats" title="Annuler les achats sélectionnés">
                    <i class="fas fa-times"></i> Annuler
                </button>
            </div>
        </div>

        <!-- Filtres -->
        <div class="card mb-3">
            <div class="card-header py-2">
                <h6 class="mb-0">
                    <i class="fas fa-filter"></i> Filtres
                </h6>
            </div>
            <div class="card-body py-2">
                <div class="row g-2">
                    <div class="col-md-2">
                        <label class="form-label small">Statut</label>
                        <select id="filterStatus" class="form-select form-select-sm">
                            <option value="">Tous les statuts</option>
                            <option value="SAISIE">Saisie</option>
                            <option value="LIVRE">Livré</option>
                            <option value="CONTROLE">Contrôlé</option>
                            <option value="A_PAYER">À payer</option>
                            <option value="PAYE">Payé</option>
                            <option value="ANNULE">Annulé</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small">Date début</label>
                        <input type="date" id="filterDateFrom" class="form-control form-control-sm">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small">Date fin</label>
                        <input type="date" id="filterDateTo" class="form-control form-control-sm">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label small">Fournisseur</label>
                        <select id="filterFournisseur" class="form-select form-select-sm">
                            <option value="">Tous les fournisseurs</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small">&nbsp;</label>
                        <div class="d-flex gap-1">
                            <button class="btn btn-outline-primary btn-sm" id="btnApplyFilters" title="Appliquer les filtres">
                                <i class="fas fa-search"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" id="btnResetFilters" title="Réinitialiser les filtres">
                                <i class="fas fa-undo"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-2">
            <!-- Table Achats -->
            <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header py-2">
                        <h6 class="mb-0">Liste des Achats</h6>
                    </div>
                    <div class="card-body p-2">
                        <div class="table-responsive">
                            <table id="tableAchats" class="table table-sm table-hover align-middle table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th><input type="checkbox" id="selectAll"></th>
                                        <th>Référence</th>
                                        <th>Fournisseur</th>
                                        <th>Date Achat</th>
                                        <th>Date Livr.</th>
                                        <th>Statut</th>
                                        <th>Mont. Total</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Données chargées dynamiquement -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistiques récapitulatives -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header py-2">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar"></i> Statistiques
                        </h6>
                    </div>
                    <div class="card-body py-2">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-primary" id="statTotalAchats">0</div>
                                    <small class="text-muted">Total Achats</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-success" id="statMontantTotal">0 Ar</div>
                                    <small class="text-muted">Montant Total</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-info" id="statFournisseurs">0</div>
                                    <small class="text-muted">Fournisseurs</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-warning" id="statEnAttente">0</div>
                                    <small class="text-muted">En Attente</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-danger" id="statAPayer">0</div>
                                    <small class="text-muted">À Payer</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-secondary" id="statPayes">0</div>
                                    <small class="text-muted">Payés</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Achat (Plein écran) -->
    <div class="modal fade" id="modalAchat" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalAchatTitle">
                        <i class="fas fa-shopping-cart"></i> Nouvel Achat
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- Informations générales -->
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Informations Générales</h6>
                                </div>
                                <div class="card-body">
                                    <form id="formAchatEntete">
                                        <div class="mb-3">
                                            <label class="form-label">Référence Achat</label>
                                            <input type="text" id="referenceAchat" class="form-control" readonly>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Fournisseur *</label>
                                            <select id="fournisseurAchat" class="form-select" required>
                                                <option value="">Sélectionner un fournisseur</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Date d'Achat</label>
                                            <input type="date" id="dateAchat" class="form-control">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Date de Livraison</label>
                                            <input type="date" id="dateLivraison" class="form-control">
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Détails des produits -->
                        <div class="col-md-9">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Détails des Produits</h6>
                                    <button class="btn btn-sm btn-success" id="btnAddProduitAchat" title="Ajouter un produit">
                                        <i class="fas fa-plus"></i> Ajouter
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table id="tableDetailsAchat" class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Produit</th>
                                                    <th>N° Lot</th>
                                                    <th>Poids Net <small class="text-muted" title="Calculé automatiquement : Poids Brut ÷ 3">(auto)</small></th>
                                                    <th>Poids Brut <small class="text-muted" title="Saisi manuellement ou calculé : Nbr. Sacs × 67 kg">(kg fraîche)</small></th>
                                                    <th>Ecart</th>
                                                    <th>Nbr. Sacs</th>
                                                    <th>Réduction</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Lignes ajoutées dynamiquement -->
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <!-- Totaux -->
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                                <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6>Résumé</h6>
                                                    <p class="mb-1">Montant HT: <span id="montantHT">0 Ar</span></p>
                                                    <p class="mb-1">Réduction: <span id="montantReduction">0 Ar</span></p>
                                                    <hr>
                                                    <p class="mb-0 fw-bold">Total TTC: <span id="montantTTC">0 Ar</span></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <button type="button" class="btn btn-primary" id="btnSaveAchat">
                        <i class="fas fa-save"></i> <span id="btnSaveText">Enregistrer l'Achat</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Détails d'Achat -->
    <div class="modal fade" id="modalDetailsAchat" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-eye"></i> Détails de l'Achat
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="detailsContent">
                        <!-- Contenu des détails chargé dynamiquement -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Saisie Petits Planteurs -->
    <div class="modal fade" id="modalPlanteurs" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-users"></i> Saisie des Petits Planteurs
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="planteursContent">
                        <!-- Contenu chargé dynamiquement -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Fermer
                    </button>
                    <button type="button" class="btn btn-success" id="btnSavePlanteurs">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Suivi d'Achat -->
    <div class="modal fade" id="modalSuiviAchat" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-route"></i> Suivi d'Achat
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="suiviContent">
                        <!-- Contenu du suivi chargé dynamiquement -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JS Bootstrap + DataTables + SweetAlert -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/gestion_achats.js?v=<?php echo date('YmdHis'); ?>"></script>

</body>

</html>

@import url("https://fonts.googleapis.com/css?family=Lato:400,700,400italic");
/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */

html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}
body {
  margin: 0;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}
audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
[hidden],
template {
  display: none;
}
a {
  background-color: transparent;
}
a:active,
a:hover {
  outline: 0;
}
abbr[title] {
  border-bottom: 1px dotted;
}
b,
strong {
  font-weight: bold;
}
dfn {
  font-style: italic;
}
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
mark {
  background: yellow;
  color: black;
}
small {
  font-size: 80%;
}
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
img {
  border: 0;
}
svg:not(:root) {
  overflow: hidden;
}
figure {
  margin: 1em 40px;
}
hr {
  box-sizing: content-box;
  height: 0;
}
pre {
  overflow: auto;
}
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}
button {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
button[disabled],
html input[disabled] {
  cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
input {
  line-height: normal;
}
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: textfield;
  box-sizing: content-box;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
fieldset {
  border: 1px solid silver;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
legend {
  border: 0;
  padding: 0;
}
textarea {
  overflow: auto;
}
optgroup {
  font-weight: bold;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
}
/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */

@media print {
  *,
  *:before,
  *:after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: "";
  }
  pre,
  blockquote {
    border: 1px solid #999999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  .navbar {
    display: none;
  }
  .btn > .caret,
  .dropup > .btn > .caret {
    border-top-color: black !important;
  }
  .label {
    border: 1px solid black;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table td,
  .table th {
    background-color: white !important;
  }
  .table-bordered th,
  .table-bordered td {
    border: 1px solid #dddddd !important;
  }
}
@font-face {
  font-family: 'Glyphicons Halflings';
  src: url('../fonts/glyphicons-halflings-regular.eot');
  src: url('../fonts/glyphicons-halflings-regular.eot?#iefix') format('embedded-opentype'), url('../fonts/glyphicons-halflings-regular.woff2') format('woff2'), url('../fonts/glyphicons-halflings-regular.woff') format('woff'), url('../fonts/glyphicons-halflings-regular.ttf') format('truetype'), url('../fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg');
}
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.glyphicon-asterisk:before {
  content: "\002a";
}
.glyphicon-plus:before {
  content: "\002b";
}
.glyphicon-euro:before,
.glyphicon-eur:before {
  content: "\20ac";
}
.glyphicon-minus:before {
  content: "\2212";
}
.glyphicon-cloud:before {
  content: "\2601";
}
.glyphicon-envelope:before {
  content: "\2709";
}
.glyphicon-pencil:before {
  content: "\270f";
}
.glyphicon-glass:before {
  content: "\e001";
}
.glyphicon-music:before {
  content: "\e002";
}
.glyphicon-search:before {
  content: "\e003";
}
.glyphicon-heart:before {
  content: "\e005";
}
.glyphicon-star:before {
  content: "\e006";
}
.glyphicon-star-empty:before {
  content: "\e007";
}
.glyphicon-user:before {
  content: "\e008";
}
.glyphicon-film:before {
  content: "\e009";
}
.glyphicon-th-large:before {
  content: "\e010";
}
.glyphicon-th:before {
  content: "\e011";
}
.glyphicon-th-list:before {
  content: "\e012";
}
.glyphicon-ok:before {
  content: "\e013";
}
.glyphicon-remove:before {
  content: "\e014";
}
.glyphicon-zoom-in:before {
  content: "\e015";
}
.glyphicon-zoom-out:before {
  content: "\e016";
}
.glyphicon-off:before {
  content: "\e017";
}
.glyphicon-signal:before {
  content: "\e018";
}
.glyphicon-cog:before {
  content: "\e019";
}
.glyphicon-trash:before {
  content: "\e020";
}
.glyphicon-home:before {
  content: "\e021";
}
.glyphicon-file:before {
  content: "\e022";
}
.glyphicon-time:before {
  content: "\e023";
}
.glyphicon-road:before {
  content: "\e024";
}
.glyphicon-download-alt:before {
  content: "\e025";
}
.glyphicon-download:before {
  content: "\e026";
}
.glyphicon-upload:before {
  content: "\e027";
}
.glyphicon-inbox:before {
  content: "\e028";
}
.glyphicon-play-circle:before {
  content: "\e029";
}
.glyphicon-repeat:before {
  content: "\e030";
}
.glyphicon-refresh:before {
  content: "\e031";
}
.glyphicon-list-alt:before {
  content: "\e032";
}
.glyphicon-lock:before {
  content: "\e033";
}
.glyphicon-flag:before {
  content: "\e034";
}
.glyphicon-headphones:before {
  content: "\e035";
}
.glyphicon-volume-off:before {
  content: "\e036";
}
.glyphicon-volume-down:before {
  content: "\e037";
}
.glyphicon-volume-up:before {
  content: "\e038";
}
.glyphicon-qrcode:before {
  content: "\e039";
}
.glyphicon-barcode:before {
  content: "\e040";
}
.glyphicon-tag:before {
  content: "\e041";
}
.glyphicon-tags:before {
  content: "\e042";
}
.glyphicon-book:before {
  content: "\e043";
}
.glyphicon-bookmark:before {
  content: "\e044";
}
.glyphicon-print:before {
  content: "\e045";
}
.glyphicon-camera:before {
  content: "\e046";
}
.glyphicon-font:before {
  content: "\e047";
}
.glyphicon-bold:before {
  content: "\e048";
}
.glyphicon-italic:before {
  content: "\e049";
}
.glyphicon-text-height:before {
  content: "\e050";
}
.glyphicon-text-width:before {
  content: "\e051";
}
.glyphicon-align-left:before {
  content: "\e052";
}
.glyphicon-align-center:before {
  content: "\e053";
}
.glyphicon-align-right:before {
  content: "\e054";
}
.glyphicon-align-justify:before {
  content: "\e055";
}
.glyphicon-list:before {
  content: "\e056";
}
.glyphicon-indent-left:before {
  content: "\e057";
}
.glyphicon-indent-right:before {
  content: "\e058";
}
.glyphicon-facetime-video:before {
  content: "\e059";
}
.glyphicon-picture:before {
  content: "\e060";
}
.glyphicon-map-marker:before {
  content: "\e062";
}
.glyphicon-adjust:before {
  content: "\e063";
}
.glyphicon-tint:before {
  content: "\e064";
}
.glyphicon-edit:before {
  content: "\e065";
}
.glyphicon-share:before {
  content: "\e066";
}
.glyphicon-check:before {
  content: "\e067";
}
.glyphicon-move:before {
  content: "\e068";
}
.glyphicon-step-backward:before {
  content: "\e069";
}
.glyphicon-fast-backward:before {
  content: "\e070";
}
.glyphicon-backward:before {
  content: "\e071";
}
.glyphicon-play:before {
  content: "\e072";
}
.glyphicon-pause:before {
  content: "\e073";
}
.glyphicon-stop:before {
  content: "\e074";
}
.glyphicon-forward:before {
  content: "\e075";
}
.glyphicon-fast-forward:before {
  content: "\e076";
}
.glyphicon-step-forward:before {
  content: "\e077";
}
.glyphicon-eject:before {
  content: "\e078";
}
.glyphicon-chevron-left:before {
  content: "\e079";
}
.glyphicon-chevron-right:before {
  content: "\e080";
}
.glyphicon-plus-sign:before {
  content: "\e081";
}
.glyphicon-minus-sign:before {
  content: "\e082";
}
.glyphicon-remove-sign:before {
  content: "\e083";
}
.glyphicon-ok-sign:before {
  content: "\e084";
}
.glyphicon-question-sign:before {
  content: "\e085";
}
.glyphicon-info-sign:before {
  content: "\e086";
}
.glyphicon-screenshot:before {
  content: "\e087";
}
.glyphicon-remove-circle:before {
  content: "\e088";
}
.glyphicon-ok-circle:before {
  content: "\e089";
}
.glyphicon-ban-circle:before {
  content: "\e090";
}
.glyphicon-arrow-left:before {
  content: "\e091";
}
.glyphicon-arrow-right:before {
  content: "\e092";
}
.glyphicon-arrow-up:before {
  content: "\e093";
}
.glyphicon-arrow-down:before {
  content: "\e094";
}
.glyphicon-share-alt:before {
  content: "\e095";
}
.glyphicon-resize-full:before {
  content: "\e096";
}
.glyphicon-resize-small:before {
  content: "\e097";
}
.glyphicon-exclamation-sign:before {
  content: "\e101";
}
.glyphicon-gift:before {
  content: "\e102";
}
.glyphicon-leaf:before {
  content: "\e103";
}
.glyphicon-fire:before {
  content: "\e104";
}
.glyphicon-eye-open:before {
  content: "\e105";
}
.glyphicon-eye-close:before {
  content: "\e106";
}
.glyphicon-warning-sign:before {
  content: "\e107";
}
.glyphicon-plane:before {
  content: "\e108";
}
.glyphicon-calendar:before {
  content: "\e109";
}
.glyphicon-random:before {
  content: "\e110";
}
.glyphicon-comment:before {
  content: "\e111";
}
.glyphicon-magnet:before {
  content: "\e112";
}
.glyphicon-chevron-up:before {
  content: "\e113";
}
.glyphicon-chevron-down:before {
  content: "\e114";
}
.glyphicon-retweet:before {
  content: "\e115";
}
.glyphicon-shopping-cart:before {
  content: "\e116";
}
.glyphicon-folder-close:before {
  content: "\e117";
}
.glyphicon-folder-open:before {
  content: "\e118";
}
.glyphicon-resize-vertical:before {
  content: "\e119";
}
.glyphicon-resize-horizontal:before {
  content: "\e120";
}
.glyphicon-hdd:before {
  content: "\e121";
}
.glyphicon-bullhorn:before {
  content: "\e122";
}
.glyphicon-bell:before {
  content: "\e123";
}
.glyphicon-certificate:before {
  content: "\e124";
}
.glyphicon-thumbs-up:before {
  content: "\e125";
}
.glyphicon-thumbs-down:before {
  content: "\e126";
}
.glyphicon-hand-right:before {
  content: "\e127";
}
.glyphicon-hand-left:before {
  content: "\e128";
}
.glyphicon-hand-up:before {
  content: "\e129";
}
.glyphicon-hand-down:before {
  content: "\e130";
}
.glyphicon-circle-arrow-right:before {
  content: "\e131";
}
.glyphicon-circle-arrow-left:before {
  content: "\e132";
}
.glyphicon-circle-arrow-up:before {
  content: "\e133";
}
.glyphicon-circle-arrow-down:before {
  content: "\e134";
}
.glyphicon-globe:before {
  content: "\e135";
}
.glyphicon-wrench:before {
  content: "\e136";
}
.glyphicon-tasks:before {
  content: "\e137";
}
.glyphicon-filter:before {
  content: "\e138";
}
.glyphicon-briefcase:before {
  content: "\e139";
}
.glyphicon-fullscreen:before {
  content: "\e140";
}
.glyphicon-dashboard:before {
  content: "\e141";
}
.glyphicon-paperclip:before {
  content: "\e142";
}
.glyphicon-heart-empty:before {
  content: "\e143";
}
.glyphicon-link:before {
  content: "\e144";
}
.glyphicon-phone:before {
  content: "\e145";
}
.glyphicon-pushpin:before {
  content: "\e146";
}
.glyphicon-usd:before {
  content: "\e148";
}
.glyphicon-gbp:before {
  content: "\e149";
}
.glyphicon-sort:before {
  content: "\e150";
}
.glyphicon-sort-by-alphabet:before {
  content: "\e151";
}
.glyphicon-sort-by-alphabet-alt:before {
  content: "\e152";
}
.glyphicon-sort-by-order:before {
  content: "\e153";
}
.glyphicon-sort-by-order-alt:before {
  content: "\e154";
}
.glyphicon-sort-by-attributes:before {
  content: "\e155";
}
.glyphicon-sort-by-attributes-alt:before {
  content: "\e156";
}
.glyphicon-unchecked:before {
  content: "\e157";
}
.glyphicon-expand:before {
  content: "\e158";
}
.glyphicon-collapse-down:before {
  content: "\e159";
}
.glyphicon-collapse-up:before {
  content: "\e160";
}
.glyphicon-log-in:before {
  content: "\e161";
}
.glyphicon-flash:before {
  content: "\e162";
}
.glyphicon-log-out:before {
  content: "\e163";
}
.glyphicon-new-window:before {
  content: "\e164";
}
.glyphicon-record:before {
  content: "\e165";
}
.glyphicon-save:before {
  content: "\e166";
}
.glyphicon-open:before {
  content: "\e167";
}
.glyphicon-saved:before {
  content: "\e168";
}
.glyphicon-import:before {
  content: "\e169";
}
.glyphicon-export:before {
  content: "\e170";
}
.glyphicon-send:before {
  content: "\e171";
}
.glyphicon-floppy-disk:before {
  content: "\e172";
}
.glyphicon-floppy-saved:before {
  content: "\e173";
}
.glyphicon-floppy-remove:before {
  content: "\e174";
}
.glyphicon-floppy-save:before {
  content: "\e175";
}
.glyphicon-floppy-open:before {
  content: "\e176";
}
.glyphicon-credit-card:before {
  content: "\e177";
}
.glyphicon-transfer:before {
  content: "\e178";
}
.glyphicon-cutlery:before {
  content: "\e179";
}
.glyphicon-header:before {
  content: "\e180";
}
.glyphicon-compressed:before {
  content: "\e181";
}
.glyphicon-earphone:before {
  content: "\e182";
}
.glyphicon-phone-alt:before {
  content: "\e183";
}
.glyphicon-tower:before {
  content: "\e184";
}
.glyphicon-stats:before {
  content: "\e185";
}
.glyphicon-sd-video:before {
  content: "\e186";
}
.glyphicon-hd-video:before {
  content: "\e187";
}
.glyphicon-subtitles:before {
  content: "\e188";
}
.glyphicon-sound-stereo:before {
  content: "\e189";
}
.glyphicon-sound-dolby:before {
  content: "\e190";
}
.glyphicon-sound-5-1:before {
  content: "\e191";
}
.glyphicon-sound-6-1:before {
  content: "\e192";
}
.glyphicon-sound-7-1:before {
  content: "\e193";
}
.glyphicon-copyright-mark:before {
  content: "\e194";
}
.glyphicon-registration-mark:before {
  content: "\e195";
}
.glyphicon-cloud-download:before {
  content: "\e197";
}
.glyphicon-cloud-upload:before {
  content: "\e198";
}
.glyphicon-tree-conifer:before {
  content: "\e199";
}
.glyphicon-tree-deciduous:before {
  content: "\e200";
}
.glyphicon-cd:before {
  content: "\e201";
}
.glyphicon-save-file:before {
  content: "\e202";
}
.glyphicon-open-file:before {
  content: "\e203";
}
.glyphicon-level-up:before {
  content: "\e204";
}
.glyphicon-copy:before {
  content: "\e205";
}
.glyphicon-paste:before {
  content: "\e206";
}
.glyphicon-alert:before {
  content: "\e209";
}
.glyphicon-equalizer:before {
  content: "\e210";
}
.glyphicon-king:before {
  content: "\e211";
}
.glyphicon-queen:before {
  content: "\e212";
}
.glyphicon-pawn:before {
  content: "\e213";
}
.glyphicon-bishop:before {
  content: "\e214";
}
.glyphicon-knight:before {
  content: "\e215";
}
.glyphicon-baby-formula:before {
  content: "\e216";
}
.glyphicon-tent:before {
  content: "\26fa";
}
.glyphicon-blackboard:before {
  content: "\e218";
}
.glyphicon-bed:before {
  content: "\e219";
}
.glyphicon-apple:before {
  content: "\f8ff";
}
.glyphicon-erase:before {
  content: "\e221";
}
.glyphicon-hourglass:before {
  content: "\231b";
}
.glyphicon-lamp:before {
  content: "\e223";
}
.glyphicon-duplicate:before {
  content: "\e224";
}
.glyphicon-piggy-bank:before {
  content: "\e225";
}
.glyphicon-scissors:before {
  content: "\e226";
}
.glyphicon-bitcoin:before {
  content: "\e227";
}
.glyphicon-btc:before {
  content: "\e227";
}
.glyphicon-xbt:before {
  content: "\e227";
}
.glyphicon-yen:before {
  content: "\00a5";
}
.glyphicon-jpy:before {
  content: "\00a5";
}
.glyphicon-ruble:before {
  content: "\20bd";
}
.glyphicon-rub:before {
  content: "\20bd";
}
.glyphicon-scale:before {
  content: "\e230";
}
.glyphicon-ice-lolly:before {
  content: "\e231";
}
.glyphicon-ice-lolly-tasted:before {
  content: "\e232";
}
.glyphicon-education:before {
  content: "\e233";
}
.glyphicon-option-horizontal:before {
  content: "\e234";
}
.glyphicon-option-vertical:before {
  content: "\e235";
}
.glyphicon-menu-hamburger:before {
  content: "\e236";
}
.glyphicon-modal-window:before {
  content: "\e237";
}
.glyphicon-oil:before {
  content: "\e238";
}
.glyphicon-grain:before {
  content: "\e239";
}
.glyphicon-sunglasses:before {
  content: "\e240";
}
.glyphicon-text-size:before {
  content: "\e241";
}
.glyphicon-text-color:before {
  content: "\e242";
}
.glyphicon-text-background:before {
  content: "\e243";
}
.glyphicon-object-align-top:before {
  content: "\e244";
}
.glyphicon-object-align-bottom:before {
  content: "\e245";
}
.glyphicon-object-align-horizontal:before {
  content: "\e246";
}
.glyphicon-object-align-left:before {
  content: "\e247";
}
.glyphicon-object-align-vertical:before {
  content: "\e248";
}
.glyphicon-object-align-right:before {
  content: "\e249";
}
.glyphicon-triangle-right:before {
  content: "\e250";
}
.glyphicon-triangle-left:before {
  content: "\e251";
}
.glyphicon-triangle-bottom:before {
  content: "\e252";
}
.glyphicon-triangle-top:before {
  content: "\e253";
}
.glyphicon-console:before {
  content: "\e254";
}
.glyphicon-superscript:before {
  content: "\e255";
}
.glyphicon-subscript:before {
  content: "\e256";
}
.glyphicon-menu-left:before {
  content: "\e257";
}
.glyphicon-menu-right:before {
  content: "\e258";
}
.glyphicon-menu-down:before {
  content: "\e259";
}
.glyphicon-menu-up:before {
  content: "\e260";
}
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
html {
  font-size: 10px;
  -webkit-tap-highlight-color: transparent;
}
body {
  font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 12px;
  line-height: 1.428571429;
  color: white;
  background-color: #222222;
}
input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
a {
  color: #0ce3ac;
  text-decoration: none;
}
a:hover,
a:focus {
  color: #0ce3ac;
  text-decoration: underline;
}
a:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
figure {
  margin: 0;
}
img {
  vertical-align: middle;
}
.img-responsive,
.thumbnail > img,
.thumbnail a > img,
.carousel-inner > .item > img,
.carousel-inner > .item > a > img {
  display: block;
  max-width: 100%;
  height: auto;
}
.img-rounded {
  border-radius: 6px;
}
.img-thumbnail {
  padding: 2px;
  line-height: 1.428571429;
  background-color: #222222;
  border: 1px solid #464545;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto;
}
.img-circle {
  border-radius: 50%;
}
hr {
  margin-top: 17px;
  margin-bottom: 17px;
  border: 0;
  border-top: 1px solid #464545;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}
[role="button"] {
  cursor: pointer;
}
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: 400;
  line-height: 1.1;
  color: inherit;
}
h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
.h1 small,
.h2 small,
.h3 small,
.h4 small,
.h5 small,
.h6 small,
h1 .small,
h2 .small,
h3 .small,
h4 .small,
h5 .small,
h6 .small,
.h1 .small,
.h2 .small,
.h3 .small,
.h4 .small,
.h5 .small,
.h6 .small {
  font-weight: normal;
  line-height: 1;
  color: #999999;
}
h1,
.h1,
h2,
.h2,
h3,
.h3 {
  margin-top: 17px;
  margin-bottom: 8.5px;
}
h1 small,
.h1 small,
h2 small,
.h2 small,
h3 small,
.h3 small,
h1 .small,
.h1 .small,
h2 .small,
.h2 .small,
h3 .small,
.h3 .small {
  font-size: 65%;
}
h4,
.h4,
h5,
.h5,
h6,
.h6 {
  margin-top: 8.5px;
  margin-bottom: 8.5px;
}
h4 small,
.h4 small,
h5 small,
.h5 small,
h6 small,
.h6 small,
h4 .small,
.h4 .small,
h5 .small,
.h5 .small,
h6 .small,
.h6 .small {
  font-size: 75%;
}
h1,
.h1 {
  font-size: 31px;
}
h2,
.h2 {
  font-size: 25px;
}
h3,
.h3 {
  font-size: 21px;
}
h4,
.h4 {
  font-size: 15px;
}
h5,
.h5 {
  font-size: 12px;
}
h6,
.h6 {
  font-size: 11px;
}
p {
  margin: 0 0 8.5px;
}
.lead {
  margin-bottom: 17px;
  font-size: 13px;
  font-weight: 300;
  line-height: 1.4;
}
@media (min-width: 768px) {
  .lead {
    font-size: 18px;
  }
}
small,
.small {
  font-size: 91%;
}
mark,
.mark {
  background-color: #f39c12;
  padding: 0.2em;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.text-justify {
  text-align: justify;
}
.text-nowrap {
  white-space: nowrap;
}
.text-lowercase {
  text-transform: lowercase;
}
.text-uppercase {
  text-transform: uppercase;
}
.text-capitalize {
  text-transform: capitalize;
}
.text-muted {
  color: #999999;
}
.text-primary {
  color: #375a7f;
}
a.text-primary:hover,
a.text-primary:focus {
  color: #28415b;
}
.text-success {
  color: white;
}
a.text-success:hover,
a.text-success:focus {
  color: #e6e6e6;
}
.text-info {
  color: white;
}
a.text-info:hover,
a.text-info:focus {
  color: #e6e6e6;
}
.text-warning {
  color: white;
}
a.text-warning:hover,
a.text-warning:focus {
  color: #e6e6e6;
}
.text-danger {
  color: white;
}
a.text-danger:hover,
a.text-danger:focus {
  color: #e6e6e6;
}
.bg-primary {
  color: white;
  background-color: #375a7f;
}
a.bg-primary:hover,
a.bg-primary:focus {
  background-color: #28415b;
}
.bg-success {
  background-color: #00bc8c;
}
a.bg-success:hover,
a.bg-success:focus {
  background-color: #008966;
}
.bg-info {
  background-color: #3498db;
}
a.bg-info:hover,
a.bg-info:focus {
  background-color: #217dbb;
}
.bg-warning {
  background-color: #f39c12;
}
a.bg-warning:hover,
a.bg-warning:focus {
  background-color: #c87f0a;
}
.bg-danger {
  background-color: #e74c3c;
}
a.bg-danger:hover,
a.bg-danger:focus {
  background-color: #d62c1a;
}
.page-header {
  padding-bottom: 7.5px;
  margin: 34px 0 17px;
  border-bottom: 1px solid transparent;
}
ul,
ol {
  margin-top: 0;
  margin-bottom: 8.5px;
}
ul ul,
ol ul,
ul ol,
ol ol {
  margin-bottom: 0;
}
.list-unstyled {
  padding-left: 0;
  list-style: none;
}
.list-inline {
  padding-left: 0;
  list-style: none;
  margin-left: -5px;
}
.list-inline > li {
  display: inline-block;
  padding-left: 5px;
  padding-right: 5px;
}
dl {
  margin-top: 0;
  margin-bottom: 17px;
}
dt,
dd {
  line-height: 1.428571429;
}
dt {
  font-weight: bold;
}
dd {
  margin-left: 0;
}
@media (min-width: 768px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .dl-horizontal dd {
    margin-left: 180px;
  }
}
abbr[title],
abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #999999;
}
.initialism {
  font-size: 90%;
  text-transform: uppercase;
}
blockquote {
  padding: 8.5px 17px;
  margin: 0 0 17px;
  font-size: 15px;
  border-left: 5px solid #464545;
}
blockquote p:last-child,
blockquote ul:last-child,
blockquote ol:last-child {
  margin-bottom: 0;
}
blockquote footer,
blockquote small,
blockquote .small {
  display: block;
  font-size: 80%;
  line-height: 1.428571429;
  color: #999999;
}
blockquote footer:before,
blockquote small:before,
blockquote .small:before {
  content: '\2014 \00A0';
}
.blockquote-reverse,
blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #464545;
  border-left: 0;
  text-align: right;
}
.blockquote-reverse footer:before,
blockquote.pull-right footer:before,
.blockquote-reverse small:before,
blockquote.pull-right small:before,
.blockquote-reverse .small:before,
blockquote.pull-right .small:before {
  content: '';
}
.blockquote-reverse footer:after,
blockquote.pull-right footer:after,
.blockquote-reverse small:after,
blockquote.pull-right small:after,
.blockquote-reverse .small:after,
blockquote.pull-right .small:after {
  content: '\00A0 \2014';
}
address {
  margin-bottom: 17px;
  font-style: normal;
  line-height: 1.428571429;
}
code,
kbd,
pre,
samp {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 4px;
}
kbd {
  padding: 2px 4px;
  font-size: 90%;
  color: white;
  background-color: #333333;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
}
kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: bold;
  box-shadow: none;
}
pre {
  display: block;
  padding: 8px;
  margin: 0 0 8.5px;
  font-size: 11px;
  line-height: 1.428571429;
  word-break: break-all;
  word-wrap: break-word;
  color: #303030;
  background-color: #ebebeb;
  border: 1px solid #cccccc;
  border-radius: 4px;
}
pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
@media (min-width: 768px) {
  .container {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .container {
    width: 1170px;
  }
}
.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
.row {
  margin-left: -15px;
  margin-right: -15px;
}
.col-xs-1,
.col-sm-1,
.col-md-1,
.col-lg-1,
.col-xs-2,
.col-sm-2,
.col-md-2,
.col-lg-2,
.col-xs-3,
.col-sm-3,
.col-md-3,
.col-lg-3,
.col-xs-4,
.col-sm-4,
.col-md-4,
.col-lg-4,
.col-xs-5,
.col-sm-5,
.col-md-5,
.col-lg-5,
.col-xs-6,
.col-sm-6,
.col-md-6,
.col-lg-6,
.col-xs-7,
.col-sm-7,
.col-md-7,
.col-lg-7,
.col-xs-8,
.col-sm-8,
.col-md-8,
.col-lg-8,
.col-xs-9,
.col-sm-9,
.col-md-9,
.col-lg-9,
.col-xs-10,
.col-sm-10,
.col-md-10,
.col-lg-10,
.col-xs-11,
.col-sm-11,
.col-md-11,
.col-lg-11,
.col-xs-12,
.col-sm-12,
.col-md-12,
.col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
}
.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
  float: left;
}
.col-xs-12 {
  width: 100%;
}
.col-xs-11 {
  width: 91.666666667%;
}
.col-xs-10 {
  width: 83.333333333%;
}
.col-xs-9 {
  width: 75%;
}
.col-xs-8 {
  width: 66.666666667%;
}
.col-xs-7 {
  width: 58.333333333%;
}
.col-xs-6 {
  width: 50%;
}
.col-xs-5 {
  width: 41.666666667%;
}
.col-xs-4 {
  width: 33.333333333%;
}
.col-xs-3 {
  width: 25%;
}
.col-xs-2 {
  width: 16.666666667%;
}
.col-xs-1 {
  width: 8.333333333%;
}
.col-xs-pull-12 {
  right: 100%;
}
.col-xs-pull-11 {
  right: 91.666666667%;
}
.col-xs-pull-10 {
  right: 83.333333333%;
}
.col-xs-pull-9 {
  right: 75%;
}
.col-xs-pull-8 {
  right: 66.666666667%;
}
.col-xs-pull-7 {
  right: 58.333333333%;
}
.col-xs-pull-6 {
  right: 50%;
}
.col-xs-pull-5 {
  right: 41.666666667%;
}
.col-xs-pull-4 {
  right: 33.333333333%;
}
.col-xs-pull-3 {
  right: 25%;
}
.col-xs-pull-2 {
  right: 16.666666667%;
}
.col-xs-pull-1 {
  right: 8.333333333%;
}
.col-xs-pull-0 {
  right: auto;
}
.col-xs-push-12 {
  left: 100%;
}
.col-xs-push-11 {
  left: 91.666666667%;
}
.col-xs-push-10 {
  left: 83.333333333%;
}
.col-xs-push-9 {
  left: 75%;
}
.col-xs-push-8 {
  left: 66.666666667%;
}
.col-xs-push-7 {
  left: 58.333333333%;
}
.col-xs-push-6 {
  left: 50%;
}
.col-xs-push-5 {
  left: 41.666666667%;
}
.col-xs-push-4 {
  left: 33.333333333%;
}
.col-xs-push-3 {
  left: 25%;
}
.col-xs-push-2 {
  left: 16.666666667%;
}
.col-xs-push-1 {
  left: 8.333333333%;
}
.col-xs-push-0 {
  left: auto;
}
.col-xs-offset-12 {
  margin-left: 100%;
}
.col-xs-offset-11 {
  margin-left: 91.666666667%;
}
.col-xs-offset-10 {
  margin-left: 83.333333333%;
}
.col-xs-offset-9 {
  margin-left: 75%;
}
.col-xs-offset-8 {
  margin-left: 66.666666667%;
}
.col-xs-offset-7 {
  margin-left: 58.333333333%;
}
.col-xs-offset-6 {
  margin-left: 50%;
}
.col-xs-offset-5 {
  margin-left: 41.666666667%;
}
.col-xs-offset-4 {
  margin-left: 33.333333333%;
}
.col-xs-offset-3 {
  margin-left: 25%;
}
.col-xs-offset-2 {
  margin-left: 16.666666667%;
}
.col-xs-offset-1 {
  margin-left: 8.333333333%;
}
.col-xs-offset-0 {
  margin-left: 0%;
}
@media (min-width: 768px) {
  .col-sm-1,
  .col-sm-2,
  .col-sm-3,
  .col-sm-4,
  .col-sm-5,
  .col-sm-6,
  .col-sm-7,
  .col-sm-8,
  .col-sm-9,
  .col-sm-10,
  .col-sm-11,
  .col-sm-12 {
    float: left;
  }
  .col-sm-12 {
    width: 100%;
  }
  .col-sm-11 {
    width: 91.666666667%;
  }
  .col-sm-10 {
    width: 83.333333333%;
  }
  .col-sm-9 {
    width: 75%;
  }
  .col-sm-8 {
    width: 66.666666667%;
  }
  .col-sm-7 {
    width: 58.333333333%;
  }
  .col-sm-6 {
    width: 50%;
  }
  .col-sm-5 {
    width: 41.666666667%;
  }
  .col-sm-4 {
    width: 33.333333333%;
  }
  .col-sm-3 {
    width: 25%;
  }
  .col-sm-2 {
    width: 16.666666667%;
  }
  .col-sm-1 {
    width: 8.333333333%;
  }
  .col-sm-pull-12 {
    right: 100%;
  }
  .col-sm-pull-11 {
    right: 91.666666667%;
  }
  .col-sm-pull-10 {
    right: 83.333333333%;
  }
  .col-sm-pull-9 {
    right: 75%;
  }
  .col-sm-pull-8 {
    right: 66.666666667%;
  }
  .col-sm-pull-7 {
    right: 58.333333333%;
  }
  .col-sm-pull-6 {
    right: 50%;
  }
  .col-sm-pull-5 {
    right: 41.666666667%;
  }
  .col-sm-pull-4 {
    right: 33.333333333%;
  }
  .col-sm-pull-3 {
    right: 25%;
  }
  .col-sm-pull-2 {
    right: 16.666666667%;
  }
  .col-sm-pull-1 {
    right: 8.333333333%;
  }
  .col-sm-pull-0 {
    right: auto;
  }
  .col-sm-push-12 {
    left: 100%;
  }
  .col-sm-push-11 {
    left: 91.666666667%;
  }
  .col-sm-push-10 {
    left: 83.333333333%;
  }
  .col-sm-push-9 {
    left: 75%;
  }
  .col-sm-push-8 {
    left: 66.666666667%;
  }
  .col-sm-push-7 {
    left: 58.333333333%;
  }
  .col-sm-push-6 {
    left: 50%;
  }
  .col-sm-push-5 {
    left: 41.666666667%;
  }
  .col-sm-push-4 {
    left: 33.333333333%;
  }
  .col-sm-push-3 {
    left: 25%;
  }
  .col-sm-push-2 {
    left: 16.666666667%;
  }
  .col-sm-push-1 {
    left: 8.333333333%;
  }
  .col-sm-push-0 {
    left: auto;
  }
  .col-sm-offset-12 {
    margin-left: 100%;
  }
  .col-sm-offset-11 {
    margin-left: 91.666666667%;
  }
  .col-sm-offset-10 {
    margin-left: 83.333333333%;
  }
  .col-sm-offset-9 {
    margin-left: 75%;
  }
  .col-sm-offset-8 {
    margin-left: 66.666666667%;
  }
  .col-sm-offset-7 {
    margin-left: 58.333333333%;
  }
  .col-sm-offset-6 {
    margin-left: 50%;
  }
  .col-sm-offset-5 {
    margin-left: 41.666666667%;
  }
  .col-sm-offset-4 {
    margin-left: 33.333333333%;
  }
  .col-sm-offset-3 {
    margin-left: 25%;
  }
  .col-sm-offset-2 {
    margin-left: 16.666666667%;
  }
  .col-sm-offset-1 {
    margin-left: 8.333333333%;
  }
  .col-sm-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 992px) {
  .col-md-1,
  .col-md-2,
  .col-md-3,
  .col-md-4,
  .col-md-5,
  .col-md-6,
  .col-md-7,
  .col-md-8,
  .col-md-9,
  .col-md-10,
  .col-md-11,
  .col-md-12 {
    float: left;
  }
  .col-md-12 {
    width: 100%;
  }
  .col-md-11 {
    width: 91.666666667%;
  }
  .col-md-10 {
    width: 83.333333333%;
  }
  .col-md-9 {
    width: 75%;
  }
  .col-md-8 {
    width: 66.666666667%;
  }
  .col-md-7 {
    width: 58.333333333%;
  }
  .col-md-6 {
    width: 50%;
  }
  .col-md-5 {
    width: 41.666666667%;
  }
  .col-md-4 {
    width: 33.333333333%;
  }
  .col-md-3 {
    width: 25%;
  }
  .col-md-2 {
    width: 16.666666667%;
  }
  .col-md-1 {
    width: 8.333333333%;
  }
  .col-md-pull-12 {
    right: 100%;
  }
  .col-md-pull-11 {
    right: 91.666666667%;
  }
  .col-md-pull-10 {
    right: 83.333333333%;
  }
  .col-md-pull-9 {
    right: 75%;
  }
  .col-md-pull-8 {
    right: 66.666666667%;
  }
  .col-md-pull-7 {
    right: 58.333333333%;
  }
  .col-md-pull-6 {
    right: 50%;
  }
  .col-md-pull-5 {
    right: 41.666666667%;
  }
  .col-md-pull-4 {
    right: 33.333333333%;
  }
  .col-md-pull-3 {
    right: 25%;
  }
  .col-md-pull-2 {
    right: 16.666666667%;
  }
  .col-md-pull-1 {
    right: 8.333333333%;
  }
  .col-md-pull-0 {
    right: auto;
  }
  .col-md-push-12 {
    left: 100%;
  }
  .col-md-push-11 {
    left: 91.666666667%;
  }
  .col-md-push-10 {
    left: 83.333333333%;
  }
  .col-md-push-9 {
    left: 75%;
  }
  .col-md-push-8 {
    left: 66.666666667%;
  }
  .col-md-push-7 {
    left: 58.333333333%;
  }
  .col-md-push-6 {
    left: 50%;
  }
  .col-md-push-5 {
    left: 41.666666667%;
  }
  .col-md-push-4 {
    left: 33.333333333%;
  }
  .col-md-push-3 {
    left: 25%;
  }
  .col-md-push-2 {
    left: 16.666666667%;
  }
  .col-md-push-1 {
    left: 8.333333333%;
  }
  .col-md-push-0 {
    left: auto;
  }
  .col-md-offset-12 {
    margin-left: 100%;
  }
  .col-md-offset-11 {
    margin-left: 91.666666667%;
  }
  .col-md-offset-10 {
    margin-left: 83.333333333%;
  }
  .col-md-offset-9 {
    margin-left: 75%;
  }
  .col-md-offset-8 {
    margin-left: 66.666666667%;
  }
  .col-md-offset-7 {
    margin-left: 58.333333333%;
  }
  .col-md-offset-6 {
    margin-left: 50%;
  }
  .col-md-offset-5 {
    margin-left: 41.666666667%;
  }
  .col-md-offset-4 {
    margin-left: 33.333333333%;
  }
  .col-md-offset-3 {
    margin-left: 25%;
  }
  .col-md-offset-2 {
    margin-left: 16.666666667%;
  }
  .col-md-offset-1 {
    margin-left: 8.333333333%;
  }
  .col-md-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 1200px) {
  .col-lg-1,
  .col-lg-2,
  .col-lg-3,
  .col-lg-4,
  .col-lg-5,
  .col-lg-6,
  .col-lg-7,
  .col-lg-8,
  .col-lg-9,
  .col-lg-10,
  .col-lg-11,
  .col-lg-12 {
    float: left;
  }
  .col-lg-12 {
    width: 100%;
  }
  .col-lg-11 {
    width: 91.666666667%;
  }
  .col-lg-10 {
    width: 83.333333333%;
  }
  .col-lg-9 {
    width: 75%;
  }
  .col-lg-8 {
    width: 66.666666667%;
  }
  .col-lg-7 {
    width: 58.333333333%;
  }
  .col-lg-6 {
    width: 50%;
  }
  .col-lg-5 {
    width: 41.666666667%;
  }
  .col-lg-4 {
    width: 33.333333333%;
  }
  .col-lg-3 {
    width: 25%;
  }
  .col-lg-2 {
    width: 16.666666667%;
  }
  .col-lg-1 {
    width: 8.333333333%;
  }
  .col-lg-pull-12 {
    right: 100%;
  }
  .col-lg-pull-11 {
    right: 91.666666667%;
  }
  .col-lg-pull-10 {
    right: 83.333333333%;
  }
  .col-lg-pull-9 {
    right: 75%;
  }
  .col-lg-pull-8 {
    right: 66.666666667%;
  }
  .col-lg-pull-7 {
    right: 58.333333333%;
  }
  .col-lg-pull-6 {
    right: 50%;
  }
  .col-lg-pull-5 {
    right: 41.666666667%;
  }
  .col-lg-pull-4 {
    right: 33.333333333%;
  }
  .col-lg-pull-3 {
    right: 25%;
  }
  .col-lg-pull-2 {
    right: 16.666666667%;
  }
  .col-lg-pull-1 {
    right: 8.333333333%;
  }
  .col-lg-pull-0 {
    right: auto;
  }
  .col-lg-push-12 {
    left: 100%;
  }
  .col-lg-push-11 {
    left: 91.666666667%;
  }
  .col-lg-push-10 {
    left: 83.333333333%;
  }
  .col-lg-push-9 {
    left: 75%;
  }
  .col-lg-push-8 {
    left: 66.666666667%;
  }
  .col-lg-push-7 {
    left: 58.333333333%;
  }
  .col-lg-push-6 {
    left: 50%;
  }
  .col-lg-push-5 {
    left: 41.666666667%;
  }
  .col-lg-push-4 {
    left: 33.333333333%;
  }
  .col-lg-push-3 {
    left: 25%;
  }
  .col-lg-push-2 {
    left: 16.666666667%;
  }
  .col-lg-push-1 {
    left: 8.333333333%;
  }
  .col-lg-push-0 {
    left: auto;
  }
  .col-lg-offset-12 {
    margin-left: 100%;
  }
  .col-lg-offset-11 {
    margin-left: 91.666666667%;
  }
  .col-lg-offset-10 {
    margin-left: 83.333333333%;
  }
  .col-lg-offset-9 {
    margin-left: 75%;
  }
  .col-lg-offset-8 {
    margin-left: 66.666666667%;
  }
  .col-lg-offset-7 {
    margin-left: 58.333333333%;
  }
  .col-lg-offset-6 {
    margin-left: 50%;
  }
  .col-lg-offset-5 {
    margin-left: 41.666666667%;
  }
  .col-lg-offset-4 {
    margin-left: 33.333333333%;
  }
  .col-lg-offset-3 {
    margin-left: 25%;
  }
  .col-lg-offset-2 {
    margin-left: 16.666666667%;
  }
  .col-lg-offset-1 {
    margin-left: 8.333333333%;
  }
  .col-lg-offset-0 {
    margin-left: 0%;
  }
}
table {
  background-color: transparent;
}
caption {
  padding-top: 3px;
  padding-bottom: 3px;
  color: #999999;
  text-align: left;
}
th {
  text-align: left;
}
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 17px;
}
.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 3px;
  line-height: 1.428571429;
  vertical-align: top;
  border-top: 1px solid #464545;
}
.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #464545;
}
.table > caption + thead > tr:first-child > th,
.table > colgroup + thead > tr:first-child > th,
.table > thead:first-child > tr:first-child > th,
.table > caption + thead > tr:first-child > td,
.table > colgroup + thead > tr:first-child > td,
.table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.table > tbody + tbody {
  border-top: 2px solid #464545;
}
.table .table {
  background-color: #222222;
}
.table-condensed > thead > tr > th,
.table-condensed > tbody > tr > th,
.table-condensed > tfoot > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > tbody > tr > td,
.table-condensed > tfoot > tr > td {
  padding: 1px;
}
.table-bordered {
  border: 1px solid #464545;
}
.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #464545;
}
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
  border-bottom-width: 2px;
}
.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #3d3d3d;
}
.table-hover > tbody > tr:hover {
  background-color: #464545;
}
table col[class*="col-"] {
  position: static;
  float: none;
  display: table-column;
}
table td[class*="col-"],
table th[class*="col-"] {
  position: static;
  float: none;
  display: table-cell;
}
.table > thead > tr > td.active,
.table > tbody > tr > td.active,
.table > tfoot > tr > td.active,
.table > thead > tr > th.active,
.table > tbody > tr > th.active,
.table > tfoot > tr > th.active,
.table > thead > tr.active > td,
.table > tbody > tr.active > td,
.table > tfoot > tr.active > td,
.table > thead > tr.active > th,
.table > tbody > tr.active > th,
.table > tfoot > tr.active > th {
  background-color: #464545;
}
.table-hover > tbody > tr > td.active:hover,
.table-hover > tbody > tr > th.active:hover,
.table-hover > tbody > tr.active:hover > td,
.table-hover > tbody > tr:hover > .active,
.table-hover > tbody > tr.active:hover > th {
  background-color: #393838;
}
.table > thead > tr > td.success,
.table > tbody > tr > td.success,
.table > tfoot > tr > td.success,
.table > thead > tr > th.success,
.table > tbody > tr > th.success,
.table > tfoot > tr > th.success,
.table > thead > tr.success > td,
.table > tbody > tr.success > td,
.table > tfoot > tr.success > td,
.table > thead > tr.success > th,
.table > tbody > tr.success > th,
.table > tfoot > tr.success > th {
  background-color: #00bc8c;
}
.table-hover > tbody > tr > td.success:hover,
.table-hover > tbody > tr > th.success:hover,
.table-hover > tbody > tr.success:hover > td,
.table-hover > tbody > tr:hover > .success,
.table-hover > tbody > tr.success:hover > th {
  background-color: #00a379;
}
.table > thead > tr > td.info,
.table > tbody > tr > td.info,
.table > tfoot > tr > td.info,
.table > thead > tr > th.info,
.table > tbody > tr > th.info,
.table > tfoot > tr > th.info,
.table > thead > tr.info > td,
.table > tbody > tr.info > td,
.table > tfoot > tr.info > td,
.table > thead > tr.info > th,
.table > tbody > tr.info > th,
.table > tfoot > tr.info > th {
  background-color: #3498db;
}
.table-hover > tbody > tr > td.info:hover,
.table-hover > tbody > tr > th.info:hover,
.table-hover > tbody > tr.info:hover > td,
.table-hover > tbody > tr:hover > .info,
.table-hover > tbody > tr.info:hover > th {
  background-color: #258cd1;
}
.table > thead > tr > td.warning,
.table > tbody > tr > td.warning,
.table > tfoot > tr > td.warning,
.table > thead > tr > th.warning,
.table > tbody > tr > th.warning,
.table > tfoot > tr > th.warning,
.table > thead > tr.warning > td,
.table > tbody > tr.warning > td,
.table > tfoot > tr.warning > td,
.table > thead > tr.warning > th,
.table > tbody > tr.warning > th,
.table > tfoot > tr.warning > th {
  background-color: #f39c12;
}
.table-hover > tbody > tr > td.warning:hover,
.table-hover > tbody > tr > th.warning:hover,
.table-hover > tbody > tr.warning:hover > td,
.table-hover > tbody > tr:hover > .warning,
.table-hover > tbody > tr.warning:hover > th {
  background-color: #e08e0b;
}
.table > thead > tr > td.danger,
.table > tbody > tr > td.danger,
.table > tfoot > tr > td.danger,
.table > thead > tr > th.danger,
.table > tbody > tr > th.danger,
.table > tfoot > tr > th.danger,
.table > thead > tr.danger > td,
.table > tbody > tr.danger > td,
.table > tfoot > tr.danger > td,
.table > thead > tr.danger > th,
.table > tbody > tr.danger > th,
.table > tfoot > tr.danger > th {
  background-color: #e74c3c;
}
.table-hover > tbody > tr > td.danger:hover,
.table-hover > tbody > tr > th.danger:hover,
.table-hover > tbody > tr.danger:hover > td,
.table-hover > tbody > tr:hover > .danger,
.table-hover > tbody > tr.danger:hover > th {
  background-color: #e43725;
}
.table-responsive {
  overflow-x: auto;
  min-height: 0.01%;
}
@media screen and (max-width: 767px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 12.75px;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #464545;
  }
  .table-responsive > .table {
    margin-bottom: 0;
  }
  .table-responsive > .table > thead > tr > th,
  .table-responsive > .table > tbody > tr > th,
  .table-responsive > .table > tfoot > tr > th,
  .table-responsive > .table > thead > tr > td,
  .table-responsive > .table > tbody > tr > td,
  .table-responsive > .table > tfoot > tr > td {
    white-space: nowrap;
  }
  .table-responsive > .table-bordered {
    border: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:first-child,
  .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .table-responsive > .table-bordered > thead > tr > td:first-child,
  .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:last-child,
  .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .table-responsive > .table-bordered > thead > tr > td:last-child,
  .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
  }
  .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .table-responsive > .table-bordered > tfoot > tr:last-child > th,
  .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0;
  }
}
.text-center th {
  text-align: center;
}
.table-vcenter td,
.table-vcenter th {
  vertical-align: middle;
}
.table thead tr th.sortable {
  cursor: pointer;
}
.table tbody tr td.line-number {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  min-width: 0;
}
legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 17px;
  font-size: 18px;
  line-height: inherit;
  color: white;
  border: 0;
  border-bottom: 1px solid transparent;
}
label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: bold;
}
input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  line-height: normal;
}
input[type="file"] {
  display: block;
}
input[type="range"] {
  display: block;
  width: 100%;
}
select[multiple],
select[size] {
  height: auto;
}
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
output {
  display: block;
  padding-top: 5px;
  font-size: 12px;
  line-height: 1.428571429;
  color: #464545;
}
.form-control {
  display: block;
  width: 100%;
  height: 29px;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1.428571429;
  color: #464545;
  background-color: white;
  background-image: none;
  border: 1px solid #f1f1f1;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.form-control:focus {
  border-color: white;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(255, 255, 255, 0.6);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(255, 255, 255, 0.6);
}
.form-control::-moz-placeholder {
  color: #999999;
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: #999999;
}
.form-control::-webkit-input-placeholder {
  color: #999999;
}
.form-control::-ms-expand {
  border: 0;
  background-color: transparent;
}
.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
  background-color: #ebebeb;
  opacity: 1;
}
.form-control[disabled],
fieldset[disabled] .form-control {
  cursor: not-allowed;
}
textarea.form-control {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: none;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="date"].form-control,
  input[type="time"].form-control,
  input[type="datetime-local"].form-control,
  input[type="month"].form-control {
    line-height: 29px;
  }
  input[type="date"].input-sm,
  input[type="time"].input-sm,
  input[type="datetime-local"].input-sm,
  input[type="month"].input-sm,
  .input-group-sm input[type="date"],
  .input-group-sm input[type="time"],
  .input-group-sm input[type="datetime-local"],
  .input-group-sm input[type="month"] {
    line-height: 24px;
  }
  input[type="date"].input-lg,
  input[type="time"].input-lg,
  input[type="datetime-local"].input-lg,
  input[type="month"].input-lg,
  .input-group-lg input[type="date"],
  .input-group-lg input[type="time"],
  .input-group-lg input[type="datetime-local"],
  .input-group-lg input[type="month"] {
    line-height: 36px;
  }
}
.form-group {
  margin-bottom: 10px;
}
.radio,
.checkbox-group,
.checkbox {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
}
.radio label,
.checkbox-group label,
.checkbox label {
  min-height: 17px;
  padding-left: 20px;
  margin-bottom: 0;
  font-weight: normal;
  cursor: pointer;
}
.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-left: -20px;
  margin-top: 4px \9;
}
.radio + .radio,
.checkbox + .checkbox {
  margin-top: -5px;
}
.radio-inline,
.checkbox-inline {
  position: relative;
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer;
}
.radio-inline + .radio-inline,
.checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: 10px;
}
input[type="radio"][disabled],
input[type="checkbox"][disabled],
input[type="radio"].disabled,
input[type="checkbox"].disabled,
fieldset[disabled] input[type="radio"],
fieldset[disabled] input[type="checkbox"] {
  cursor: not-allowed;
}
.radio-inline.disabled,
.checkbox-inline.disabled,
fieldset[disabled] .radio-inline,
fieldset[disabled] .checkbox-inline {
  cursor: not-allowed;
}
.radio.disabled label,
.checkbox.disabled label,
fieldset[disabled] .radio label,
fieldset[disabled] .checkbox label {
  cursor: not-allowed;
}
.form-control-static {
  padding-top: 5px;
  padding-bottom: 5px;
  margin-bottom: 0;
  min-height: 29px;
}
.form-control-static.input-lg,
.form-control-static.input-sm {
  padding-left: 0;
  padding-right: 0;
}
.input-sm {
  height: 24px;
  padding: 2px 5px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 3px;
}
select.input-sm {
  height: 24px;
  line-height: 24px;
}
textarea.input-sm,
select[multiple].input-sm {
  height: auto;
}
.form-group-sm .form-control {
  height: 24px;
  padding: 2px 5px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 3px;
}
.form-group-sm select.form-control {
  height: 24px;
  line-height: 24px;
}
.form-group-sm textarea.form-control,
.form-group-sm select[multiple].form-control {
  height: auto;
}
.form-group-sm .form-control-static {
  height: 24px;
  min-height: 28px;
  padding: 3px 5px;
  font-size: 11px;
  line-height: 1.5;
}
.input-lg {
  height: 36px;
  padding: 6px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 6px;
}
select.input-lg {
  height: 36px;
  line-height: 36px;
}
textarea.input-lg,
select[multiple].input-lg {
  height: auto;
}
.form-group-lg .form-control {
  height: 36px;
  padding: 6px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.form-group-lg select.form-control {
  height: 36px;
  line-height: 36px;
}
.form-group-lg textarea.form-control,
.form-group-lg select[multiple].form-control {
  height: auto;
}
.form-group-lg .form-control-static {
  height: 36px;
  min-height: 32px;
  padding: 7px 10px;
  font-size: 15px;
  line-height: 1.3333333;
}
.has-feedback {
  position: relative;
}
.has-feedback .form-control {
  padding-right: 36.25px;
}
.form-control-feedback {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  display: block;
  width: 29px;
  height: 29px;
  line-height: 29px;
  text-align: center;
  pointer-events: none;
}
.input-lg + .form-control-feedback,
.input-group-lg + .form-control-feedback,
.form-group-lg .form-control + .form-control-feedback {
  width: 36px;
  height: 36px;
  line-height: 36px;
}
.input-sm + .form-control-feedback,
.input-group-sm + .form-control-feedback,
.form-group-sm .form-control + .form-control-feedback {
  width: 24px;
  height: 24px;
  line-height: 24px;
}
.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline,
.has-success.radio label,
.has-success.checkbox label,
.has-success.radio-inline label,
.has-success.checkbox-inline label {
  color: white;
}
.has-success .form-control {
  border-color: white;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-success .form-control:focus {
  border-color: #e6e6e6;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
}
.has-success .input-group-addon {
  color: white;
  border-color: white;
  background-color: #00bc8c;
}
.has-success .form-control-feedback {
  color: white;
}
.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline,
.has-warning.radio label,
.has-warning.checkbox label,
.has-warning.radio-inline label,
.has-warning.checkbox-inline label {
  color: white;
}
.has-warning .form-control {
  border-color: white;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-warning .form-control:focus {
  border-color: #e6e6e6;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
}
.has-warning .input-group-addon {
  color: white;
  border-color: white;
  background-color: #f39c12;
}
.has-warning .form-control-feedback {
  color: white;
}
.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline,
.has-error.radio label,
.has-error.checkbox label,
.has-error.radio-inline label,
.has-error.checkbox-inline label {
  color: white;
}
.has-error .form-control {
  border-color: white;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-error .form-control:focus {
  border-color: #e6e6e6;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
}
.has-error .input-group-addon {
  color: white;
  border-color: white;
  background-color: #e74c3c;
}
.has-error .form-control-feedback {
  color: white;
}
.has-feedback label ~ .form-control-feedback {
  top: 22px;
}
.has-feedback label.sr-only ~ .form-control-feedback {
  top: 0;
}
.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: white;
}
@media (min-width: 768px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-static {
    display: inline-block;
  }
  .form-inline .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .form-inline .input-group .input-group-addon,
  .form-inline .input-group .input-group-btn,
  .form-inline .input-group .form-control {
    width: auto;
  }
  .form-inline .input-group > .form-control {
    width: 100%;
  }
  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio,
  .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio label,
  .form-inline .checkbox label {
    padding-left: 0;
  }
  .form-inline .radio input[type="radio"],
  .form-inline .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0;
  }
  .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}
.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 5px;
}
.form-horizontal .radio,
.form-horizontal .checkbox {
  min-height: 22px;
}
.form-horizontal .form-group {
  margin-left: -15px;
  margin-right: -15px;
}
@media (min-width: 768px) {
  .form-horizontal .control-label {
    text-align: right;
    margin-bottom: 0;
    padding-top: 5px;
  }
}
.form-horizontal .has-feedback .form-control-feedback {
  right: 15px;
}
@media (min-width: 768px) {
  .form-horizontal .form-group-lg .control-label {
    padding-top: 7px;
    font-size: 15px;
  }
}
@media (min-width: 768px) {
  .form-horizontal .form-group-sm .control-label {
    padding-top: 3px;
    font-size: 11px;
  }
}
.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1.428571429;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn.active.focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.btn:hover,
.btn:focus,
.btn.focus {
  color: white;
  text-decoration: none;
}
.btn:active,
.btn.active {
  outline: 0;
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
  cursor: not-allowed;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.btn.disabled,
fieldset[disabled] a.btn {
  pointer-events: none;
}
.btn-default {
  color: white;
  background-color: #464545;
  border-color: #464545;
}
.btn-default:focus,
.btn-default.focus {
  color: white;
  background-color: #2c2c2c;
  border-color: #060606;
}
.btn-default:hover {
  color: white;
  background-color: #2c2c2c;
  border-color: #272727;
}
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  color: white;
  background-color: #2c2c2c;
  border-color: #272727;
}
.btn-default:active:hover,
.btn-default.active:hover,
.open > .dropdown-toggle.btn-default:hover,
.btn-default:active:focus,
.btn-default.active:focus,
.open > .dropdown-toggle.btn-default:focus,
.btn-default:active.focus,
.btn-default.active.focus,
.open > .dropdown-toggle.btn-default.focus {
  color: white;
  background-color: #1a1a1a;
  border-color: #060606;
}
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  background-image: none;
}
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled.focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default.focus {
  background-color: #464545;
  border-color: #464545;
}
.btn-default .badge {
  color: #464545;
  background-color: white;
}
.btn-primary {
  color: white;
  background-color: #375a7f;
  border-color: #375a7f;
}
.btn-primary:focus,
.btn-primary.focus {
  color: white;
  background-color: #28415b;
  border-color: #101b26;
}
.btn-primary:hover {
  color: white;
  background-color: #28415b;
  border-color: #253c54;
}
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  color: white;
  background-color: #28415b;
  border-color: #253c54;
}
.btn-primary:active:hover,
.btn-primary.active:hover,
.open > .dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open > .dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open > .dropdown-toggle.btn-primary.focus {
  color: white;
  background-color: #1d2f43;
  border-color: #101b26;
}
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  background-image: none;
}
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus {
  background-color: #375a7f;
  border-color: #375a7f;
}
.btn-primary .badge {
  color: #375a7f;
  background-color: white;
}
.btn-success {
  color: white;
  background-color: #00bc8c;
  border-color: #00bc8c;
}
.btn-success:focus,
.btn-success.focus {
  color: white;
  background-color: #008966;
  border-color: #003d2d;
}
.btn-success:hover {
  color: white;
  background-color: #008966;
  border-color: #007f5e;
}
.btn-success:active,
.btn-success.active,
.open > .dropdown-toggle.btn-success {
  color: white;
  background-color: #008966;
  border-color: #007f5e;
}
.btn-success:active:hover,
.btn-success.active:hover,
.open > .dropdown-toggle.btn-success:hover,
.btn-success:active:focus,
.btn-success.active:focus,
.open > .dropdown-toggle.btn-success:focus,
.btn-success:active.focus,
.btn-success.active.focus,
.open > .dropdown-toggle.btn-success.focus {
  color: white;
  background-color: #00654b;
  border-color: #003d2d;
}
.btn-success:active,
.btn-success.active,
.open > .dropdown-toggle.btn-success {
  background-image: none;
}
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled.focus,
.btn-success[disabled].focus,
fieldset[disabled] .btn-success.focus {
  background-color: #00bc8c;
  border-color: #00bc8c;
}
.btn-success .badge {
  color: #00bc8c;
  background-color: white;
}
.btn-info {
  color: white;
  background-color: #3498db;
  border-color: #3498db;
}
.btn-info:focus,
.btn-info.focus {
  color: white;
  background-color: #217dbb;
  border-color: #16527a;
}
.btn-info:hover {
  color: white;
  background-color: #217dbb;
  border-color: #2077b2;
}
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info {
  color: white;
  background-color: #217dbb;
  border-color: #2077b2;
}
.btn-info:active:hover,
.btn-info.active:hover,
.open > .dropdown-toggle.btn-info:hover,
.btn-info:active:focus,
.btn-info.active:focus,
.open > .dropdown-toggle.btn-info:focus,
.btn-info:active.focus,
.btn-info.active.focus,
.open > .dropdown-toggle.btn-info.focus {
  color: white;
  background-color: #1c699d;
  border-color: #16527a;
}
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info {
  background-image: none;
}
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled.focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info.focus {
  background-color: #3498db;
  border-color: #3498db;
}
.btn-info .badge {
  color: #3498db;
  background-color: white;
}
.btn-warning {
  color: white;
  background-color: #f39c12;
  border-color: #f39c12;
}
.btn-warning:focus,
.btn-warning.focus {
  color: white;
  background-color: #c87f0a;
  border-color: #7f5006;
}
.btn-warning:hover {
  color: white;
  background-color: #c87f0a;
  border-color: #be780a;
}
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning {
  color: white;
  background-color: #c87f0a;
  border-color: #be780a;
}
.btn-warning:active:hover,
.btn-warning.active:hover,
.open > .dropdown-toggle.btn-warning:hover,
.btn-warning:active:focus,
.btn-warning.active:focus,
.open > .dropdown-toggle.btn-warning:focus,
.btn-warning:active.focus,
.btn-warning.active.focus,
.open > .dropdown-toggle.btn-warning.focus {
  color: white;
  background-color: #a66908;
  border-color: #7f5006;
}
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning {
  background-image: none;
}
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning.focus {
  background-color: #f39c12;
  border-color: #f39c12;
}
.btn-warning .badge {
  color: #f39c12;
  background-color: white;
}
.btn-danger {
  color: white;
  background-color: #e74c3c;
  border-color: #e74c3c;
}
.btn-danger:focus,
.btn-danger.focus {
  color: white;
  background-color: #d62c1a;
  border-color: #921e12;
}
.btn-danger:hover {
  color: white;
  background-color: #d62c1a;
  border-color: #cd2a19;
}
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  color: white;
  background-color: #d62c1a;
  border-color: #cd2a19;
}
.btn-danger:active:hover,
.btn-danger.active:hover,
.open > .dropdown-toggle.btn-danger:hover,
.btn-danger:active:focus,
.btn-danger.active:focus,
.open > .dropdown-toggle.btn-danger:focus,
.btn-danger:active.focus,
.btn-danger.active.focus,
.open > .dropdown-toggle.btn-danger.focus {
  color: white;
  background-color: #b62516;
  border-color: #921e12;
}
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  background-image: none;
}
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus {
  background-color: #e74c3c;
  border-color: #e74c3c;
}
.btn-danger .badge {
  color: #e74c3c;
  background-color: white;
}
.btn-link {
  color: #0ce3ac;
  font-weight: normal;
  border-radius: 0;
}
.btn-link,
.btn-link:active,
.btn-link.active,
.btn-link[disabled],
fieldset[disabled] .btn-link {
  background-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active {
  border-color: transparent;
}
.btn-link:hover,
.btn-link:focus {
  color: #0ce3ac;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link[disabled]:hover,
fieldset[disabled] .btn-link:hover,
.btn-link[disabled]:focus,
fieldset[disabled] .btn-link:focus {
  color: #999999;
  text-decoration: none;
}
.btn-lg,
.btn-group-lg > .btn {
  padding: 6px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.btn-sm,
.btn-group-sm > .btn {
  padding: 2px 5px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 3px;
}
.btn-xs,
.btn-group-xs > .btn {
  padding: 1px 3px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 3px;
}
.btn-block {
  display: block;
  width: 100%;
}
.btn-block + .btn-block {
  margin-top: 5px;
}
input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%;
}
.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
}
.fade.in {
  opacity: 1;
}
.collapse {
  display: none;
}
.collapse.in {
  display: block;
}
tr.collapse.in {
  display: table-row;
}
tbody.collapse.in {
  display: table-row-group;
}
.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition-property: height, visibility;
  transition-property: height, visibility;
  -webkit-transition-duration: 0.35s;
  transition-duration: 0.35s;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
}
.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid \9;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}
.dropup,
.dropdown {
  position: relative;
}
.dropdown-toggle:focus {
  outline: 0;
}
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 12px;
  text-align: left;
  background-color: #303030;
  border: 1px solid #cccccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box;
}
.dropdown-menu.pull-right {
  right: 0;
  left: auto;
}
.dropdown-menu .divider {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #464545;
}
.dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.428571429;
  color: #ebebeb;
  white-space: nowrap;
}
.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus {
  text-decoration: none;
  color: white;
  background-color: #375a7f;
}
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
  color: white;
  text-decoration: none;
  outline: 0;
  background-color: #375a7f;
}
.dropdown-menu > .disabled > a,
.dropdown-menu > .disabled > a:hover,
.dropdown-menu > .disabled > a:focus {
  color: #999999;
}
.dropdown-menu > .disabled > a:hover,
.dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed;
}
.open > .dropdown-menu {
  display: block;
}
.open > a {
  outline: 0;
}
.dropdown-menu-right {
  left: auto;
  right: 0;
}
.dropdown-menu-left {
  left: 0;
  right: auto;
}
.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 11px;
  line-height: 1.428571429;
  color: #999999;
  white-space: nowrap;
}
.dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 990;
}
.pull-right > .dropdown-menu {
  right: 0;
  left: auto;
}
.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  border-top: 0;
  border-bottom: 4px dashed;
  border-bottom: 4px solid \9;
  content: "";
}
.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px;
}
@media (min-width: 768px) {
  .navbar-right .dropdown-menu {
    left: auto;
    right: 0;
  }
  .navbar-right .dropdown-menu-left {
    left: 0;
    right: auto;
  }
}
.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  float: left;
}
.btn-group > .btn:hover,
.btn-group-vertical > .btn:hover,
.btn-group > .btn:focus,
.btn-group-vertical > .btn:focus,
.btn-group > .btn:active,
.btn-group-vertical > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn.active {
  z-index: 2;
}
.btn-group .btn + .btn,
.btn-group .btn + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .btn-group {
  margin-left: -1px;
}
.btn-toolbar {
  margin-left: -5px;
}
.btn-toolbar .btn,
.btn-toolbar .btn-group,
.btn-toolbar .input-group {
  float: left;
}
.btn-toolbar > .btn,
.btn-toolbar > .btn-group,
.btn-toolbar > .input-group {
  margin-left: 5px;
}
.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}
.btn-group > .btn:first-child {
  margin-left: 0;
}
.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn:last-child:not(:first-child),
.btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group > .btn-group {
  float: left;
}
.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0;
}
.btn-group > .btn + .dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px;
}
.btn-group > .btn-lg + .dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px;
}
.btn-group.open .dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn-group.open .dropdown-toggle.btn-link {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn .caret {
  margin-left: 0;
}
.btn-lg .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0;
}
.dropup .btn-lg .caret {
  border-width: 0 5px 5px;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group,
.btn-group-vertical > .btn-group > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}
.btn-group-vertical > .btn-group > .btn {
  float: none;
}
.btn-group-vertical > .btn + .btn,
.btn-group-vertical > .btn + .btn-group,
.btn-group-vertical > .btn-group + .btn,
.btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0;
}
.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-bottom-left-radius: 4px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}
.btn-group-justified > .btn,
.btn-group-justified > .btn-group {
  float: none;
  display: table-cell;
  width: 1%;
}
.btn-group-justified > .btn-group .btn {
  width: 100%;
}
.btn-group-justified > .btn-group .dropdown-menu {
  left: auto;
}
[data-toggle="buttons"] > .btn input[type="radio"],
[data-toggle="buttons"] > .btn-group > .btn input[type="radio"],
[data-toggle="buttons"] > .btn input[type="checkbox"],
[data-toggle="buttons"] > .btn-group > .btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}
.input-group[class*="col-"] {
  float: none;
  padding-left: 0;
  padding-right: 0;
}
.input-group .form-control {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0;
}
.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn {
  height: 36px;
  padding: 6px 10px;
  font-size: 15px;
  line-height: 1.3333333;
  border-radius: 6px;
}
select.input-group-lg > .form-control,
select.input-group-lg > .input-group-addon,
select.input-group-lg > .input-group-btn > .btn {
  height: 36px;
  line-height: 36px;
}
textarea.input-group-lg > .form-control,
textarea.input-group-lg > .input-group-addon,
textarea.input-group-lg > .input-group-btn > .btn,
select[multiple].input-group-lg > .form-control,
select[multiple].input-group-lg > .input-group-addon,
select[multiple].input-group-lg > .input-group-btn > .btn {
  height: auto;
}
.input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn {
  height: 24px;
  padding: 2px 5px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 3px;
}
select.input-group-sm > .form-control,
select.input-group-sm > .input-group-addon,
select.input-group-sm > .input-group-btn > .btn {
  height: 24px;
  line-height: 24px;
}
textarea.input-group-sm > .form-control,
textarea.input-group-sm > .input-group-addon,
textarea.input-group-sm > .input-group-btn > .btn,
select[multiple].input-group-sm > .form-control,
select[multiple].input-group-sm > .input-group-addon,
select[multiple].input-group-sm > .input-group-btn > .btn {
  height: auto;
}
.input-group-addon,
.input-group-btn,
.input-group .form-control {
  display: table-cell;
}
.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.input-group-addon,
.input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}
.input-group-addon {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: normal;
  line-height: 1;
  color: #464545;
  text-align: center;
  background-color: #464545;
  border: 1px solid transparent;
  border-radius: 4px;
}
.input-group-addon.input-sm {
  padding: 2px 5px;
  font-size: 11px;
  border-radius: 3px;
}
.input-group-addon.input-lg {
  padding: 6px 10px;
  font-size: 15px;
  border-radius: 6px;
}
.input-group-addon input[type="radio"],
.input-group-addon input[type="checkbox"] {
  margin-top: 0;
}
.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group > .btn,
.input-group-btn:first-child > .dropdown-toggle,
.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.input-group-addon:first-child {
  border-right: 0;
}
.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group > .btn,
.input-group-btn:last-child > .dropdown-toggle,
.input-group-btn:first-child > .btn:not(:first-child),
.input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.input-group-addon:last-child {
  border-left: 0;
}
.input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap;
}
.input-group-btn > .btn {
  position: relative;
}
.input-group-btn > .btn + .btn {
  margin-left: -1px;
}
.input-group-btn > .btn:hover,
.input-group-btn > .btn:focus,
.input-group-btn > .btn:active {
  z-index: 2;
}
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group {
  margin-right: -1px;
}
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group {
  z-index: 2;
  margin-left: -1px;
}
.nav {
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}
.nav > li {
  position: relative;
  display: block;
}
.nav > li > a {
  position: relative;
  display: block;
  padding: 3px 8px;
}
.nav > li > a:hover,
.nav > li > a:focus {
  text-decoration: none;
  background-color: #303030;
}
.nav > li.disabled > a {
  color: #605e5e;
}
.nav > li.disabled > a:hover,
.nav > li.disabled > a:focus {
  color: #605e5e;
  text-decoration: none;
  background-color: transparent;
  cursor: not-allowed;
}
.nav .open > a,
.nav .open > a:hover,
.nav .open > a:focus {
  background-color: #303030;
  border-color: #0ce3ac;
}
.nav .nav-divider {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.nav > li > a > img {
  max-width: none;
}
.nav-tabs {
  border-bottom: 1px solid #464545;
}
.nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}
.nav-tabs > li > a {
  margin-right: 2px;
  line-height: 1.428571429;
  border: 1px solid transparent;
  border-radius: 4px 4px 0 0;
}
.nav-tabs > li > a:hover {
  border-color: #464545 #464545 #464545;
}
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  color: #00bc8c;
  background-color: #222222;
  border: 1px solid #464545;
  border-bottom-color: transparent;
  cursor: default;
}
.nav-tabs.nav-justified {
  width: 100%;
  border-bottom: 0;
}
.nav-tabs.nav-justified > li {
  float: none;
}
.nav-tabs.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
.nav-tabs.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs.nav-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.nav-tabs.nav-justified > .active > a,
.nav-tabs.nav-justified > .active > a:hover,
.nav-tabs.nav-justified > .active > a:focus {
  border: 1px solid #ebebeb;
}
@media (min-width: 768px) {
  .nav-tabs.nav-justified > li > a {
    border-bottom: 1px solid #ebebeb;
    border-radius: 4px 4px 0 0;
  }
  .nav-tabs.nav-justified > .active > a,
  .nav-tabs.nav-justified > .active > a:hover,
  .nav-tabs.nav-justified > .active > a:focus {
    border-bottom-color: #222222;
  }
}
.nav-pills > li {
  float: left;
}
.nav-pills > li > a {
  border-radius: 4px;
}
.nav-pills > li + li {
  margin-left: 2px;
}
.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus {
  color: white;
  background-color: #375a7f;
}
.nav-stacked > li {
  float: none;
}
.nav-stacked > li + li {
  margin-top: 2px;
  margin-left: 0;
}
.nav-justified {
  width: 100%;
}
.nav-justified > li {
  float: none;
}
.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs-justified {
  border-bottom: 0;
}
.nav-tabs-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.nav-tabs-justified > .active > a,
.nav-tabs-justified > .active > a:hover,
.nav-tabs-justified > .active > a:focus {
  border: 1px solid #ebebeb;
}
@media (min-width: 768px) {
  .nav-tabs-justified > li > a {
    border-bottom: 1px solid #ebebeb;
    border-radius: 4px 4px 0 0;
  }
  .nav-tabs-justified > .active > a,
  .nav-tabs-justified > .active > a:hover,
  .nav-tabs-justified > .active > a:focus {
    border-bottom-color: #222222;
  }
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.navbar {
  position: relative;
  min-height: 40px;
  margin-bottom: 17px;
  border: 1px solid transparent;
}
@media (min-width: 768px) {
  .navbar {
    border-radius: 4px;
  }
}
@media (min-width: 768px) {
  .navbar-header {
    float: left;
  }
}
.navbar-collapse {
  overflow-x: visible;
  padding-right: 15px;
  padding-left: 15px;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch;
}
.navbar-collapse.in {
  overflow-y: auto;
}
@media (min-width: 768px) {
  .navbar-collapse {
    width: auto;
    border-top: 0;
    box-shadow: none;
  }
  .navbar-collapse.collapse {
    display: block !important;
    height: auto !important;
    padding-bottom: 0;
    overflow: visible !important;
  }
  .navbar-collapse.in {
    overflow-y: visible;
  }
  .navbar-fixed-top .navbar-collapse,
  .navbar-static-top .navbar-collapse,
  .navbar-fixed-bottom .navbar-collapse {
    padding-left: 0;
    padding-right: 0;
  }
}
.navbar-fixed-top .navbar-collapse,
.navbar-fixed-bottom .navbar-collapse {
  max-height: 340px;
}
@media (max-device-width: 480px) and (orientation: landscape) {
  .navbar-fixed-top .navbar-collapse,
  .navbar-fixed-bottom .navbar-collapse {
    max-height: 200px;
  }
}
.container > .navbar-header,
.container-fluid > .navbar-header,
.container > .navbar-collapse,
.container-fluid > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .container > .navbar-header,
  .container-fluid > .navbar-header,
  .container > .navbar-collapse,
  .container-fluid > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.navbar-static-top {
  z-index: 1000;
  border-width: 0 0 1px;
}
@media (min-width: 768px) {
  .navbar-static-top {
    border-radius: 0;
  }
}
.navbar-fixed-top,
.navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030;
}
@media (min-width: 768px) {
  .navbar-fixed-top,
  .navbar-fixed-bottom {
    border-radius: 0;
  }
}
.navbar-fixed-top {
  top: 0;
  border-width: 0 0 1px;
}
.navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0;
}
.navbar-brand {
  float: left;
  padding: 11.5px 15px;
  font-size: 15px;
  line-height: 17px;
  height: 40px;
}
.navbar-brand:hover,
.navbar-brand:focus {
  text-decoration: none;
}
.navbar-brand > img {
  display: block;
}
@media (min-width: 768px) {
  .navbar > .container .navbar-brand,
  .navbar > .container-fluid .navbar-brand {
    margin-left: -15px;
  }
}
.navbar-toggle {
  position: relative;
  float: right;
  margin-right: 15px;
  padding: 9px 10px;
  margin-top: 3px;
  margin-bottom: 3px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}
.navbar-toggle:focus {
  outline: 0;
}
.navbar-toggle .icon-bar {
  display: block;
  width: 22px;
  height: 2px;
  border-radius: 1px;
}
.navbar-toggle .icon-bar + .icon-bar {
  margin-top: 4px;
}
@media (min-width: 768px) {
  .navbar-toggle {
    display: none;
  }
}
.navbar-nav {
  margin: 5.75px -15px;
}
.navbar-nav > li > a {
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 17px;
}
@media (max-width: 767px) {
  .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    box-shadow: none;
  }
  .navbar-nav .open .dropdown-menu > li > a,
  .navbar-nav .open .dropdown-menu .dropdown-header {
    padding: 5px 15px 5px 25px;
  }
  .navbar-nav .open .dropdown-menu > li > a {
    line-height: 17px;
  }
  .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-nav .open .dropdown-menu > li > a:focus {
    background-image: none;
  }
}
@media (min-width: 768px) {
  .navbar-nav {
    float: left;
    margin: 0;
  }
  .navbar-nav > li {
    float: left;
  }
  .navbar-nav > li > a {
    padding-top: 11.5px;
    padding-bottom: 11.5px;
  }
}
.navbar-form {
  margin-left: -15px;
  margin-right: -15px;
  padding: 10px 15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 5.5px;
  margin-bottom: 5.5px;
}
@media (min-width: 768px) {
  .navbar-form .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .navbar-form .form-control-static {
    display: inline-block;
  }
  .navbar-form .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .navbar-form .input-group .input-group-addon,
  .navbar-form .input-group .input-group-btn,
  .navbar-form .input-group .form-control {
    width: auto;
  }
  .navbar-form .input-group > .form-control {
    width: 100%;
  }
  .navbar-form .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio,
  .navbar-form .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio label,
  .navbar-form .checkbox label {
    padding-left: 0;
  }
  .navbar-form .radio input[type="radio"],
  .navbar-form .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0;
  }
  .navbar-form .has-feedback .form-control-feedback {
    top: 0;
  }
}
@media (max-width: 767px) {
  .navbar-form .form-group {
    margin-bottom: 5px;
  }
  .navbar-form .form-group:last-child {
    margin-bottom: 0;
  }
}
@media (min-width: 768px) {
  .navbar-form {
    width: auto;
    border: 0;
    margin-left: 0;
    margin-right: 0;
    padding-top: 0;
    padding-bottom: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
  }
}
.navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
  margin-bottom: 0;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.navbar-btn {
  margin-top: 5.5px;
  margin-bottom: 5.5px;
}
.navbar-btn.btn-sm {
  margin-top: 8px;
  margin-bottom: 8px;
}
.navbar-btn.btn-xs {
  margin-top: 9px;
  margin-bottom: 9px;
}
.navbar-text {
  margin-top: 11.5px;
  margin-bottom: 11.5px;
}
@media (min-width: 768px) {
  .navbar-text {
    float: left;
    margin-left: 15px;
    margin-right: 15px;
  }
}
@media (min-width: 768px) {
  .navbar-left {
    float: left !important;
  }
  .navbar-right {
    float: right !important;
    margin-right: -15px;
  }
  .navbar-right ~ .navbar-right {
    margin-right: 0;
  }
}
.navbar-default {
  background-color: #375a7f;
  border-color: transparent;
}
.navbar-default .navbar-brand {
  color: white;
}
.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
  color: #00bc8c;
  background-color: transparent;
}
.navbar-default .navbar-text {
  color: #777777;
}
.navbar-default .navbar-nav > li > a {
  color: white;
}
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
  color: #00bc8c;
  background-color: transparent;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
  color: white;
  background-color: #28415b;
}
.navbar-default .navbar-nav > .disabled > a,
.navbar-default .navbar-nav > .disabled > a:hover,
.navbar-default .navbar-nav > .disabled > a:focus {
  color: #cccccc;
  background-color: transparent;
}
.navbar-default .navbar-toggle {
  border-color: #28415b;
}
.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
  background-color: #28415b;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: white;
}
.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
  border-color: transparent;
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus {
  background-color: #28415b;
  color: white;
}
@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: white;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #00bc8c;
    background-color: transparent;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: white;
    background-color: #28415b;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #cccccc;
    background-color: transparent;
  }
}
.navbar-default .navbar-link {
  color: white;
}
.navbar-default .navbar-link:hover {
  color: #00bc8c;
}
.navbar-default .btn-link {
  color: white;
}
.navbar-default .btn-link:hover,
.navbar-default .btn-link:focus {
  color: #00bc8c;
}
.navbar-default .btn-link[disabled]:hover,
fieldset[disabled] .navbar-default .btn-link:hover,
.navbar-default .btn-link[disabled]:focus,
fieldset[disabled] .navbar-default .btn-link:focus {
  color: #cccccc;
}
.navbar-inverse {
  background-color: #00bc8c;
  border-color: transparent;
}
.navbar-inverse .navbar-brand {
  color: white;
}
.navbar-inverse .navbar-brand:hover,
.navbar-inverse .navbar-brand:focus {
  color: #375a7f;
  background-color: transparent;
}
.navbar-inverse .navbar-text {
  color: white;
}
.navbar-inverse .navbar-nav > li > a {
  color: white;
}
.navbar-inverse .navbar-nav > li > a:hover,
.navbar-inverse .navbar-nav > li > a:focus {
  color: #375a7f;
  background-color: transparent;
}
.navbar-inverse .navbar-nav > .active > a,
.navbar-inverse .navbar-nav > .active > a:hover,
.navbar-inverse .navbar-nav > .active > a:focus {
  color: white;
  background-color: #00a379;
}
.navbar-inverse .navbar-nav > .disabled > a,
.navbar-inverse .navbar-nav > .disabled > a:hover,
.navbar-inverse .navbar-nav > .disabled > a:focus {
  color: #aaaaaa;
  background-color: transparent;
}
.navbar-inverse .navbar-toggle {
  border-color: #008966;
}
.navbar-inverse .navbar-toggle:hover,
.navbar-inverse .navbar-toggle:focus {
  background-color: #008966;
}
.navbar-inverse .navbar-toggle .icon-bar {
  background-color: white;
}
.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
  border-color: #009871;
}
.navbar-inverse .navbar-nav > .open > a,
.navbar-inverse .navbar-nav > .open > a:hover,
.navbar-inverse .navbar-nav > .open > a:focus {
  background-color: #00a379;
  color: white;
}
@media (max-width: 767px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
    border-color: transparent;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
    background-color: transparent;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: white;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #375a7f;
    background-color: transparent;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: white;
    background-color: #00a379;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #aaaaaa;
    background-color: transparent;
  }
}
.navbar-inverse .navbar-link {
  color: white;
}
.navbar-inverse .navbar-link:hover {
  color: #375a7f;
}
.navbar-inverse .btn-link {
  color: white;
}
.navbar-inverse .btn-link:hover,
.navbar-inverse .btn-link:focus {
  color: #375a7f;
}
.navbar-inverse .btn-link[disabled]:hover,
fieldset[disabled] .navbar-inverse .btn-link:hover,
.navbar-inverse .btn-link[disabled]:focus,
fieldset[disabled] .navbar-inverse .btn-link:focus {
  color: #aaaaaa;
}
.breadcrumb {
  padding: 8px 15px;
  margin-bottom: 17px;
  list-style: none;
  background-color: #464545;
  border-radius: 4px;
}
.breadcrumb > li {
  display: inline-block;
}
.breadcrumb > li + li:before {
  content: "/\00a0";
  padding: 0 5px;
  color: white;
}
.breadcrumb > .active {
  color: #999999;
}
.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 17px 0;
  border-radius: 4px;
}
.pagination > li {
  display: inline;
}
.pagination > li > a,
.pagination > li > span {
  position: relative;
  float: left;
  padding: 4px 8px;
  line-height: 1.428571429;
  text-decoration: none;
  color: white;
  background-color: #00bc8c;
  border: 1px solid transparent;
  margin-left: -1px;
}
.pagination > li:first-child > a,
.pagination > li:first-child > span {
  margin-left: 0;
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
}
.pagination > li:last-child > a,
.pagination > li:last-child > span {
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
}
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
  z-index: 2;
  color: white;
  background-color: #00dba3;
  border-color: transparent;
}
.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  z-index: 3;
  color: white;
  background-color: #00dba3;
  border-color: transparent;
  cursor: default;
}
.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
  color: white;
  background-color: #007053;
  border-color: transparent;
  cursor: not-allowed;
}
.pagination-lg > li > a,
.pagination-lg > li > span {
  padding: 6px 10px;
  font-size: 15px;
  line-height: 1.3333333;
}
.pagination-lg > li:first-child > a,
.pagination-lg > li:first-child > span {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.pagination-lg > li:last-child > a,
.pagination-lg > li:last-child > span {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
}
.pagination-sm > li > a,
.pagination-sm > li > span {
  padding: 2px 5px;
  font-size: 11px;
  line-height: 1.5;
}
.pagination-sm > li:first-child > a,
.pagination-sm > li:first-child > span {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
.pagination-sm > li:last-child > a,
.pagination-sm > li:last-child > span {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
.pager {
  padding-left: 0;
  margin: 17px 0;
  list-style: none;
  text-align: center;
}
.pager li {
  display: inline;
}
.pager li > a,
.pager li > span {
  display: inline-block;
  padding: 5px 14px;
  background-color: #00bc8c;
  border: 1px solid transparent;
  border-radius: 15px;
}
.pager li > a:hover,
.pager li > a:focus {
  text-decoration: none;
  background-color: #00dba3;
}
.pager .next > a,
.pager .next > span {
  float: right;
}
.pager .previous > a,
.pager .previous > span {
  float: left;
}
.pager .disabled > a,
.pager .disabled > a:hover,
.pager .disabled > a:focus,
.pager .disabled > span {
  color: #dddddd;
  background-color: #00bc8c;
  cursor: not-allowed;
}
.label {
  display: inline;
  padding: 0.2em 0.6em 0.3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
}
a.label:hover,
a.label:focus {
  color: white;
  text-decoration: none;
  cursor: pointer;
}
.label:empty {
  display: none;
}
.btn .label {
  position: relative;
  top: -1px;
}
.label-default {
  background-color: #464545;
}
.label-default[href]:hover,
.label-default[href]:focus {
  background-color: #2c2c2c;
}
.label-primary {
  background-color: #375a7f;
}
.label-primary[href]:hover,
.label-primary[href]:focus {
  background-color: #28415b;
}
.label-success {
  background-color: #00bc8c;
}
.label-success[href]:hover,
.label-success[href]:focus {
  background-color: #008966;
}
.label-info {
  background-color: #3498db;
}
.label-info[href]:hover,
.label-info[href]:focus {
  background-color: #217dbb;
}
.label-warning {
  background-color: #f39c12;
}
.label-warning[href]:hover,
.label-warning[href]:focus {
  background-color: #c87f0a;
}
.label-danger {
  background-color: #e74c3c;
}
.label-danger[href]:hover,
.label-danger[href]:focus {
  background-color: #d62c1a;
}
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 11px;
  font-weight: bold;
  color: white;
  line-height: 1;
  vertical-align: middle;
  white-space: nowrap;
  text-align: center;
  background-color: #464545;
  border-radius: 10px;
}
.badge:empty {
  display: none;
}
.btn .badge {
  position: relative;
  top: -1px;
}
.btn-xs .badge,
.btn-group-xs > .btn .badge {
  top: 0;
  padding: 1px 5px;
}
a.badge:hover,
a.badge:focus {
  color: white;
  text-decoration: none;
  cursor: pointer;
}
.list-group-item.active > .badge,
.nav-pills > .active > a > .badge {
  color: #375a7f;
  background-color: white;
}
.list-group-item > .badge {
  float: right;
}
.list-group-item > .badge + .badge {
  margin-right: 5px;
}
.nav-pills > li > a > .badge {
  margin-left: 3px;
}
.jumbotron {
  padding-top: 30px;
  padding-bottom: 30px;
  margin-bottom: 30px;
  color: inherit;
  background-color: #303030;
}
.jumbotron h1,
.jumbotron .h1 {
  color: inherit;
}
.jumbotron p {
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 200;
}
.jumbotron > hr {
  border-top-color: #161616;
}
.container .jumbotron,
.container-fluid .jumbotron {
  border-radius: 6px;
  padding-left: 15px;
  padding-right: 15px;
}
.jumbotron .container {
  max-width: 100%;
}
@media screen and (min-width: 768px) {
  .jumbotron {
    padding-top: 48px;
    padding-bottom: 48px;
  }
  .container .jumbotron,
  .container-fluid .jumbotron {
    padding-left: 60px;
    padding-right: 60px;
  }
  .jumbotron h1,
  .jumbotron .h1 {
    font-size: 54px;
  }
}
.thumbnail {
  display: block;
  padding: 2px;
  margin-bottom: 17px;
  line-height: 1.428571429;
  background-color: #222222;
  border: 1px solid #464545;
  border-radius: 4px;
  -webkit-transition: border 0.2s ease-in-out;
  -o-transition: border 0.2s ease-in-out;
  transition: border 0.2s ease-in-out;
}
.thumbnail > img,
.thumbnail a > img {
  margin-left: auto;
  margin-right: auto;
}
a.thumbnail:hover,
a.thumbnail:focus,
a.thumbnail.active {
  border-color: #0ce3ac;
}
.thumbnail .caption {
  padding: 9px;
  color: white;
}
.alert {
  padding: 15px;
  margin-bottom: 17px;
  border: 1px solid transparent;
  border-radius: 4px;
}
.alert h4 {
  margin-top: 0;
  color: inherit;
}
.alert .alert-link {
  font-weight: bold;
}
.alert > p,
.alert > ul {
  margin-bottom: 0;
}
.alert > p + p {
  margin-top: 5px;
}
.alert-dismissable,
.alert-dismissible {
  padding-right: 35px;
}
.alert-dismissable .close,
.alert-dismissible .close {
  position: relative;
  top: -2px;
  right: -21px;
  color: inherit;
}
.alert-success {
  background-color: #00bc8c;
  border-color: #00bc8c;
  color: white;
}
.alert-success hr {
  border-top-color: #00a379;
}
.alert-success .alert-link {
  color: #e6e6e6;
}
.alert-info {
  background-color: #3498db;
  border-color: #3498db;
  color: white;
}
.alert-info hr {
  border-top-color: #258cd1;
}
.alert-info .alert-link {
  color: #e6e6e6;
}
.alert-warning {
  background-color: #f39c12;
  border-color: #f39c12;
  color: white;
}
.alert-warning hr {
  border-top-color: #e08e0b;
}
.alert-warning .alert-link {
  color: #e6e6e6;
}
.alert-danger {
  background-color: #e74c3c;
  border-color: #e74c3c;
  color: white;
}
.alert-danger hr {
  border-top-color: #e43725;
}
.alert-danger .alert-link {
  color: #e6e6e6;
}
.alert .alert-link {
  text-decoration: underline;
}
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
.progress {
  overflow: hidden;
  height: 17px;
  margin-bottom: 17px;
  background-color: #ebebeb;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 11px;
  line-height: 17px;
  color: white;
  text-align: center;
  background-color: #375a7f;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: width 0.6s ease;
  -o-transition: width 0.6s ease;
  transition: width 0.6s ease;
}
.progress-striped .progress-bar,
.progress-bar-striped {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.progress.active .progress-bar,
.progress-bar.active {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  -o-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite;
}
.progress-bar-success {
  background-color: #00bc8c;
}
.progress-striped .progress-bar-success {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-info {
  background-color: #3498db;
}
.progress-striped .progress-bar-info {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-warning {
  background-color: #f39c12;
}
.progress-striped .progress-bar-warning {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-danger {
  background-color: #e74c3c;
}
.progress-striped .progress-bar-danger {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.media {
  margin-top: 15px;
}
.media:first-child {
  margin-top: 0;
}
.media,
.media-body {
  zoom: 1;
  overflow: hidden;
}
.media-body {
  width: 10000px;
}
.media-object {
  display: block;
}
.media-object.img-thumbnail {
  max-width: none;
}
.media-right,
.media > .pull-right {
  padding-left: 10px;
}
.media-left,
.media > .pull-left {
  padding-right: 10px;
}
.media-left,
.media-right,
.media-body {
  display: table-cell;
  vertical-align: top;
}
.media-middle {
  vertical-align: middle;
}
.media-bottom {
  vertical-align: bottom;
}
.media-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.media-list {
  padding-left: 0;
  list-style: none;
}
.list-group {
  margin-bottom: 20px;
  padding-left: 0;
}
.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #303030;
  border: 1px solid #464545;
}
.list-group-item:first-child {
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
}
.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
a.list-group-item,
button.list-group-item {
  color: #0ce3ac;
}
a.list-group-item .list-group-item-heading,
button.list-group-item .list-group-item-heading {
  color: #0bcb9a;
}
a.list-group-item:hover,
button.list-group-item:hover,
a.list-group-item:focus,
button.list-group-item:focus {
  text-decoration: none;
  color: #0ce3ac;
  background-color: transparent;
}
button.list-group-item {
  width: 100%;
  text-align: left;
}
.list-group-item.disabled,
.list-group-item.disabled:hover,
.list-group-item.disabled:focus {
  background-color: #ebebeb;
  color: #999999;
  cursor: not-allowed;
}
.list-group-item.disabled .list-group-item-heading,
.list-group-item.disabled:hover .list-group-item-heading,
.list-group-item.disabled:focus .list-group-item-heading {
  color: inherit;
}
.list-group-item.disabled .list-group-item-text,
.list-group-item.disabled:hover .list-group-item-text,
.list-group-item.disabled:focus .list-group-item-text {
  color: #999999;
}
.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
  z-index: 2;
  color: white;
  background-color: #375a7f;
  border-color: #375a7f;
}
.list-group-item.active .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading,
.list-group-item.active .list-group-item-heading > small,
.list-group-item.active:hover .list-group-item-heading > small,
.list-group-item.active:focus .list-group-item-heading > small,
.list-group-item.active .list-group-item-heading > .small,
.list-group-item.active:hover .list-group-item-heading > .small,
.list-group-item.active:focus .list-group-item-heading > .small {
  color: inherit;
}
.list-group-item.active .list-group-item-text,
.list-group-item.active:hover .list-group-item-text,
.list-group-item.active:focus .list-group-item-text {
  color: #a8c0da;
}
.list-group-item-success {
  color: white;
  background-color: #00bc8c;
}
a.list-group-item-success,
button.list-group-item-success {
  color: white;
}
a.list-group-item-success .list-group-item-heading,
button.list-group-item-success .list-group-item-heading {
  color: inherit;
}
a.list-group-item-success:hover,
button.list-group-item-success:hover,
a.list-group-item-success:focus,
button.list-group-item-success:focus {
  color: white;
  background-color: #00a379;
}
a.list-group-item-success.active,
button.list-group-item-success.active,
a.list-group-item-success.active:hover,
button.list-group-item-success.active:hover,
a.list-group-item-success.active:focus,
button.list-group-item-success.active:focus {
  color: white;
  background-color: white;
  border-color: white;
}
.list-group-item-info {
  color: white;
  background-color: #3498db;
}
a.list-group-item-info,
button.list-group-item-info {
  color: white;
}
a.list-group-item-info .list-group-item-heading,
button.list-group-item-info .list-group-item-heading {
  color: inherit;
}
a.list-group-item-info:hover,
button.list-group-item-info:hover,
a.list-group-item-info:focus,
button.list-group-item-info:focus {
  color: white;
  background-color: #258cd1;
}
a.list-group-item-info.active,
button.list-group-item-info.active,
a.list-group-item-info.active:hover,
button.list-group-item-info.active:hover,
a.list-group-item-info.active:focus,
button.list-group-item-info.active:focus {
  color: white;
  background-color: white;
  border-color: white;
}
.list-group-item-warning {
  color: white;
  background-color: #f39c12;
}
a.list-group-item-warning,
button.list-group-item-warning {
  color: white;
}
a.list-group-item-warning .list-group-item-heading,
button.list-group-item-warning .list-group-item-heading {
  color: inherit;
}
a.list-group-item-warning:hover,
button.list-group-item-warning:hover,
a.list-group-item-warning:focus,
button.list-group-item-warning:focus {
  color: white;
  background-color: #e08e0b;
}
a.list-group-item-warning.active,
button.list-group-item-warning.active,
a.list-group-item-warning.active:hover,
button.list-group-item-warning.active:hover,
a.list-group-item-warning.active:focus,
button.list-group-item-warning.active:focus {
  color: white;
  background-color: white;
  border-color: white;
}
.list-group-item-danger {
  color: white;
  background-color: #e74c3c;
}
a.list-group-item-danger,
button.list-group-item-danger {
  color: white;
}
a.list-group-item-danger .list-group-item-heading,
button.list-group-item-danger .list-group-item-heading {
  color: inherit;
}
a.list-group-item-danger:hover,
button.list-group-item-danger:hover,
a.list-group-item-danger:focus,
button.list-group-item-danger:focus {
  color: white;
  background-color: #e43725;
}
a.list-group-item-danger.active,
button.list-group-item-danger.active,
a.list-group-item-danger.active:hover,
button.list-group-item-danger.active:hover,
a.list-group-item-danger.active:focus,
button.list-group-item-danger.active:focus {
  color: white;
  background-color: white;
  border-color: white;
}
.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}
.panel {
  margin-bottom: 17px;
  background-color: #303030;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
.panel-body {
  padding: 15px;
}
.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel-heading > .dropdown .dropdown-toggle {
  color: inherit;
}
.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 14px;
  color: inherit;
}
.panel-title > a,
.panel-title > small,
.panel-title > .small,
.panel-title > small > a,
.panel-title > .small > a {
  color: inherit;
}
.panel-footer {
  padding: 10px 15px;
  background-color: #464545;
  border-top: 1px solid #464545;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel > .list-group,
.panel > .panel-collapse > .list-group {
  margin-bottom: 0;
}
.panel > .list-group .list-group-item,
.panel > .panel-collapse > .list-group .list-group-item {
  border-width: 1px 0;
  border-radius: 0;
}
.panel > .list-group:first-child .list-group-item:first-child,
.panel > .panel-collapse > .list-group:first-child .list-group-item:first-child {
  border-top: 0;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel > .list-group:last-child .list-group-item:last-child,
.panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {
  border-bottom: 0;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel > .panel-heading + .panel-collapse > .list-group .list-group-item:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0;
}
.list-group + .panel-footer {
  border-top-width: 0;
}
.panel > .table,
.panel > .table-responsive > .table,
.panel > .panel-collapse > .table {
  margin-bottom: 0;
}
.panel > .table caption,
.panel > .table-responsive > .table caption,
.panel > .panel-collapse > .table caption {
  padding-left: 15px;
  padding-right: 15px;
}
.panel > .table:first-child,
.panel > .table-responsive:first-child > .table:first-child {
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel > .table:first-child > thead:first-child > tr:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
  border-top-left-radius: 3px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
  border-top-right-radius: 3px;
}
.panel > .table:last-child,
.panel > .table-responsive:last-child > .table:last-child {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
  border-bottom-left-radius: 3px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
  border-bottom-right-radius: 3px;
}
.panel > .panel-body + .table,
.panel > .panel-body + .table-responsive,
.panel > .table + .panel-body,
.panel > .table-responsive + .panel-body {
  border-top: 1px solid #464545;
}
.panel > .table > tbody:first-child > tr:first-child th,
.panel > .table > tbody:first-child > tr:first-child td {
  border-top: 0;
}
.panel > .table-bordered,
.panel > .table-responsive > .table-bordered {
  border: 0;
}
.panel > .table-bordered > thead > tr > th:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.panel > .table-bordered > tbody > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.panel > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-bordered > thead > tr > td:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.panel > .table-bordered > tbody > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.panel > .table-bordered > tfoot > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
  border-left: 0;
}
.panel > .table-bordered > thead > tr > th:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.panel > .table-bordered > tbody > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.panel > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-bordered > thead > tr > td:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.panel > .table-bordered > tbody > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.panel > .table-bordered > tfoot > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
  border-right: 0;
}
.panel > .table-bordered > thead > tr:first-child > td,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
.panel > .table-bordered > tbody > tr:first-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
.panel > .table-bordered > thead > tr:first-child > th,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > th,
.panel > .table-bordered > tbody > tr:first-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
  border-bottom: 0;
}
.panel > .table-bordered > tbody > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.panel > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-bordered > tbody > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.panel > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
  border-bottom: 0;
}
.panel > .table-responsive {
  border: 0;
  margin-bottom: 0;
}
.panel-group {
  margin-bottom: 17px;
}
.panel-group .panel {
  margin-bottom: 0;
  border-radius: 4px;
}
.panel-group .panel + .panel {
  margin-top: 5px;
}
.panel-group .panel-heading {
  border-bottom: 0;
}
.panel-group .panel-heading + .panel-collapse > .panel-body,
.panel-group .panel-heading + .panel-collapse > .list-group {
  border-top: 1px solid #464545;
}
.panel-group .panel-footer {
  border-top: 0;
}
.panel-group .panel-footer + .panel-collapse .panel-body {
  border-bottom: 1px solid #464545;
}
.panel-default {
  border-color: #464545;
}
.panel-default > .panel-heading {
  color: white;
  background-color: #303030;
  border-color: #464545;
}
.panel-default > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #464545;
}
.panel-default > .panel-heading .badge {
  color: #303030;
  background-color: white;
}
.panel-default > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #464545;
}
.panel-primary {
  border-color: #375a7f;
}
.panel-primary > .panel-heading {
  color: white;
  background-color: #375a7f;
  border-color: #375a7f;
}
.panel-primary > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #375a7f;
}
.panel-primary > .panel-heading .badge {
  color: #375a7f;
  background-color: white;
}
.panel-primary > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #375a7f;
}
.panel-success {
  border-color: #00bc8c;
}
.panel-success > .panel-heading {
  color: white;
  background-color: #00bc8c;
  border-color: #00bc8c;
}
.panel-success > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #00bc8c;
}
.panel-success > .panel-heading .badge {
  color: #00bc8c;
  background-color: white;
}
.panel-success > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #00bc8c;
}
.panel-info {
  border-color: #3498db;
}
.panel-info > .panel-heading {
  color: white;
  background-color: #3498db;
  border-color: #3498db;
}
.panel-info > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #3498db;
}
.panel-info > .panel-heading .badge {
  color: #3498db;
  background-color: white;
}
.panel-info > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #3498db;
}
.panel-warning {
  border-color: #f39c12;
}
.panel-warning > .panel-heading {
  color: white;
  background-color: #f39c12;
  border-color: #f39c12;
}
.panel-warning > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #f39c12;
}
.panel-warning > .panel-heading .badge {
  color: #f39c12;
  background-color: white;
}
.panel-warning > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #f39c12;
}
.panel-danger {
  border-color: #e74c3c;
}
.panel-danger > .panel-heading {
  color: white;
  background-color: #e74c3c;
  border-color: #e74c3c;
}
.panel-danger > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #e74c3c;
}
.panel-danger > .panel-heading .badge {
  color: #e74c3c;
  background-color: white;
}
.panel-danger > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #e74c3c;
}
.embed-responsive {
  position: relative;
  display: block;
  height: 0;
  padding: 0;
  overflow: hidden;
}
.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  border: 0;
}
.embed-responsive-16by9 {
  padding-bottom: 56.25%;
}
.embed-responsive-4by3 {
  padding-bottom: 75%;
}
.well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #303030;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}
.well blockquote {
  border-color: #dddddd;
  border-color: rgba(0, 0, 0, 0.15);
}
.well-lg {
  padding: 24px;
  border-radius: 6px;
}
.well-sm {
  padding: 9px;
  border-radius: 3px;
}
.close {
  float: right;
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
  color: white;
  text-shadow: none;
  opacity: 0.2;
  filter: alpha(opacity=20);
}
.close:hover,
.close:focus {
  color: white;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
}
.modal-open {
  overflow: hidden;
}
.modal {
  display: none;
  overflow: hidden;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}
.modal.fade .modal-dialog {
  -webkit-transform: translate(0, -25%);
  -ms-transform: translate(0, -25%);
  -o-transform: translate(0, -25%);
  transform: translate(0, -25%);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  -moz-transition: -moz-transform 0.3s ease-out;
  -o-transition: -o-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
}
.modal.in .modal-dialog {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}
.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px;
}
.modal-content {
  position: relative;
  background-color: #303030;
  border: 1px solid #999999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: 0;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: black;
}
.modal-backdrop.fade {
  opacity: 0;
  filter: alpha(opacity=0);
}
.modal-backdrop.in {
  opacity: 0.7;
  filter: alpha(opacity=70);
}
.modal-header {
  padding: 15px;
  border-bottom: 1px solid #464545;
}
.modal-header .close {
  margin-top: -2px;
}
.modal-title {
  margin: 0;
  line-height: 1.428571429;
}
.modal-body {
  position: relative;
  padding: 20px;
}
.modal-footer {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #464545;
}
.modal-footer .btn + .btn {
  margin-left: 5px;
  margin-bottom: 0;
}
.modal-footer .btn-group .btn + .btn {
  margin-left: -1px;
}
.modal-footer .btn-block + .btn-block {
  margin-left: 0;
}
.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}
@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto;
  }
  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }
  .modal-sm {
    width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.428571429;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  font-size: 11px;
  opacity: 0;
  filter: alpha(opacity=0);
}
.tooltip.in {
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.tooltip.top {
  margin-top: -3px;
  padding: 5px 0;
}
.tooltip.right {
  margin-left: 3px;
  padding: 0 5px;
}
.tooltip.bottom {
  margin-top: 3px;
  padding: 5px 0;
}
.tooltip.left {
  margin-left: -3px;
  padding: 0 5px;
}
.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: white;
  text-align: center;
  background-color: black;
  border-radius: 4px;
}
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: black;
}
.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  right: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: black;
}
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  left: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: black;
}
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: black;
}
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: black;
}
.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: black;
}
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  right: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: black;
}
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  left: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: black;
}
.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: none;
  max-width: 276px;
  padding: 1px;
  font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.428571429;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  font-size: 12px;
  background-color: #303030;
  background-clip: padding-box;
  border: 1px solid #999999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}
.popover.top {
  margin-top: -10px;
}
.popover.right {
  margin-left: 10px;
}
.popover.bottom {
  margin-top: 10px;
}
.popover.left {
  margin-left: -10px;
}
.popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 12px;
  background-color: #282828;
  border-bottom: 1px solid #1b1b1b;
  border-radius: 5px 5px 0 0;
}
.popover-content {
  padding: 9px 14px;
}
.popover > .arrow,
.popover > .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popover > .arrow {
  border-width: 11px;
}
.popover > .arrow:after {
  border-width: 10px;
  content: "";
}
.popover.top > .arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #666666;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px;
}
.popover.top > .arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-bottom-width: 0;
  border-top-color: #303030;
}
.popover.right > .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #666666;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.popover.right > .arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #303030;
}
.popover.bottom > .arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #666666;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px;
}
.popover.bottom > .arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-top-width: 0;
  border-bottom-color: #303030;
}
.popover.left > .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #666666;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.popover.left > .arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-left-color: #303030;
  bottom: -10px;
}
.carousel {
  position: relative;
}
.carousel-inner {
  position: relative;
  overflow: hidden;
  width: 100%;
}
.carousel-inner > .item {
  display: none;
  position: relative;
  -webkit-transition: 0.6s ease-in-out left;
  -o-transition: 0.6s ease-in-out left;
  transition: 0.6s ease-in-out left;
}
.carousel-inner > .item > img,
.carousel-inner > .item > a > img {
  line-height: 1;
}
@media all and (transform-3d), (-webkit-transform-3d) {
  .carousel-inner > .item {
    -webkit-transition: -webkit-transform 0.6s ease-in-out;
    -moz-transition: -moz-transform 0.6s ease-in-out;
    -o-transition: -o-transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    -moz-perspective: 1000px;
    perspective: 1000px;
  }
  .carousel-inner > .item.next,
  .carousel-inner > .item.active.right {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    left: 0;
  }
  .carousel-inner > .item.prev,
  .carousel-inner > .item.active.left {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    left: 0;
  }
  .carousel-inner > .item.next.left,
  .carousel-inner > .item.prev.right,
  .carousel-inner > .item.active {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    left: 0;
  }
}
.carousel-inner > .active,
.carousel-inner > .next,
.carousel-inner > .prev {
  display: block;
}
.carousel-inner > .active {
  left: 0;
}
.carousel-inner > .next,
.carousel-inner > .prev {
  position: absolute;
  top: 0;
  width: 100%;
}
.carousel-inner > .next {
  left: 100%;
}
.carousel-inner > .prev {
  left: -100%;
}
.carousel-inner > .next.left,
.carousel-inner > .prev.right {
  left: 0;
}
.carousel-inner > .active.left {
  left: -100%;
}
.carousel-inner > .active.right {
  left: 100%;
}
.carousel-control {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 15%;
  opacity: 0.5;
  filter: alpha(opacity=50);
  font-size: 20px;
  color: white;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.carousel-control.left {
  background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);
}
.carousel-control.right {
  left: auto;
  right: 0;
  background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);
}
.carousel-control:hover,
.carousel-control:focus {
  outline: 0;
  color: white;
  text-decoration: none;
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.carousel-control .icon-prev,
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-left,
.carousel-control .glyphicon-chevron-right {
  position: absolute;
  top: 50%;
  margin-top: -10px;
  z-index: 5;
  display: inline-block;
}
.carousel-control .icon-prev,
.carousel-control .glyphicon-chevron-left {
  left: 50%;
  margin-left: -10px;
}
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-right {
  right: 50%;
  margin-right: -10px;
}
.carousel-control .icon-prev,
.carousel-control .icon-next {
  width: 20px;
  height: 20px;
  line-height: 1;
  font-family: serif;
}
.carousel-control .icon-prev:before {
  content: '\2039';
}
.carousel-control .icon-next:before {
  content: '\203a';
}
.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 15;
  width: 60%;
  margin-left: -30%;
  padding-left: 0;
  list-style: none;
  text-align: center;
}
.carousel-indicators li {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 1px;
  text-indent: -999px;
  border: 1px solid white;
  border-radius: 10px;
  cursor: pointer;
  background-color: black \9;
  background-color: transparent;
}
.carousel-indicators .active {
  margin: 0;
  width: 12px;
  height: 12px;
  background-color: white;
}
.carousel-caption {
  position: absolute;
  left: 15%;
  right: 15%;
  bottom: 20px;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: white;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.carousel-caption .btn {
  text-shadow: none;
}
@media screen and (min-width: 768px) {
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-prev,
  .carousel-control .icon-next {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    font-size: 30px;
  }
  .carousel-control .glyphicon-chevron-left,
  .carousel-control .icon-prev {
    margin-left: -15px;
  }
  .carousel-control .glyphicon-chevron-right,
  .carousel-control .icon-next {
    margin-right: -15px;
  }
  .carousel-caption {
    left: 20%;
    right: 20%;
    padding-bottom: 30px;
  }
  .carousel-indicators {
    bottom: 20px;
  }
}
.clearfix:before,
.clearfix:after,
.dl-horizontal dd:before,
.container:before,
.container-fluid:before,
.row:before,
.form-horizontal .form-group:before,
.btn-toolbar:before,
.btn-group-vertical > .btn-group:before,
.nav:before,
.navbar:before,
.navbar-header:before,
.navbar-collapse:before,
.pager:before,
.panel-body:before,
.modal-header:before,
.modal-footer:before,
.dl-horizontal dd:after,
.container:after,
.container-fluid:after,
.row:after,
.form-horizontal .form-group:after,
.btn-toolbar:after,
.btn-group-vertical > .btn-group:after,
.nav:after,
.navbar:after,
.navbar-header:after,
.navbar-collapse:after,
.pager:after,
.panel-body:after,
.modal-header:after,
.modal-footer:after {
  content: " ";
  display: table;
}
.clearfix:after,
.dl-horizontal dd:after,
.container:after,
.container-fluid:after,
.row:after,
.form-horizontal .form-group:after,
.btn-toolbar:after,
.btn-group-vertical > .btn-group:after,
.nav:after,
.navbar:after,
.navbar-header:after,
.navbar-collapse:after,
.pager:after,
.panel-body:after,
.modal-header:after,
.modal-footer:after {
  clear: both;
}
.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.pull-right {
  float: right !important;
}
.pull-left {
  float: left !important;
}
.hide {
  display: none !important;
}
.show {
  display: block !important;
}
.invisible {
  visibility: hidden;
}
.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.hidden {
  display: none !important;
}
.affix {
  position: fixed;
}
@-ms-viewport {
  width: device-width;
}
.visible-xs,
.visible-sm,
.visible-md,
.visible-lg {
  display: none !important;
}
.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block {
  display: none !important;
}
@media (max-width: 767px) {
  .visible-xs {
    display: block !important;
  }
  table.visible-xs {
    display: table !important;
  }
  tr.visible-xs {
    display: table-row !important;
  }
  th.visible-xs,
  td.visible-xs {
    display: table-cell !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-block {
    display: block !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-inline {
    display: inline !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm {
    display: block !important;
  }
  table.visible-sm {
    display: table !important;
  }
  tr.visible-sm {
    display: table-row !important;
  }
  th.visible-sm,
  td.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-block {
    display: block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline {
    display: inline !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md {
    display: block !important;
  }
  table.visible-md {
    display: table !important;
  }
  tr.visible-md {
    display: table-row !important;
  }
  th.visible-md,
  td.visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-block {
    display: block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline {
    display: inline !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg {
    display: block !important;
  }
  table.visible-lg {
    display: table !important;
  }
  tr.visible-lg {
    display: table-row !important;
  }
  th.visible-lg,
  td.visible-lg {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-block {
    display: block !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-inline {
    display: inline !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-inline-block {
    display: inline-block !important;
  }
}
@media (max-width: 767px) {
  .hidden-xs {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}
.visible-print {
  display: none !important;
}
@media print {
  .visible-print {
    display: block !important;
  }
  table.visible-print {
    display: table !important;
  }
  tr.visible-print {
    display: table-row !important;
  }
  th.visible-print,
  td.visible-print {
    display: table-cell !important;
  }
}
.visible-print-block {
  display: none !important;
}
@media print {
  .visible-print-block {
    display: block !important;
  }
}
.visible-print-inline {
  display: none !important;
}
@media print {
  .visible-print-inline {
    display: inline !important;
  }
}
.visible-print-inline-block {
  display: none !important;
}
@media print {
  .visible-print-inline-block {
    display: inline-block !important;
  }
}
@media print {
  .hidden-print {
    display: none !important;
  }
}
.navbar {
  border-width: 0;
}
.navbar-default .badge {
  background-color: white;
  color: #375a7f;
}
.navbar-inverse .badge {
  background-color: white;
  color: #00bc8c;
}
.navbar-brand {
  line-height: 1;
}
.navbar-form .form-control {
  background-color: white;
}
.navbar-form .form-control:focus {
  border-color: white;
}
.nav .nav-divider {
  background-color: #4d4d4d;
}
.btn {
  border-width: 2px;
}
.btn:active {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn-group.open .dropdown-toggle {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.text-primary,
.text-primary:hover {
  color: #4673a3;
}
.text-success,
.text-success:hover {
  color: #00bc8c;
}
.text-danger,
.text-danger:hover {
  color: #e74c3c;
}
.text-warning,
.text-warning:hover {
  color: #f39c12;
}
.text-info,
.text-info:hover {
  color: #3498db;
}
table a:not(.btn),
.table a:not(.btn) {
  text-decoration: underline;
}
table .dropdown-menu a,
.table .dropdown-menu a {
  text-decoration: none;
}
table .success,
.table .success,
table .warning,
.table .warning,
table .danger,
.table .danger,
table .info,
.table .info {
  color: white;
}
table .success > th > a,
.table .success > th > a,
table .warning > th > a,
.table .warning > th > a,
table .danger > th > a,
.table .danger > th > a,
table .info > th > a,
.table .info > th > a,
table .success > td > a,
.table .success > td > a,
table .warning > td > a,
.table .warning > td > a,
table .danger > td > a,
.table .danger > td > a,
table .info > td > a,
.table .info > td > a,
table .success > a,
.table .success > a,
table .warning > a,
.table .warning > a,
table .danger > a,
.table .danger > a,
table .info > a,
.table .info > a {
  color: white;
}
table > thead > tr > th,
.table > thead > tr > th,
table > tbody > tr > th,
.table > tbody > tr > th,
table > tfoot > tr > th,
.table > tfoot > tr > th,
table > thead > tr > td,
.table > thead > tr > td,
table > tbody > tr > td,
.table > tbody > tr > td,
table > tfoot > tr > td,
.table > tfoot > tr > td {
  border: none;
}
table-bordered > thead > tr > th,
.table-bordered > thead > tr > th,
table-bordered > tbody > tr > th,
.table-bordered > tbody > tr > th,
table-bordered > tfoot > tr > th,
.table-bordered > tfoot > tr > th,
table-bordered > thead > tr > td,
.table-bordered > thead > tr > td,
table-bordered > tbody > tr > td,
.table-bordered > tbody > tr > td,
table-bordered > tfoot > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #464545;
}
input,
textarea {
  color: #464545;
}
.form-control,
input,
textarea {
  border: 2px hidden transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.form-control:focus,
input:focus,
textarea:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline,
.has-warning.radio label,
.has-warning.checkbox label,
.has-warning.radio-inline label,
.has-warning.checkbox-inline label,
.has-warning .form-control-feedback {
  color: #f39c12;
}
.has-warning .form-control,
.has-warning .form-control:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.has-warning .input-group-addon {
  border-color: #f39c12;
}
.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline,
.has-error.radio label,
.has-error.checkbox label,
.has-error.radio-inline label,
.has-error.checkbox-inline label,
.has-error .form-control-feedback {
  color: #e74c3c;
}
.has-error .form-control,
.has-error .form-control:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.has-error .input-group-addon {
  border-color: #e74c3c;
}
.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline,
.has-success.radio label,
.has-success.checkbox label,
.has-success.radio-inline label,
.has-success.checkbox-inline label,
.has-success .form-control-feedback {
  color: #00bc8c;
}
.has-success .form-control,
.has-success .form-control:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.has-success .input-group-addon {
  border-color: #00bc8c;
}
.input-group-addon {
  color: white;
}
.nav .open > a,
.nav .open > a:hover,
.nav .open > a:focus {
  border-color: #464545;
}
.nav-tabs > li > a,
.nav-pills > li > a {
  color: white;
}
.pager a,
.pager a:hover {
  color: white;
}
.pager .disabled > a,
.pager .disabled > a:hover,
.pager .disabled > a:focus,
.pager .disabled > span {
  background-color: #007053;
}
.breadcrumb a {
  color: white;
}
.close {
  text-decoration: none;
  text-shadow: none;
  opacity: 0.4;
}
.close:hover,
.close:focus {
  opacity: 1;
}
.alert .alert-link {
  color: white;
  text-decoration: underline;
}
.progress {
  height: 10px;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.progress .progress-bar {
  font-size: 10px;
  line-height: 10px;
}
.well {
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.list-group-item.active,
a.list-group-item.active:hover,
a.list-group-item.active:focus {
  border-color: #464545;
}
a.list-group-item-success.active {
  background-color: #00bc8c;
}
a.list-group-item-success.active:hover,
a.list-group-item-success.active:focus {
  background-color: #00a379;
}
a.list-group-item-warning.active {
  background-color: #f39c12;
}
a.list-group-item-warning.active:hover,
a.list-group-item-warning.active:focus {
  background-color: #e08e0b;
}
a.list-group-item-danger.active {
  background-color: #e74c3c;
}
a.list-group-item-danger.active:hover,
a.list-group-item-danger.active:focus {
  background-color: #e43725;
}
.popover {
  color: white;
}
.panel-default > .panel-heading {
  background-color: #464545;
}
.page-header {
  margin-bottom: 8.5px;
}
.page-header h1 {
  margin-top: 0;
  margin-bottom: 4.25px;
}
.pagination {
  margin: 11px 0;
}
.toggle.btn {
  min-width: 44px !important;
  min-height: 26px !important;
}
.toggle.btn-lg {
  min-width: 50px !important;
  min-height: 30px !important;
}
.toggle.btn-sm {
  min-width: 40px !important;
  min-height: 24px !important;
}
.toggle.btn-xs {
  min-width: 35px !important;
  min-height: 22px !important;
}
.toggle-on.btn {
  padding-right: 16px !important;
}
.toggle-on.btn-lg {
  padding-right: 22px !important;
}
.toggle-on.btn-sm {
  padding-right: 10px !important;
}
.toggle-on.btn-xs {
  padding-right: 6px !important;
}
.toggle-off.btn {
  padding-left: 16px !important;
}
.toggle-off.btn-lg {
  padding-left: 22px !important;
}
.toggle-off.btn-sm {
  padding-left: 10px !important;
}
.toggle-off.btn-xs {
  padding-left: 6px !important;
}
.toggle-handle.btn-lg {
  width: 0 !important;
}
.page-header {
  margin-top: 0;
}
.page-header .btn {
  vertical-align: middle;
}
.page-header:before,
.page-header:after {
  content: " ";
  display: table;
}
.page-header:after {
  clear: both;
}
/**
 * Trumbowyg v2.18.0 - A lightweight WYSIWYG editor
 * Default stylesheet for Trumbowyg editor
 * ------------------------
 * @link http://alex-d.github.io/Trumbowyg
 * @license MIT
 * <AUTHOR> Demode (Alex-D)
 *         Twitter : @AlexandreDemode
 *         Website : alex-d.fr
 */

#trumbowyg-icons {
  overflow: hidden;
  visibility: hidden;
  height: 0;
  width: 0;
}
#trumbowyg-icons svg {
  height: 0;
  width: 0;
}
.trumbowyg-box *,
.trumbowyg-box *::before,
.trumbowyg-box *::after,
.trumbowyg-modal *,
.trumbowyg-modal *::before,
.trumbowyg-modal *::after {
  box-sizing: border-box;
}
.trumbowyg-box svg,
.trumbowyg-modal svg {
  width: 17px;
  height: 100%;
  fill: #222222;
  color: #222222;
}
.trumbowyg-box,
.trumbowyg-editor {
  display: block;
  position: relative;
  border: 1px solid #dddddd;
  width: 100%;
  min-height: 300px;
  margin: 17px auto;
}
.trumbowyg-box .trumbowyg-editor {
  margin: 0 auto;
}
.trumbowyg-box.trumbowyg-fullscreen {
  background: #fefefe;
  border: none !important;
}
.trumbowyg-editor,
.trumbowyg-textarea {
  position: relative;
  box-sizing: border-box;
  padding: 20px;
  min-height: 300px;
  width: 100%;
  border-style: none;
  resize: none;
  outline: none;
  overflow: auto;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
.trumbowyg-editor.trumbowyg-autogrow-on-enter,
.trumbowyg-textarea.trumbowyg-autogrow-on-enter {
  transition: height 300ms ease-out;
}
.trumbowyg-box-blur .trumbowyg-editor *,
.trumbowyg-box-blur .trumbowyg-editor::before {
  color: transparent !important;
  text-shadow: 0 0 7px #333333;
}
@media screen and (min-width: 0 \0) {
  .trumbowyg-box-blur .trumbowyg-editor *,
  .trumbowyg-box-blur .trumbowyg-editor::before {
    color: rgba(200, 200, 200, 0.6) !important;
  }
}
@supports (-ms-accelerator: true) {
  .trumbowyg-box-blur .trumbowyg-editor *,
  .trumbowyg-box-blur .trumbowyg-editor::before {
    color: rgba(200, 200, 200, 0.6) !important;
  }
}
.trumbowyg-box-blur .trumbowyg-editor img,
.trumbowyg-box-blur .trumbowyg-editor hr {
  opacity: 0.2;
}
.trumbowyg-textarea {
  position: relative;
  display: block;
  overflow: auto;
  border: none;
  font-size: 14px;
  font-family: "Inconsolata", "Consolas", "Courier", "Courier New", sans-serif;
  line-height: 18px;
}
.trumbowyg-box.trumbowyg-editor-visible .trumbowyg-textarea {
  height: 1px !important;
  width: 25%;
  min-height: 0 !important;
  padding: 0 !important;
  background: none;
  opacity: 0 !important;
}
.trumbowyg-box.trumbowyg-editor-hidden .trumbowyg-textarea {
  display: block;
}
.trumbowyg-box.trumbowyg-editor-hidden .trumbowyg-editor {
  display: none;
}
.trumbowyg-box.trumbowyg-disabled .trumbowyg-textarea {
  opacity: 0.8;
  background: none;
}
.trumbowyg-editor[contenteditable=true]:empty:not(:focus)::before {
  content: attr(placeholder);
  color: #999999;
  pointer-events: none;
}
.trumbowyg-button-pane {
  width: 100%;
  min-height: 36px;
  background: #ecf0f1;
  border-bottom: 1px solid #d7e0e2;
  margin: 0;
  padding: 0 5px;
  position: relative;
  list-style-type: none;
  line-height: 10px;
  backface-visibility: hidden;
  overflow: hidden;
  z-index: 11;
}
.trumbowyg-button-pane::after {
  content: " ";
  display: block;
  position: absolute;
  top: 36px;
  left: 0;
  right: 0;
  width: 100%;
  height: 1px;
  background: #d7e0e2;
}
.trumbowyg-button-pane .trumbowyg-button-group {
  display: inline-block;
}
.trumbowyg-button-pane .trumbowyg-button-group .trumbowyg-fullscreen-button svg {
  color: transparent;
}
.trumbowyg-button-pane .trumbowyg-button-group::after {
  content: " ";
  display: inline-block;
  width: 1px;
  background: #d7e0e2;
  margin: 0 5px;
  height: 35px;
  vertical-align: top;
}
.trumbowyg-button-pane .trumbowyg-button-group:last-child::after {
  content: none;
}
.trumbowyg-button-pane button {
  display: inline-block;
  position: relative;
  width: 35px;
  height: 35px;
  padding: 1px 6px !important;
  margin-bottom: 1px;
  overflow: hidden;
  border: none;
  cursor: pointer;
  background: none;
  vertical-align: middle;
  transition: background-color 150ms, opacity 150ms;
}
.trumbowyg-button-pane button.trumbowyg-textual-button {
  width: auto;
  line-height: 35px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.trumbowyg-button-pane.trumbowyg-disable button:not(.trumbowyg-not-disable):not(.trumbowyg-active),
.trumbowyg-button-pane button.trumbowyg-disable,
.trumbowyg-disabled .trumbowyg-button-pane button:not(.trumbowyg-not-disable):not(.trumbowyg-viewHTML-button) {
  opacity: 0.2;
  cursor: default;
}
.trumbowyg-button-pane.trumbowyg-disable .trumbowyg-button-group::before,
.trumbowyg-disabled .trumbowyg-button-pane .trumbowyg-button-group::before {
  background: #e3e9eb;
}
.trumbowyg-button-pane button:not(.trumbowyg-disable):hover,
.trumbowyg-button-pane button:not(.trumbowyg-disable):focus,
.trumbowyg-button-pane button.trumbowyg-active {
  background-color: white;
  outline: none;
}
.trumbowyg-button-pane .trumbowyg-open-dropdown::after {
  display: block;
  content: " ";
  position: absolute;
  top: 25px;
  right: 3px;
  height: 0;
  width: 0;
  border: 3px solid transparent;
  border-top-color: #555555;
}
.trumbowyg-button-pane .trumbowyg-open-dropdown.trumbowyg-textual-button {
  color: #222222;
  padding-left: 10px !important;
  padding-right: 18px !important;
}
.trumbowyg-button-pane .trumbowyg-open-dropdown.trumbowyg-textual-button::after {
  top: 17px;
  right: 7px;
}
.trumbowyg-button-pane .trumbowyg-right {
  float: right;
}
.trumbowyg-dropdown {
  max-width: 300px;
  max-height: 250px;
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px solid #ecf0f1;
  padding: 5px 0;
  border-top: none;
  background: white;
  margin-left: -1px;
  box-shadow: rgba(0, 0, 0, 0.1) 0 2px 3px;
  z-index: 12;
}
.trumbowyg-dropdown button {
  display: block;
  width: 100%;
  height: 35px;
  line-height: 35px;
  text-decoration: none;
  background: white;
  padding: 0 20px 0 10px;
  color: #333333 !important;
  border: none;
  cursor: pointer;
  text-align: left;
  white-space: nowrap;
  font-size: 15px;
  transition: all 150ms;
}
.trumbowyg-dropdown button:hover,
.trumbowyg-dropdown button:focus {
  background: #ecf0f1;
}
.trumbowyg-dropdown button svg {
  float: left;
  margin-right: 14px;
}
/* Modal box */

.trumbowyg-modal {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  max-width: 520px;
  width: 100%;
  height: 350px;
  z-index: 12;
  overflow: hidden;
  backface-visibility: hidden;
}
.trumbowyg-modal-box {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  max-width: 500px;
  width: calc(80%);
  padding-bottom: 45px;
  z-index: 1;
  background-color: white;
  text-align: center;
  font-size: 14px;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 3px;
  backface-visibility: hidden;
}
.trumbowyg-modal-box .trumbowyg-modal-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 20px;
  padding: 15px 0 13px;
  display: block;
  border-bottom: 1px solid #eeeeee;
  color: #333333;
  background: #fbfcfc;
}
.trumbowyg-modal-box .trumbowyg-progress {
  width: 100%;
  height: 3px;
  position: absolute;
  top: 58px;
}
.trumbowyg-modal-box .trumbowyg-progress .trumbowyg-progress-bar {
  background: #2bc06a;
  width: 0;
  height: 100%;
  transition: width 150ms linear;
}
.trumbowyg-modal-box label {
  display: block;
  position: relative;
  margin: 15px 12px;
  height: 29px;
  line-height: 29px;
  overflow: hidden;
}
.trumbowyg-modal-box label .trumbowyg-input-infos {
  display: block;
  text-align: left;
  height: 25px;
  line-height: 25px;
  transition: all 150ms;
}
.trumbowyg-modal-box label .trumbowyg-input-infos span {
  display: block;
  color: #69878f;
  background-color: #fbfcfc;
  border: 1px solid #dedede;
  padding: 0 7px;
  width: 150px;
}
.trumbowyg-modal-box label .trumbowyg-input-infos span.trumbowyg-msg-error {
  color: #e74c3c;
}
.trumbowyg-modal-box label.trumbowyg-input-error input,
.trumbowyg-modal-box label.trumbowyg-input-error textarea {
  border: 1px solid #e74c3c;
}
.trumbowyg-modal-box label.trumbowyg-input-error .trumbowyg-input-infos {
  margin-top: -27px;
}
.trumbowyg-modal-box label input {
  position: absolute;
  top: 0;
  right: 0;
  height: 27px;
  line-height: 27px;
  border: 1px solid #dedede;
  background: white;
  font-size: 14px;
  max-width: 330px;
  width: 70%;
  padding: 0 7px;
  transition: all 150ms;
}
.trumbowyg-modal-box label input:hover,
.trumbowyg-modal-box label input:focus {
  outline: none;
  border: 1px solid #95a5a6;
}
.trumbowyg-modal-box label input:focus {
  background: #fbfcfc;
}
.trumbowyg-modal-box label input[type="checkbox"] {
  left: 6px;
  top: 6px;
  right: auto;
  height: 16px;
  width: 16px;
}
.trumbowyg-modal-box label input[type="checkbox"] + .trumbowyg-input-infos span {
  width: auto;
  padding-left: 25px;
}
.trumbowyg-modal-box .error {
  margin-top: 25px;
  display: block;
  color: red;
}
.trumbowyg-modal-box .trumbowyg-modal-button {
  position: absolute;
  bottom: 10px;
  right: 0;
  text-decoration: none;
  color: white;
  display: block;
  width: 100px;
  height: 35px;
  line-height: 33px;
  margin: 0 10px;
  background-color: #333333;
  border: none;
  cursor: pointer;
  font-family: "Trebuchet MS", Helvetica, Verdana, sans-serif;
  font-size: 16px;
  transition: all 150ms;
}
.trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-submit {
  right: 110px;
  background: #2bc06a;
}
.trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-submit:hover,
.trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-submit:focus {
  background: #40d47e;
  outline: none;
}
.trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-submit:active {
  background: #25a25a;
}
.trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-reset {
  color: #555555;
  background: #e6e6e6;
}
.trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-reset:hover,
.trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-reset:focus {
  background: #fbfbfb;
  outline: none;
}
.trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-reset:active {
  background: #d5d5d5;
}
.trumbowyg-overlay {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.5);
  height: 100%;
  width: 100%;
  left: 0;
  display: none;
  top: 0;
  z-index: 10;
}
/**
 * Fullscreen
 */

body.trumbowyg-body-fullscreen {
  overflow: hidden;
}
.trumbowyg-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  z-index: 99999;
}
.trumbowyg-fullscreen.trumbowyg-box,
.trumbowyg-fullscreen .trumbowyg-editor {
  border: none;
}
.trumbowyg-fullscreen .trumbowyg-editor,
.trumbowyg-fullscreen .trumbowyg-textarea {
  height: calc(63%) !important;
  overflow: auto;
}
.trumbowyg-fullscreen .trumbowyg-overlay {
  height: 100% !important;
}
.trumbowyg-fullscreen .trumbowyg-button-group .trumbowyg-fullscreen-button svg {
  color: #222222;
  fill: transparent;
}
.trumbowyg-editor object,
.trumbowyg-editor embed,
.trumbowyg-editor video,
.trumbowyg-editor img {
  max-width: 100%;
}
.trumbowyg-editor video,
.trumbowyg-editor img {
  height: auto;
}
.trumbowyg-editor img {
  cursor: move;
}
.trumbowyg-editor.trumbowyg-reset-css {
  background: #fefefe !important;
  font-family: "Trebuchet MS", Helvetica, Verdana, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.45em !important;
  color: #333333;
}
.trumbowyg-editor.trumbowyg-reset-css a {
  color: #1155cc !important;
  text-decoration: underline !important;
}
.trumbowyg-editor.trumbowyg-reset-css div,
.trumbowyg-editor.trumbowyg-reset-css p,
.trumbowyg-editor.trumbowyg-reset-css ul,
.trumbowyg-editor.trumbowyg-reset-css ol,
.trumbowyg-editor.trumbowyg-reset-css blockquote {
  box-shadow: none !important;
  background: none !important;
  margin: 0 !important;
  margin-bottom: 15px !important;
  line-height: 1.4em !important;
  font-family: "Trebuchet MS", Helvetica, Verdana, sans-serif !important;
  font-size: 14px !important;
  border: none;
}
.trumbowyg-editor.trumbowyg-reset-css iframe,
.trumbowyg-editor.trumbowyg-reset-css object,
.trumbowyg-editor.trumbowyg-reset-css hr {
  margin-bottom: 15px !important;
}
.trumbowyg-editor.trumbowyg-reset-css blockquote {
  margin-left: 32px !important;
  font-style: italic !important;
  color: #555555;
}
.trumbowyg-editor.trumbowyg-reset-css ul {
  list-style: disc;
}
.trumbowyg-editor.trumbowyg-reset-css ul,
.trumbowyg-editor.trumbowyg-reset-css ol {
  padding-left: 20px !important;
}
.trumbowyg-editor.trumbowyg-reset-css ul ul,
.trumbowyg-editor.trumbowyg-reset-css ol ol,
.trumbowyg-editor.trumbowyg-reset-css ul ol,
.trumbowyg-editor.trumbowyg-reset-css ol ul {
  border: none;
  margin: 2px !important;
  padding: 0 !important;
  padding-left: 24px !important;
}
.trumbowyg-editor.trumbowyg-reset-css hr {
  display: block;
  height: 1px;
  border: none;
  border-top: 1px solid #cccccc;
}
.trumbowyg-editor.trumbowyg-reset-css h1,
.trumbowyg-editor.trumbowyg-reset-css h2,
.trumbowyg-editor.trumbowyg-reset-css h3,
.trumbowyg-editor.trumbowyg-reset-css h4 {
  color: #111111;
  background: none;
  margin: 0 !important;
  padding: 0 !important;
  font-weight: bold;
}
.trumbowyg-editor.trumbowyg-reset-css h1 {
  font-size: 32px !important;
  line-height: 38px !important;
  margin-bottom: 20px !important;
}
.trumbowyg-editor.trumbowyg-reset-css h2 {
  font-size: 26px !important;
  line-height: 34px !important;
  margin-bottom: 15px !important;
}
.trumbowyg-editor.trumbowyg-reset-css h3 {
  font-size: 22px !important;
  line-height: 28px !important;
  margin-bottom: 7px !important;
}
.trumbowyg-editor.trumbowyg-reset-css h4 {
  font-size: 16px !important;
  line-height: 22px !important;
  margin-bottom: 7px !important;
}
/*
 * Dark theme
 */

.trumbowyg-dark .trumbowyg-textarea {
  background: #111111;
  color: #dddddd;
}
.trumbowyg-dark .trumbowyg-box {
  border: 1px solid #343434;
}
.trumbowyg-dark .trumbowyg-box.trumbowyg-fullscreen {
  background: #111111;
}
.trumbowyg-dark .trumbowyg-box.trumbowyg-box-blur .trumbowyg-editor *,
.trumbowyg-dark .trumbowyg-box.trumbowyg-box-blur .trumbowyg-editor::before {
  text-shadow: 0 0 7px #cccccc;
}
@media screen and (min-width: 0 \0 ) {
  .trumbowyg-dark .trumbowyg-box.trumbowyg-box-blur .trumbowyg-editor *,
  .trumbowyg-dark .trumbowyg-box.trumbowyg-box-blur .trumbowyg-editor::before {
    color: rgba(20, 20, 20, 0.6) !important;
  }
}
@supports (-ms-accelerator: true) {
  .trumbowyg-dark .trumbowyg-box.trumbowyg-box-blur .trumbowyg-editor *,
  .trumbowyg-dark .trumbowyg-box.trumbowyg-box-blur .trumbowyg-editor::before {
    color: rgba(20, 20, 20, 0.6) !important;
  }
}
.trumbowyg-dark .trumbowyg-box svg {
  fill: #ecf0f1;
  color: #ecf0f1;
}
.trumbowyg-dark .trumbowyg-button-pane {
  background-color: #222222;
  border-bottom-color: #343434;
}
.trumbowyg-dark .trumbowyg-button-pane::after {
  background: #343434;
}
.trumbowyg-dark .trumbowyg-button-pane .trumbowyg-button-group:not(:empty)::after {
  background-color: #343434;
}
.trumbowyg-dark .trumbowyg-button-pane .trumbowyg-button-group:not(:empty) .trumbowyg-fullscreen-button svg {
  color: transparent;
}
.trumbowyg-dark .trumbowyg-button-pane.trumbowyg-disable .trumbowyg-button-group::after {
  background-color: #2a2a2a;
}
.trumbowyg-dark .trumbowyg-button-pane button:not(.trumbowyg-disable):hover,
.trumbowyg-dark .trumbowyg-button-pane button:not(.trumbowyg-disable):focus,
.trumbowyg-dark .trumbowyg-button-pane button.trumbowyg-active {
  background-color: #333333;
}
.trumbowyg-dark .trumbowyg-button-pane .trumbowyg-open-dropdown::after {
  border-top-color: white;
}
.trumbowyg-dark .trumbowyg-fullscreen .trumbowyg-button-pane .trumbowyg-button-group:not(:empty) .trumbowyg-fullscreen-button svg {
  color: #ecf0f1;
  fill: transparent;
}
.trumbowyg-dark .trumbowyg-dropdown {
  border-color: #222222;
  background: #333333;
  box-shadow: rgba(0, 0, 0, 0.3) 0 2px 3px;
}
.trumbowyg-dark .trumbowyg-dropdown button {
  background: #333333;
  color: white !important;
}
.trumbowyg-dark .trumbowyg-dropdown button:hover,
.trumbowyg-dark .trumbowyg-dropdown button:focus {
  background: #222222;
}
.trumbowyg-dark .trumbowyg-modal-box {
  background-color: #222222;
}
.trumbowyg-dark .trumbowyg-modal-box .trumbowyg-modal-title {
  border-bottom: 1px solid #555555;
  color: white;
  background: #3c3c3c;
}
.trumbowyg-dark .trumbowyg-modal-box label {
  display: block;
  position: relative;
  margin: 15px 12px;
  height: 27px;
  line-height: 27px;
  overflow: hidden;
}
.trumbowyg-dark .trumbowyg-modal-box label .trumbowyg-input-infos span {
  color: #eeeeee;
  background-color: #2f2f2f;
  border-color: #222222;
}
.trumbowyg-dark .trumbowyg-modal-box label .trumbowyg-input-infos span.trumbowyg-msg-error {
  color: #e74c3c;
}
.trumbowyg-dark .trumbowyg-modal-box label.trumbowyg-input-error input,
.trumbowyg-dark .trumbowyg-modal-box label.trumbowyg-input-error textarea {
  border-color: #e74c3c;
}
.trumbowyg-dark .trumbowyg-modal-box label input {
  border-color: #222222;
  color: #eeeeee;
  background: #333333;
}
.trumbowyg-dark .trumbowyg-modal-box label input:hover,
.trumbowyg-dark .trumbowyg-modal-box label input:focus {
  border-color: #626262;
}
.trumbowyg-dark .trumbowyg-modal-box label input:focus {
  background-color: #2f2f2f;
}
.trumbowyg-dark .trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-submit {
  background: #1b7943;
}
.trumbowyg-dark .trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-submit:hover,
.trumbowyg-dark .trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-submit:focus {
  background: #25a25a;
}
.trumbowyg-dark .trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-submit:active {
  background: #176437;
}
.trumbowyg-dark .trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-reset {
  background: #333333;
  color: #cccccc;
}
.trumbowyg-dark .trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-reset:hover,
.trumbowyg-dark .trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-reset:focus {
  background: #444444;
}
.trumbowyg-dark .trumbowyg-modal-box .trumbowyg-modal-button.trumbowyg-modal-reset:active {
  background: #111111;
}
.trumbowyg-dark .trumbowyg-overlay {
  background-color: rgba(15, 15, 15, 0.6);
}
/**
 * Trumbowyg v2.18.0 - A lightweight WYSIWYG editor
 * Trumbowyg plugin stylesheet
 * ------------------------
 * @link http://alex-d.github.io/Trumbowyg
 * @license MIT
 * <AUTHOR> Demode (Alex-D)
 *         Twitter : @AlexandreDemode
 *         Website : alex-d.fr
 */

.trumbowyg-dropdown-foreColor:not(.trumbowyg-dropdown--color-list),
.trumbowyg-dropdown-backColor:not(.trumbowyg-dropdown--color-list) {
  max-width: 276px;
  padding: 7px 5px;
  overflow: initial;
}
.trumbowyg-dropdown-foreColor:not(.trumbowyg-dropdown--color-list) button,
.trumbowyg-dropdown-backColor:not(.trumbowyg-dropdown--color-list) button {
  display: block;
  position: relative;
  float: left;
  text-indent: -9999px;
  height: 20px;
  width: 20px;
  border: 1px solid #333333;
  padding: 0;
  margin: 2px;
}
.trumbowyg-dropdown-foreColor:not(.trumbowyg-dropdown--color-list) button:hover::after,
.trumbowyg-dropdown-foreColor:not(.trumbowyg-dropdown--color-list) button:focus::after,
.trumbowyg-dropdown-backColor:not(.trumbowyg-dropdown--color-list) button:hover::after,
.trumbowyg-dropdown-backColor:not(.trumbowyg-dropdown--color-list) button:focus::after {
  content: " ";
  display: block;
  position: absolute;
  top: -5px;
  left: -5px;
  width: 27px;
  height: 27px;
  background: inherit;
  border: 1px solid white;
  box-shadow: black 0 0 2px;
  z-index: 10;
}
.trumbowyg-dropdown-backColor.trumbowyg-dropdown--color-list button:not(.trumbowyg-backColorRemove-dropdown-button) {
  position: relative;
  color: white !important;
}
.trumbowyg-dropdown-backColor.trumbowyg-dropdown--color-list button:not(.trumbowyg-backColorRemove-dropdown-button):hover::after,
.trumbowyg-dropdown-backColor.trumbowyg-dropdown--color-list button:not(.trumbowyg-backColorRemove-dropdown-button):focus::after {
  content: " ";
  display: block;
  position: absolute;
  top: 13px;
  left: 0;
  width: 0;
  height: 0;
  border: 5px solid transparent;
  border-left-color: white;
}
/**
 * Trumbowyg v2.18.0 - A lightweight WYSIWYG editor
 * Trumbowyg plugin stylesheet
 * ------------------------
 * @link http://alex-d.github.io/Trumbowyg
 * @license MIT
 * <AUTHOR> Demode (Alex-D)
 *         Twitter : @AlexandreDemode
 *         Website : alex-d.fr
 */

.trumbowyg-symbol-\&nbsp-dropdown-button {
  display: none !important;
}
.trumbowyg-symbol-\&nbsp-dropdown-button + button {
  clear: both;
}
.trumbowyg-dropdown-specialChars {
  width: 248px;
  padding: 5px 3px 3px;
}
.trumbowyg-dropdown-specialChars button {
  display: block;
  position: relative;
  float: left;
  height: 26px;
  width: 26px;
  padding: 0;
  margin: 2px;
  line-height: 24px;
  text-align: center;
}
.trumbowyg-dropdown-specialChars button:hover::after,
.trumbowyg-dropdown-specialChars button:focus::after {
  display: block;
  position: absolute;
  top: -5px;
  left: -5px;
  height: 27px;
  width: 27px;
  background: inherit;
  box-shadow: black 0 0 2px;
  z-index: 10;
  background-color: transparent;
}
.trumbowyg .specialChars {
  width: 22px;
  height: 22px;
  display: inline-block;
}
/**
 * Trumbowyg v2.18.0 - A lightweight WYSIWYG editor
 * Trumbowyg plugin stylesheet
 * ------------------------
 * @link http://alex-d.github.io/Trumbowyg
 * @license MIT
 * <AUTHOR> Demode (Alex-D)
 *         Twitter : @AlexandreDemode
 *         Website : alex-d.fr
 */

.trumbowyg-editor table {
  background-color: white;
  width: 100%;
}
.trumbowyg-editor table td {
  border: 1px dotted #e7eaec !important;
  background-color: white;
  color: #333333;
  padding: 8px;
}
.trumbowyg-dropdown-table table {
  margin: 10px;
  display: inline-block;
}
.trumbowyg-dropdown-table table td {
  display: inline-block;
  height: 20px;
  width: 20px;
  margin: 1px;
  padding: 0;
  background-color: white;
  box-shadow: 0 0 0 1px #cecece inset;
}
.trumbowyg-dropdown-table table td.active {
  background-color: #00b393;
  box-shadow: none;
  cursor: pointer;
}
.trumbowyg-dropdown-table .trumbowyg-table-size {
  text-align: center;
}
/**
 * Trumbowyg v2.18.0 - A lightweight WYSIWYG editor
 * Trumbowyg plugin stylesheet
 * ------------------------
 * @link http://alex-d.github.io/Trumbowyg
 * @license MIT
 * <AUTHOR> Demode (Alex-D)
 *         Twitter : @AlexandreDemode
 *         Website : alex-d.fr
 */

.trumbowyg-dropdown-emoji {
  width: 265px;
  padding: 7px 0 7px 5px;
}
.trumbowyg-dropdown-emoji svg {
  display: none !important;
}
.trumbowyg-dropdown-emoji button {
  display: block;
  position: relative;
  float: left;
  height: 26px;
  width: 26px;
  padding: 0;
  margin: 2px;
  line-height: 24px;
  text-align: center;
}
.trumbowyg-dropdown-emoji button:hover::after,
.trumbowyg-dropdown-emoji button:focus::after {
  display: block;
  position: absolute;
  top: -5px;
  left: -5px;
  height: 27px;
  width: 27px;
  background: inherit;
  box-shadow: black 0 0 2px;
  z-index: 10;
  background-color: transparent;
}
.trumbowyg .emoji {
  width: 22px;
  height: 22px;
  display: inline-block;
}
/*!
 * bootstrap-fileinput v4.4.7
 * http://plugins.krajee.com/file-input
 *
 * Krajee default styling for bootstrap-fileinput.
 *
 * Author: Kartik Visweswaran
 * Copyright: 2014 - 2017, Kartik Visweswaran, Krajee.com
 *
 * Licensed under the BSD 3-Clause
 * https://github.com/kartik-v/bootstrap-fileinput/blob/master/LICENSE.md
 */

.file-loading input[type=file],
input[type=file].file-loading {
  width: 0;
  height: 0;
}
.kv-hidden,
.file-caption-icon,
.file-zoom-dialog .modal-header:before,
.file-zoom-dialog .modal-header:after,
.file-input-new .file-preview,
.file-input-new .close,
.file-input-new .glyphicon-file,
.file-input-new .fileinput-remove-button,
.file-input-new .fileinput-upload-button,
.file-input-new .no-browse .input-group-btn,
.file-input-ajax-new .fileinput-remove-button,
.file-input-ajax-new .fileinput-upload-button,
.file-input-ajax-new .no-browse .input-group-btn,
.hide-content .kv-file-content {
  display: none;
}
.btn-file input[type=file],
.file-caption-icon,
.file-preview .fileinput-remove,
.krajee-default .file-thumb-progress,
.file-zoom-dialog .btn-navigate,
.file-zoom-dialog .floating-buttons {
  position: absolute;
}
.file-loading:before,
.btn-file,
.file-caption,
.file-preview,
.krajee-default.file-preview-frame,
.krajee-default .file-thumbnail-footer,
.file-zoom-dialog .modal-dialog {
  position: relative;
}
.file-error-message pre,
.file-error-message ul,
.krajee-default .file-actions,
.krajee-default .file-other-error {
  text-align: left;
}
.file-error-message pre,
.file-error-message ul {
  margin: 0;
}
.krajee-default .file-drag-handle,
.krajee-default .file-upload-indicator {
  float: left;
  margin: 5px 0 -5px;
  width: 16px;
  height: 16px;
}
.krajee-default .file-thumb-progress .progress,
.krajee-default .file-thumb-progress .progress-bar {
  height: 11px;
  font-size: 9px;
  line-height: 10px;
}
.krajee-default .file-caption-info,
.krajee-default .file-size-info {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 160px;
  height: 15px;
  margin: auto;
}
.file-zoom-content > .file-object.type-video,
.file-zoom-content > .file-object.type-flash,
.file-zoom-content > .file-object.type-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
}
.file-zoom-content > .file-object.type-video,
.file-zoom-content > .file-object.type-flash {
  height: 100%;
}
.file-zoom-content > .file-object.type-pdf,
.file-zoom-content > .file-object.type-html,
.file-zoom-content > .file-object.type-text,
.file-zoom-content > .file-object.type-default {
  width: 100%;
}
.rotate-2 {
  transform: rotateY(180deg);
}
.rotate-3 {
  transform: rotate(180deg);
}
.rotate-4 {
  transform: rotate(180deg) rotateY(180deg);
}
.rotate-5 {
  transform: rotate(270deg) rotateY(180deg);
}
.rotate-6 {
  transform: rotate(90deg);
}
.rotate-7 {
  transform: rotate(90deg) rotateY(180deg);
}
.rotate-8 {
  transform: rotate(270deg);
}
.file-loading:before {
  content: " Loading...";
  display: inline-block;
  padding-left: 20px;
  line-height: 16px;
  font-size: 13px;
  font-variant: small-caps;
  color: #999999;
  background: transparent url(../img/loading.gif) top left no-repeat;
}
.file-object {
  margin: 0 0 -5px 0;
  padding: 0;
}
.btn-file {
  overflow: hidden;
}
.btn-file input[type=file] {
  top: 0;
  right: 0;
  min-width: 100%;
  min-height: 100%;
  text-align: right;
  opacity: 0;
  background: none repeat scroll 0 0 transparent;
  cursor: inherit;
  display: block;
}
.btn-file ::-ms-browse {
  font-size: 10000px;
  width: 100%;
  height: 100%;
}
.file-caption .file-caption-name {
  width: 100%;
  margin: 0;
  padding: 0;
  box-shadow: none;
  border: none;
  background: none;
  outline: none;
}
.file-caption.icon-visible .file-caption-icon {
  display: inline-block;
}
.file-caption.icon-visible .file-caption-name {
  padding-left: 15px;
}
.file-caption-icon {
  line-height: 1;
  left: 8px;
}
.file-error-message {
  color: #a94442;
  background-color: #f2dede;
  margin: 5px;
  border: 1px solid #ebccd1;
  border-radius: 4px;
  padding: 15px;
}
.file-error-message pre {
  margin: 5px 0;
}
.file-caption-disabled {
  background-color: #eeeeee;
  cursor: not-allowed;
  opacity: 1;
}
.file-preview {
  border-radius: 5px;
  border: 1px solid #dddddd;
  padding: 8px;
  width: 100%;
  margin-bottom: 5px;
}
.file-preview .btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.file-preview .fileinput-remove {
  top: 1px;
  right: 1px;
  line-height: 10px;
}
.file-preview .clickable {
  cursor: pointer;
}
.file-preview-image {
  font: 40px Impact, Charcoal, sans-serif;
  color: green;
}
.krajee-default.file-preview-frame {
  margin: 8px;
  border: 1px solid #dddddd;
  box-shadow: 1px 1px 5px 0 #a2958a;
  padding: 6px;
  float: left;
  text-align: center;
}
.krajee-default.file-preview-frame .kv-file-content {
  width: 213px;
  height: 160px;
}
.krajee-default.file-preview-frame .file-thumbnail-footer {
  height: 70px;
}
.krajee-default.file-preview-frame:not(.file-preview-error):hover {
  box-shadow: 3px 3px 5px 0 #333333;
}
.krajee-default .file-preview-text {
  display: block;
  color: #428bca;
  border: 1px solid #dddddd;
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
  outline: none;
  padding: 8px;
  resize: none;
}
.krajee-default .file-preview-html {
  border: 1px solid #dddddd;
  padding: 8px;
  overflow: auto;
}
.krajee-default .file-other-icon {
  font-size: 6em;
}
.krajee-default .file-footer-buttons {
  float: right;
}
.krajee-default .file-footer-caption {
  display: block;
  text-align: center;
  padding-top: 4px;
  font-size: 11px;
  color: #777777;
  margin-bottom: 15px;
}
.krajee-default .file-preview-error {
  opacity: 0.65;
  box-shadow: none;
}
.krajee-default .file-thumb-progress {
  height: 11px;
  top: 37px;
  left: 0;
  right: 0;
}
.krajee-default.kvsortable-ghost {
  background: #e1edf7;
  border: 2px solid #a1abff;
}
.krajee-default .file-preview-other:hover {
  opacity: 0.8;
}
.krajee-default .file-preview-frame:not(.file-preview-error) .file-footer-caption:hover {
  color: black;
}
.kv-upload-progress .progress {
  height: 20px;
  line-height: 20px;
  margin: 10px 0;
  overflow: hidden;
}
.kv-upload-progress .progress-bar {
  height: 20px;
  line-height: 20px;
}
/*noinspection CssOverwrittenProperties*/

.file-zoom-dialog .file-other-icon {
  font-size: 22em;
  font-size: 50vmin;
}
.file-zoom-dialog .modal-dialog {
  width: auto;
}
.file-zoom-dialog .modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.file-zoom-dialog .btn-navigate {
  padding: 0;
  margin: 0;
  background: transparent;
  text-decoration: none;
  outline: none;
  opacity: 0.7;
  top: 45%;
  font-size: 4em;
  color: #1c94c4;
}
.file-zoom-dialog .btn-navigate:not([disabled]):hover {
  outline: none;
  box-shadow: none;
  opacity: 0.6;
}
.file-zoom-dialog .floating-buttons {
  top: 5px;
  right: 10px;
}
.file-zoom-dialog .btn-navigate[disabled] {
  opacity: 0.3;
}
.file-zoom-dialog .btn-prev {
  left: 1px;
}
.file-zoom-dialog .btn-next {
  right: 1px;
}
.file-zoom-dialog .kv-zoom-title {
  font-weight: 300;
  color: #999999;
  max-width: 50%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.file-input-new .no-browse .form-control {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.file-input-ajax-new .no-browse .form-control {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.file-caption-main {
  width: 100%;
}
.file-thumb-loading {
  background: transparent url(../img/loading.gif) no-repeat scroll center center content-box !important;
}
.file-drop-zone {
  border: 1px dashed #aaaaaa;
  border-radius: 4px;
  height: 100%;
  text-align: center;
  vertical-align: middle;
  margin: 12px 15px 12px 12px;
  padding: 5px;
}
.file-drop-zone.clickable:hover {
  border: 2px dashed #999999;
}
.file-drop-zone.clickable:focus {
  border: 2px solid #5acde2;
}
.file-drop-zone .file-preview-thumbnails {
  cursor: default;
}
.file-drop-zone-title {
  color: #aaaaaa;
  font-size: 1.6em;
  padding: 85px 10px;
  cursor: default;
}
.file-highlighted {
  border: 2px dashed #999999 !important;
  background-color: #eeeeee;
}
.file-uploading {
  background: url(../img/loading-sm.gif) no-repeat center bottom 10px;
  opacity: 0.65;
}
@media (min-width: 576px) {
  .file-zoom-dialog .modal-dialog {
    max-width: 500px;
  }
}
@media (min-width: 992px) {
  .file-zoom-dialog .modal-lg {
    max-width: 800px;
  }
}
.file-zoom-fullscreen.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.file-zoom-fullscreen .modal-dialog {
  position: fixed;
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}
.file-zoom-fullscreen .modal-content {
  border-radius: 0;
  box-shadow: none;
}
.file-zoom-fullscreen .modal-body {
  overflow-y: auto;
}
.btn-kv {
  display: inline-block;
  text-align: center;
  width: 30px;
  height: 30px;
  line-height: 30px;
  padding: 0;
  font-size: 90%;
  border-radius: 0.2rem;
}
.floating-buttons {
  z-index: 3000;
}
.floating-buttons .btn-kv {
  margin-left: 3px;
  z-index: 3000;
}
.file-zoom-content {
  height: 480px;
  text-align: center;
}
.file-zoom-content .file-preview-image {
  max-height: 100%;
}
.file-zoom-content .file-preview-video {
  max-height: 100%;
}
.file-zoom-content .is-portrait-gt4 {
  margin-top: 60px;
}
.file-zoom-content > .file-object.type-image {
  height: auto;
  min-height: inherit;
}
.file-zoom-content > .file-object.type-audio {
  width: auto;
  height: 30px;
}
@media screen and (max-width: 767px) {
  .file-preview-thumbnails {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }
  .file-zoom-dialog .modal-header {
    flex-direction: column;
  }
}
@media screen and (max-width: 350px) {
  .krajee-default.file-preview-frame .kv-file-content {
    width: 160px;
  }
}
.file-loading[dir=rtl]:before {
  background: transparent url(../img/loading.gif) top right no-repeat;
  padding-left: 0;
  padding-right: 20px;
}
.file-sortable .file-drag-handle {
  cursor: move;
  opacity: 1;
}
.file-sortable .file-drag-handle:hover {
  opacity: 0.7;
}
.clickable .file-drop-zone-title {
  cursor: pointer;
}
.kv-zoom-actions .btn-kv {
  margin-left: 3px;
}
.file-preview-initial.sortable-chosen {
  background-color: #d9edf7;
}
/*!
 * bootstrap-fileinput v4.4.7
 * http://plugins.krajee.com/file-input
 *
 * Krajee Explorer Font Awesome theme style for bootstrap-fileinput. Load this theme file after loading `fileinput.css`.
 *
 * Author: Kartik Visweswaran
 * Copyright: 2014 - 2017, Kartik Visweswaran, Krajee.com
 *
 * Licensed under the BSD 3-Clause
 * https://github.com/kartik-v/bootstrap-fileinput/blob/master/LICENSE.md
 */

.theme-explorer-fa .file-upload-indicator,
.theme-explorer-fa .file-drag-handle,
.theme-explorer-fa .explorer-frame .kv-file-content,
.theme-explorer-fa .file-actions,
.explorer-frame .file-preview-other {
  text-align: center;
}
.theme-explorer-fa .file-thumb-progress .progress,
.theme-explorer-fa .file-thumb-progress .progress-bar {
  height: 13px;
  font-size: 11px;
  line-height: 13px;
}
.theme-explorer-fa .file-upload-indicator,
.theme-explorer-fa .file-drag-handle {
  position: absolute;
  display: inline-block;
  top: 0;
  right: 3px;
  width: 16px;
  height: 16px;
  font-size: 16px;
}
.theme-explorer-fa .file-thumb-progress .progress,
.theme-explorer-fa .explorer-caption {
  display: block;
}
.theme-explorer-fa .explorer-frame td {
  vertical-align: middle;
  text-align: left;
}
.theme-explorer-fa .explorer-frame .kv-file-content {
  width: 80px;
  height: 80px;
  padding: 5px;
}
.theme-explorer-fa .file-actions-cell {
  position: relative;
  width: 120px;
  padding: 0;
}
.theme-explorer-fa .file-thumb-progress .progress {
  margin-top: 5px;
}
.theme-explorer-fa .explorer-caption {
  color: #777777;
}
.theme-explorer-fa .kvsortable-ghost {
  opacity: 0.6;
  background: #e1edf7;
  border: 2px solid #a1abff;
}
.theme-explorer-fa .file-preview .table {
  margin: 0;
}
.theme-explorer-fa .file-error-message ul {
  padding: 5px 0 0 20px;
}
.explorer-frame .file-preview-text {
  display: inline-block;
  color: #428bca;
  border: 1px solid #dddddd;
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
  outline: none;
  padding: 8px;
  resize: none;
}
.explorer-frame .file-preview-html {
  display: inline-block;
  border: 1px solid #dddddd;
  padding: 8px;
  overflow: auto;
}
.explorer-frame .file-other-icon {
  font-size: 2.6em;
}
@media only screen and (max-width: 767px) {
  .theme-explorer-fa .table,
  .theme-explorer-fa .table tbody,
  .theme-explorer-fa .table tr,
  .theme-explorer-fa .table td {
    display: block;
    width: 100% !important;
  }
  .theme-explorer-fa .table {
    border: none;
  }
  .theme-explorer-fa .table tr {
    margin-top: 5px;
  }
  .theme-explorer-fa .table tr:first-child {
    margin-top: 0;
  }
  .theme-explorer-fa .table td {
    text-align: center;
  }
  .theme-explorer-fa .table .kv-file-content {
    border-bottom: none;
    padding: 4px;
    margin: 0;
  }
  .theme-explorer-fa .table .kv-file-content .file-preview-image {
    max-width: 100%;
    font-size: 20px;
  }
  .theme-explorer-fa .file-details-cell {
    border-top: none;
    border-bottom: none;
    padding-top: 0;
    margin: 0;
  }
  .theme-explorer-fa .file-actions-cell {
    border-top: none;
    padding-bottom: 4px;
  }
  .theme-explorer-fa .explorer-frame .explorer-caption {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    left: 0;
    right: 0;
    margin: auto;
  }
}
/*noinspection CssOverwrittenProperties*/

.file-zoom-dialog .explorer-frame .file-other-icon {
  font-size: 22em;
  font-size: 50vmin;
}
/*
Version: @@ver@@ Timestamp: @@timestamp@@
*/

.select2-container {
  margin: 0;
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.select2-container,
.select2-drop,
.select2-search,
.select2-search input {
  /*
    Force border-box so that % widths fit the parent
    container without overlap because of margin/padding.
    More Info : http://www.quirksmode.org/css/box.html
  */
  -webkit-box-sizing: border-box;
  /* webkit */
  -moz-box-sizing: border-box;
  /* firefox */
  box-sizing: border-box;
  /* css3 */

}
.select2-container .select2-choice {
  display: block;
  height: 28px;
  padding: 0 0 0 8px;
  overflow: hidden;
  position: relative;
  border: 1px solid #aaaaaa;
  white-space: nowrap;
  line-height: 26px;
  color: #444444;
  text-decoration: none;
  border-radius: 4px;
  background-clip: padding-box;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: white;
  background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eeeeee), color-stop(0.5, white));
  background-image: -webkit-linear-gradient(center bottom, #eeeeee 0%, white 50%);
  background-image: -moz-linear-gradient(center bottom, #eeeeee 0%, white 50%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee', GradientType=0);
  background-image: linear-gradient(to top, #eeeeee 0%, white 50%);
}
html[dir="rtl"] .select2-container .select2-choice {
  padding: 0 8px 0 0;
}
.select2-container.select2-drop-above .select2-choice {
  border-bottom-color: #aaaaaa;
  border-radius: 0 0 4px 4px;
  background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eeeeee), color-stop(0.9, white));
  background-image: -webkit-linear-gradient(center bottom, #eeeeee 0%, white 90%);
  background-image: -moz-linear-gradient(center bottom, #eeeeee 0%, white 90%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee', GradientType=0);
  background-image: linear-gradient(to bottom, #eeeeee 0%, white 90%);
}
.select2-container .select2-choice > .select2-chosen {
  margin-right: 26px;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  float: none;
  width: auto;
  text-align: left;
}
html[dir="rtl"] .select2-container .select2-choice > .select2-chosen {
  margin-left: 26px;
  margin-right: 0;
}
.select2-container .select2-choice abbr {
  display: none;
  width: 12px;
  height: 12px;
  position: absolute;
  right: 24px;
  top: 8px;
  font-size: 1px;
  text-decoration: none;
  border: 0;
  background: url('../img/vendor/select2/select2.png') right top no-repeat;
  cursor: pointer;
  outline: 0;
}
.select2-container.select2-allowclear .select2-choice abbr {
  display: inline-block;
}
.select2-container .select2-choice abbr:hover {
  background-position: right -11px;
  cursor: pointer;
}
.select2-drop-mask {
  border: 0;
  margin: 0;
  padding: 0;
  position: fixed;
  left: 0;
  top: 0;
  min-height: 100%;
  min-width: 100%;
  height: auto;
  width: auto;
  opacity: 0;
  z-index: 9998;
  /* styles required for IE to work */
  background-color: white;
  filter: alpha(opacity=0);
}
.select2-drop {
  width: 100%;
  margin-top: -1px;
  position: absolute;
  z-index: 9999;
  top: 100%;
  background: white;
  color: black;
  border: 1px solid #aaaaaa;
  border-top: 0;
  border-radius: 0 0 4px 4px;
  -webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
  box-shadow: 0 4px 5px rgba(0, 0, 0, 0.15);
}
.select2-drop.select2-drop-above {
  margin-top: 1px;
  border-top: 1px solid #aaaaaa;
  border-bottom: 0;
  border-radius: 4px 4px 0 0;
  -webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, 0.15);
  box-shadow: 0 -4px 5px rgba(0, 0, 0, 0.15);
}
.select2-drop-active {
  border: 1px solid #5897fb;
  border-top: none;
}
.select2-drop.select2-drop-above.select2-drop-active {
  border-top: 1px solid #5897fb;
}
.select2-drop-auto-width {
  border-top: 1px solid #aaaaaa;
  width: auto;
}
.select2-container .select2-choice .select2-arrow {
  display: inline-block;
  width: 18px;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  border-left: 1px solid #aaaaaa;
  border-radius: 0 4px 4px 0;
  background-clip: padding-box;
  background: #cccccc;
  background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #cccccc), color-stop(0.6, #eeeeee));
  background-image: -webkit-linear-gradient(center bottom, #cccccc 0%, #eeeeee 60%);
  background-image: -moz-linear-gradient(center bottom, #cccccc 0%, #eeeeee 60%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#cccccc', GradientType=0);
  background-image: linear-gradient(to top, #cccccc 0%, #eeeeee 60%);
}
html[dir="rtl"] .select2-container .select2-choice .select2-arrow {
  left: 0;
  right: auto;
  border-left: none;
  border-right: 1px solid #aaaaaa;
  border-radius: 4px 0 0 4px;
}
.select2-container .select2-choice .select2-arrow b {
  display: block;
  width: 100%;
  height: 100%;
  background: url('../img/vendor/select2/select2.png') no-repeat 0 1px;
}
html[dir="rtl"] .select2-container .select2-choice .select2-arrow b {
  background-position: 2px 1px;
}
.select2-search {
  display: inline-block;
  width: 100%;
  min-height: 26px;
  margin: 0;
  padding: 4px 4px 0 4px;
  position: relative;
  z-index: 10000;
  white-space: nowrap;
}
.select2-search input {
  width: 100%;
  height: auto !important;
  min-height: 26px;
  padding: 4px 20px 4px 5px;
  margin: 0;
  outline: 0;
  font-family: sans-serif;
  font-size: 1em;
  border: 1px solid #aaaaaa;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  background: white url('../img/vendor/select2/select2.png') no-repeat 100% -22px;
  background: url('../img/vendor/select2/select2.png') no-repeat 100% -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eeeeee));
  background: url('../img/vendor/select2/select2.png') no-repeat 100% -22px, -webkit-linear-gradient(center bottom, white 85%, #eeeeee 99%);
  background: url('../img/vendor/select2/select2.png') no-repeat 100% -22px, -moz-linear-gradient(center bottom, white 85%, #eeeeee 99%);
  background: url('../img/vendor/select2/select2.png') no-repeat 100% -22px, linear-gradient(to bottom, white 85%, #eeeeee 99%) 0 0;
}
html[dir="rtl"] .select2-search input {
  padding: 4px 5px 4px 20px;
  background: white url('../img/vendor/select2/select2.png') no-repeat -37px -22px;
  background: url('../img/vendor/select2/select2.png') no-repeat -37px -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eeeeee));
  background: url('../img/vendor/select2/select2.png') no-repeat -37px -22px, -webkit-linear-gradient(center bottom, white 85%, #eeeeee 99%);
  background: url('../img/vendor/select2/select2.png') no-repeat -37px -22px, -moz-linear-gradient(center bottom, white 85%, #eeeeee 99%);
  background: url('../img/vendor/select2/select2.png') no-repeat -37px -22px, linear-gradient(to bottom, white 85%, #eeeeee 99%) 0 0;
}
.select2-search input.select2-active {
  background: white url('../img/vendor/select2/select2-spinner.gif') no-repeat 100%;
  background: url('../img/vendor/select2/select2-spinner.gif') no-repeat 100%, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, white), color-stop(0.99, #eeeeee));
  background: url('../img/vendor/select2/select2-spinner.gif') no-repeat 100%, -webkit-linear-gradient(center bottom, white 85%, #eeeeee 99%);
  background: url('../img/vendor/select2/select2-spinner.gif') no-repeat 100%, -moz-linear-gradient(center bottom, white 85%, #eeeeee 99%);
  background: url('../img/vendor/select2/select2-spinner.gif') no-repeat 100%, linear-gradient(to bottom, white 85%, #eeeeee 99%) 0 0;
}
.select2-container-active .select2-choice,
.select2-container-active .select2-choices {
  border: 1px solid #5897fb;
  outline: none;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}
.select2-dropdown-open .select2-choice {
  border-bottom-color: transparent;
  -webkit-box-shadow: 0 1px 0 white inset;
  box-shadow: 0 1px 0 white inset;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  background-color: #eeeeee;
  background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, white), color-stop(0.5, #eeeeee));
  background-image: -webkit-linear-gradient(center bottom, white 0%, #eeeeee 50%);
  background-image: -moz-linear-gradient(center bottom, white 0%, #eeeeee 50%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0);
  background-image: linear-gradient(to top, white 0%, #eeeeee 50%);
}
.select2-dropdown-open.select2-drop-above .select2-choice,
.select2-dropdown-open.select2-drop-above .select2-choices {
  border: 1px solid #5897fb;
  border-top-color: transparent;
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, white), color-stop(0.5, #eeeeee));
  background-image: -webkit-linear-gradient(center top, white 0%, #eeeeee 50%);
  background-image: -moz-linear-gradient(center top, white 0%, #eeeeee 50%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0);
  background-image: linear-gradient(to bottom, white 0%, #eeeeee 50%);
}
.select2-dropdown-open .select2-choice .select2-arrow {
  background: transparent;
  border-left: none;
  filter: none;
}
html[dir="rtl"] .select2-dropdown-open .select2-choice .select2-arrow {
  border-right: none;
}
.select2-dropdown-open .select2-choice .select2-arrow b {
  background-position: -18px 1px;
}
html[dir="rtl"] .select2-dropdown-open .select2-choice .select2-arrow b {
  background-position: -16px 1px;
}
.select2-hidden-accessible {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}
/* results */

.select2-results {
  max-height: 200px;
  padding: 0 0 0 4px;
  margin: 4px 4px 4px 0;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-tap-highlight-color: transparent;
}
html[dir="rtl"] .select2-results {
  padding: 0 4px 0 0;
  margin: 4px 0 4px 4px;
}
.select2-results ul.select2-result-sub {
  margin: 0;
  padding-left: 0;
}
.select2-results li {
  list-style: none;
  display: list-item;
  background-image: none;
}
.select2-results li.select2-result-with-children > .select2-result-label {
  font-weight: bold;
}
.select2-results .select2-result-label {
  padding: 3px 7px 4px;
  margin: 0;
  cursor: pointer;
  min-height: 1em;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.select2-results-dept-1 .select2-result-label {
  padding-left: 20px;
}
.select2-results-dept-2 .select2-result-label {
  padding-left: 40px;
}
.select2-results-dept-3 .select2-result-label {
  padding-left: 60px;
}
.select2-results-dept-4 .select2-result-label {
  padding-left: 80px;
}
.select2-results-dept-5 .select2-result-label {
  padding-left: 100px;
}
.select2-results-dept-6 .select2-result-label {
  padding-left: 110px;
}
.select2-results-dept-7 .select2-result-label {
  padding-left: 120px;
}
.select2-results .select2-highlighted {
  background: #3875d7;
  color: white;
}
.select2-results li em {
  background: #feffde;
  font-style: normal;
}
.select2-results .select2-highlighted em {
  background: transparent;
}
.select2-results .select2-highlighted ul {
  background: white;
  color: black;
}
.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-ajax-error,
.select2-results .select2-selection-limit {
  background: #f4f4f4;
  display: list-item;
  padding-left: 5px;
}
/*
disabled look for disabled choices in the results dropdown
*/

.select2-results .select2-disabled.select2-highlighted {
  color: #666666;
  background: #f4f4f4;
  display: list-item;
  cursor: default;
}
.select2-results .select2-disabled {
  background: #f4f4f4;
  display: list-item;
  cursor: default;
}
.select2-results .select2-selected {
  display: none;
}
.select2-more-results.select2-active {
  background: #f4f4f4 url('../img/vendor/select2/select2-spinner.gif') no-repeat 100%;
}
.select2-results .select2-ajax-error {
  background: rgba(255, 50, 50, 0.2);
}
.select2-more-results {
  background: #f4f4f4;
  display: list-item;
}
/* disabled styles */

.select2-container.select2-container-disabled .select2-choice {
  background-color: #f4f4f4;
  background-image: none;
  border: 1px solid #dddddd;
  cursor: default;
}
.select2-container.select2-container-disabled .select2-choice .select2-arrow {
  background-color: #f4f4f4;
  background-image: none;
  border-left: 0;
}
.select2-container.select2-container-disabled .select2-choice abbr {
  display: none;
}
/* multiselect */

.select2-container-multi .select2-choices {
  height: auto !important;
  height: 1%;
  margin: 0;
  padding: 0 5px 0 0;
  position: relative;
  border: 1px solid #aaaaaa;
  cursor: text;
  overflow: hidden;
  background-color: white;
  background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(1%, #eeeeee), color-stop(15%, white));
  background-image: -webkit-linear-gradient(top, #eeeeee 1%, white 15%);
  background-image: -moz-linear-gradient(top, #eeeeee 1%, white 15%);
  background-image: linear-gradient(to bottom, #eeeeee 1%, white 15%);
}
html[dir="rtl"] .select2-container-multi .select2-choices {
  padding: 0 0 0 5px;
}
.select2-locked {
  padding: 3px 5px 3px 5px !important;
}
.select2-container-multi .select2-choices {
  min-height: 26px;
}
.select2-container-multi.select2-container-active .select2-choices {
  border: 1px solid #5897fb;
  outline: none;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}
.select2-container-multi .select2-choices li {
  float: left;
  list-style: none;
}
html[dir="rtl"] .select2-container-multi .select2-choices li {
  float: right;
}
.select2-container-multi .select2-choices .select2-search-field {
  margin: 0;
  padding: 0;
  white-space: nowrap;
}
.select2-container-multi .select2-choices .select2-search-field input {
  padding: 5px;
  margin: 1px 0;
  font-family: sans-serif;
  font-size: 100%;
  color: #666666;
  outline: 0;
  border: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  background: transparent !important;
}
.select2-container-multi .select2-choices .select2-search-field input.select2-active {
  background: white url('../img/vendor/select2/select2-spinner.gif') no-repeat 100% !important;
}
.select2-default {
  color: #999999 !important;
}
.select2-container-multi .select2-choices .select2-search-choice {
  padding: 3px 5px 3px 18px;
  margin: 3px 0 3px 5px;
  position: relative;
  line-height: 13px;
  color: #333333;
  cursor: default;
  border: 1px solid #aaaaaa;
  border-radius: 3px;
  -webkit-box-shadow: 0 0 2px white inset, 0 1px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 2px white inset, 0 1px 0 rgba(0, 0, 0, 0.05);
  background-clip: padding-box;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: #e4e4e4;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#f4f4f4', GradientType=0);
  background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eeeeee));
  background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
  background-image: linear-gradient(to bottom, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eeeeee 100%);
}
html[dir="rtl"] .select2-container-multi .select2-choices .select2-search-choice {
  margin: 3px 5px 3px 0;
  padding: 3px 18px 3px 5px;
}
.select2-container-multi .select2-choices .select2-search-choice .select2-chosen {
  cursor: default;
}
.select2-container-multi .select2-choices .select2-search-choice-focus {
  background: #d4d4d4;
}
.select2-search-choice-close {
  display: block;
  width: 12px;
  height: 13px;
  position: absolute;
  right: 3px;
  top: 4px;
  font-size: 1px;
  outline: none;
  background: url('../img/vendor/select2/select2.png') right top no-repeat;
}
html[dir="rtl"] .select2-search-choice-close {
  right: auto;
  left: 3px;
}
.select2-container-multi .select2-search-choice-close {
  left: 3px;
}
html[dir="rtl"] .select2-container-multi .select2-search-choice-close {
  left: auto;
  right: 2px;
}
.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover {
  background-position: right -11px;
}
.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close {
  background-position: right -11px;
}
/* disabled styles */

.select2-container-multi.select2-container-disabled .select2-choices {
  background-color: #f4f4f4;
  background-image: none;
  border: 1px solid #dddddd;
  cursor: default;
}
.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice {
  padding: 3px 5px 3px 5px;
  border: 1px solid #dddddd;
  background-image: none;
  background-color: #f4f4f4;
}
.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close {
  display: none;
  background: none;
}
/* end multiselect */

.select2-result-selectable .select2-match,
.select2-result-unselectable .select2-match {
  text-decoration: underline;
}
.select2-offscreen,
.select2-offscreen:focus {
  clip: rect(0 0 0 0) !important;
  width: 1px !important;
  height: 1px !important;
  border: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  outline: 0 !important;
  left: 0px !important;
  top: 0px !important;
}
.select2-display-none {
  display: none;
}
.select2-measure-scrollbar {
  position: absolute;
  top: -10000px;
  left: -10000px;
  width: 100px;
  height: 100px;
  overflow: scroll;
}
/* Retina-ize icons */

@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 2dppx) {
  .select2-search input,
  .select2-search-choice-close,
  .select2-container .select2-choice abbr,
  .select2-container .select2-choice .select2-arrow b {
    background-image: url('../img/vendor/select2/select2x2.png') !important;
    background-repeat: no-repeat !important;
    background-size: 60px 40px !important;
  }
  .select2-search input {
    background-position: 100% -21px !important;
  }
}
/**
 * Select2 Bootstrap CSS
 * Compatible with Select2 3.3.2, 3.4.1, 3.4.2 and Twitter Bootstrap 3.0.0
 * MIT License
 */

/**
 * Reset Bootstrap 3 .form-control styles which - if applied to the
 * original <select>-element the Select2-plugin may be run against -
 * are copied to the .select2-container.
 *
 * 1. Overwrite .select2-container's original display:inline-block
 *    with Bootstrap 3's default for .form-control, display:block;
 *    courtesy of @juristr (@see https://github.com/fk/select2-bootstrap-css/pull/1)
 */

.select2-container.form-control {
  background: transparent;
  border: none;
  display: block;
  /* 1 */
  margin: 0;
  padding: 0;
}
/**
 * Adjust Select2 inputs to fit Bootstrap 3 default .form-control appearance.
 */
.select2-container .select2-choices .select2-search-field input,
.select2-container .select2-choice,
.select2-container .select2-choices {
  background: none;
  padding: 0;
  border-color: #f1f1f1;
  border-radius: 4px;
  color: #464545;
  font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
  background-color: white;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.select2-search input {
  border-color: #f1f1f1;
  border-radius: 4px;
  color: #464545;
  font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
  background-color: white;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.select2-container .select2-choices .select2-search-field input {
  -webkit-box-shadow: none;
  box-shadow: none;
}
/**
 * Adjust Select2 input heights to match the Bootstrap default.
 */

.select2-container .select2-choice {
  height: 29px;
  line-height: 1.428571429;
}
/**
 * Address Multi Select2's height which - depending on how many elements have been selected -
 * may grown higher than their initial size.
 */

.select2-container.select2-container-multi.form-control {
  height: auto;
}
/**
 * Address Bootstrap 3 control sizing classes
 * @see http://getbootstrap.com/css/#forms-control-sizes
 */

.select2-container.input-sm .select2-choice,
.input-group-sm .select2-container .select2-choice {
  height: 24px;
  line-height: 1.5;
  border-radius: 3px;
}
.select2-container.input-lg .select2-choice,
.input-group-lg .select2-container .select2-choice {
  height: 36px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.select2-container-multi .select2-choices .select2-search-field input {
  height: 27px;
}
.select2-container-multi.input-sm .select2-choices .select2-search-field input,
.input-group-sm .select2-container-multi .select2-choices .select2-search-field input {
  height: 22px;
}
.select2-container-multi.input-lg .select2-choices .select2-search-field input,
.input-group-lg .select2-container-multi .select2-choices .select2-search-field input {
  height: 34px;
}
/**
 * Adjust height and line-height for .select2-search-field amd multi-select Select2 widgets.
 *
 * 1. Class repetition to address missing .select2-chosen in Select2 < 3.3.2.
 */

.select2-container-multi .select2-choices .select2-search-field input {
  margin: 0;
}
.select2-chosen,
.select2-choice > span:first-child,
/* 1 */ .select2-container .select2-choices .select2-search-field input {
  padding: 4px 8px;
  color: #464545;
}
.input-sm .select2-chosen,
.input-group-sm .select2-chosen,
.input-sm .select2-choice > span:first-child,
/* 1 */ .input-group-sm .select2-choice > span:first-child,
/* 1 */ .input-sm .select2-choices .select2-search-field input,
.input-group-sm .select2-choices .select2-search-field input {
  padding: 2px 5px;
}
.input-lg .select2-chosen,
.input-group-lg .select2-chosen,
.input-lg .select2-choice > span:first-child,
/* 1 */ .input-group-lg .select2-choice > span:first-child,
/* 1 */ .input-lg .select2-choices .select2-search-field input,
.input-group-lg .select2-choices .select2-search-field input {
  padding: 6px 10px;
}
.select2-container-multi .select2-choices .select2-search-choice {
  margin-top: 5px;
  margin-bottom: 3px;
}
.select2-container-multi.input-sm .select2-choices .select2-search-choice,
.input-group-sm .select2-container-multi .select2-choices .select2-search-choice {
  margin-top: 3px;
  margin-bottom: 2px;
}
.select2-container-multi.input-lg .select2-choices .select2-search-choice,
.input-group-lg .select2-container-multi .select2-choices .select2-search-choice {
  line-height: 24px;
}
/**
 * Adjust the single Select2's dropdown arrow button appearance.
 *
 * 1. For Select2 v.3.3.2.
 */

.select2-container .select2-choice .select2-arrow,
.select2-container .select2-choice div/* 1 */ {
  border-left: 1px solid #f1f1f1;
  background: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.select2-dropdown-open .select2-choice .select2-arrow,
.select2-dropdown-open .select2-choice div/* 1 */ {
  border-left-color: transparent;
  background: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
/**
 * Adjust the dropdown arrow button icon position for the single-select Select2 elements
 * to make it line up vertically now that we increased the height of .select2-container.
 *
 * 1. Class repetition to address missing .select2-chosen in Select2 v.3.3.2.
 */

.select2-container .select2-choice .select2-arrow b,
.select2-container .select2-choice div b/* 1 */ {
  background-position: 0 3px;
}
.select2-dropdown-open .select2-choice .select2-arrow b,
.select2-dropdown-open .select2-choice div b/* 1 */ {
  background-position: -18px 3px;
}
.select2-container.input-sm .select2-choice .select2-arrow b,
.input-group-sm .select2-container .select2-choice .select2-arrow b,
.select2-container.input-sm .select2-choice div b,
/* 1 */ .input-group-sm .select2-container .select2-choice div b/* 1 */ {
  background-position: 0 1px;
}
.select2-dropdown-open.input-sm .select2-choice .select2-arrow b,
.input-group-sm .select2-dropdown-open .select2-choice .select2-arrow b,
.select2-dropdown-open.input-sm .select2-choice div b,
/* 1 */ .input-group-sm .select2-dropdown-open .select2-choice div b/* 1 */ {
  background-position: -18px 1px;
}
.select2-container.input-lg .select2-choice .select2-arrow b,
.input-group-lg .select2-container .select2-choice .select2-arrow b,
.select2-container.input-lg .select2-choice div b,
/* 1 */ .input-group-lg .select2-container .select2-choice div b/* 1 */ {
  background-position: 0 9px;
}
.select2-dropdown-open.input-lg .select2-choice .select2-arrow b,
.input-group-lg .select2-dropdown-open .select2-choice .select2-arrow b,
.select2-dropdown-open.input-lg .select2-choice div b,
/* 1 */ .input-group-lg .select2-dropdown-open .select2-choice div b/* 1 */ {
  background-position: -18px 9px;
}
/**
 * Address Bootstrap's validation states and change Select2's border colors and focus states.
 * Apply .has-warning, .has-danger or .has-succes to #select2-drop to match Bootstraps' colors.
 */
.has-warning .select2-choice,
.has-warning .select2-choices {
  border-color: white;
}
.has-warning .select2-container-active .select2-choice,
.has-warning .select2-container-multi.select2-container-active .select2-choices {
  border-color: #e6e6e6;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
}
.has-warning.select2-drop-active {
  border-color: #e6e6e6;
}
.has-warning.select2-drop-active.select2-drop.select2-drop-above {
  border-top-color: #e6e6e6;
}
.has-error .select2-choice,
.has-error .select2-choices {
  border-color: white;
}
.has-error .select2-container-active .select2-choice,
.has-error .select2-container-multi.select2-container-active .select2-choices {
  border-color: #e6e6e6;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
}
.has-error.select2-drop-active {
  border-color: #e6e6e6;
}
.has-error.select2-drop-active.select2-drop.select2-drop-above {
  border-top-color: #e6e6e6;
}
.has-success .select2-choice,
.has-success .select2-choices {
  border-color: white;
}
.has-success .select2-container-active .select2-choice,
.has-success .select2-container-multi.select2-container-active .select2-choices {
  border-color: #e6e6e6;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px white;
}
.has-success.select2-drop-active {
  border-color: #e6e6e6;
}
.has-success.select2-drop-active.select2-drop.select2-drop-above {
  border-top-color: #e6e6e6;
}
/**
 * Make Select2's active-styles - applied to .select2-container when the widget receives focus -
 * fit Bootstrap 3's .form-element:focus appearance.
 */

.select2-container-active .select2-choice,
.select2-container-multi.select2-container-active .select2-choices {
  border-color: white;
  outline: none;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.select2-drop-active {
  border-color: white;
}
.select2-drop-auto-width,
.select2-drop.select2-drop-above.select2-drop-active {
  border-top-color: white;
}
/**
 * Select2 widgets in Bootstrap Input Groups
 *
 * When Select2 widgets are combined with other elements using Bootstrap 3's
 * "Input Group" component, we don't want specific edges of the Select2 container
 * to have a border-radius.
 *
 * In Bootstrap 2, input groups required a markup where these style adjustments
 * could be bound to a CSS-class identifying if the additional elements are appended,
 * prepended or both.
 *
 * Bootstrap 3 doesn't rely on these classes anymore, so we have to use our own.
 * Use .select2-bootstrap-prepend and .select2-bootstrap-append on a Bootstrap 3 .input-group
 * to let the contained Select2 widget know which edges should not be rounded as they are
 * directly followed by another element.
 *
 * @see http://getbootstrap.com/components/#input-groups
 */

.input-group.select2-bootstrap-prepend [class^="select2-choice"] {
  border-bottom-left-radius: 0 !important;
  border-top-left-radius: 0 !important;
}
.input-group.select2-bootstrap-append [class^="select2-choice"] {
  border-bottom-right-radius: 0 !important;
  border-top-right-radius: 0 !important;
}
.select2-dropdown-open [class^="select2-choice"] {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}
.select2-dropdown-open.select2-drop-above [class^="select2-choice"] {
  border-top-right-radius: 0 !important;
  border-top-left-radius: 0 !important;
}
/**
 * Adjust Select2's choices hover and selected styles to match Bootstrap 3's default dropdown styles.
 */

.select2-results .select2-highlighted {
  color: white;
  background-color: #375a7f;
}
/**
 * Adjust alignment of Bootstrap 3 buttons in Bootstrap 3 Input Groups to address
 * Multi Select2's height which - depending on how many elements have been selected -
 * may grown higher than their initial size.
 */

.select2-bootstrap-append .select2-container-multiple,
.select2-bootstrap-prepend .select2-container-multiple,
.select2-bootstrap-append .input-group-btn,
.select2-bootstrap-prepend .input-group-btn,
.select2-bootstrap-append .input-group-btn .btn,
.select2-bootstrap-prepend .input-group-btn .btn {
  vertical-align: top;
}
/**
 * Make Multi Select2's choices match Bootstrap 3's default button styles.
 */

.select2-container-multi .select2-choices .select2-search-choice {
  color: #464545;
  background: #464545;
  border-color: #464545;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.select2-container-multi .select2-choices .select2-search-choice-focus {
  background: #313131;
  border-color: #272727;
  color: white;
  -webkit-box-shadow: none;
  box-shadow: none;
}
/**
 * Address Multi Select2's choice close-button vertical alignment.
 */

.select2-search-choice-close {
  margin-top: -7px;
  top: 50%;
}
/**
 * Adjust the single Select2's clear button position (used to reset the select box
 * back to the placeholder value and visible once a selection is made
 * activated by Select2's "allowClear" option).
 */

.select2-container .select2-choice abbr {
  top: 50%;
}
/**
 * Adjust "no results" and "selection limit" messages to make use
 * of Bootstrap 3's default "Alert" style.
 *
 * @see http://getbootstrap.com/components/#alerts-default
 */

.select2-results .select2-no-results,
.select2-results .select2-searching,
.select2-results .select2-selection-limit {
  background-color: #f39c12;
  color: white;
}
/**
 * Address disabled Select2 styles.
 *
 * 1. For Select2 v.3.3.2.
 * 2. Revert border-left:0 inherited from Select2's CSS to prevent the arrow
 *    from jumping when switching from disabled to enabled state and vice versa.
 */

.select2-container.select2-container-disabled .select2-choice,
.select2-container.select2-container-disabled .select2-choices {
  cursor: not-allowed;
  background-color: #ebebeb;
  border-color: #f1f1f1;
}
.select2-container.select2-container-disabled .select2-choice .select2-arrow,
.select2-container.select2-container-disabled .select2-choices .select2-arrow,
.select2-container.select2-container-disabled .select2-choice div/* 1 */,
.select2-container.select2-container-disabled .select2-choices div/* 1 */ {
  background-color: transparent;
  border-left: 1px solid transparent;
  /* 2 */

}
/**
 * Address Select2's loading indicator position - which should not stick
 * to the right edge of Select2's search input.
 *
 * 1. in .select2-search input
 * 2. in Multi Select2's .select2-search-field input
 * 3. in the status-message of infinite-scroll with remote data (@see http://ivaynberg.github.io/select2/#infinite)
 *
 * These styles alter Select2's default background-position of 100%
 * and supply the new background-position syntax to browsers which support it:
 *
 * 1. Android, Safari < 6/Mobile, IE<9: change to a relative background-position of 99%
 * 2. Chrome 25+, Firefox 13+, IE 9+, Opera 10.5+: use the new CSS3-background-position syntax
 *
 * @see http://www.w3.org/TR/css3-background/#background-position
 *
 * @todo Since both Select2 and Bootstrap 3 only support IE8 and above,
 * we could use the :after-pseudo-element to display the loading indicator.
 * Alternatively, we could supply an altered loading indicator image which already
 * contains an offset to the right.
 */

.select2-search input.select2-active,
/* 1 */ .select2-container-multi .select2-choices .select2-search-field input.select2-active,
/* 2 */ .select2-more-results.select2-active/* 3 */ {
  background-position: 99%;
  /* 4 */
  background-position: right 4px center;
  /* 5 */

}
.select2-container-multi .select2-choices .select2-search-choice {
  color: white;
}
.mfp-bg {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1062;
  overflow: hidden;
  position: fixed;
  background: #0b0b0b;
  opacity: 0.8;
  filter: alpha(opacity=80);
}
.mfp-wrap {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1063;
  position: fixed;
  outline: none !important;
  -webkit-backface-visibility: hidden;
}
.mfp-container {
  text-align: center;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  padding: 0 8px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.mfp-container:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.mfp-align-top .mfp-container:before {
  display: none;
}
.mfp-content {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: 0 auto;
  text-align: left;
  z-index: 1065;
}
.mfp-inline-holder .mfp-content,
.mfp-ajax-holder .mfp-content {
  width: 100%;
  cursor: auto;
}
.mfp-ajax-cur {
  cursor: progress;
}
.mfp-zoom-out-cur,
.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
  cursor: -moz-zoom-out;
  cursor: -webkit-zoom-out;
  cursor: zoom-out;
}
.mfp-zoom {
  cursor: pointer;
  cursor: -webkit-zoom-in;
  cursor: -moz-zoom-in;
  cursor: zoom-in;
}
.mfp-auto-cursor .mfp-content {
  cursor: auto;
}
.mfp-close,
.mfp-arrow,
.mfp-preloader,
.mfp-counter {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.mfp-loading.mfp-figure {
  display: none;
}
.mfp-hide {
  display: none !important;
}
.mfp-preloader {
  color: #cccccc;
  position: absolute;
  top: 50%;
  width: auto;
  text-align: center;
  margin-top: -0.8em;
  left: 8px;
  right: 8px;
  z-index: 1064;
}
.mfp-preloader a {
  color: #cccccc;
}
.mfp-preloader a:hover {
  color: white;
}
.mfp-s-ready .mfp-preloader {
  display: none;
}
.mfp-s-error .mfp-content {
  display: none;
}
button.mfp-close,
button.mfp-arrow {
  overflow: visible;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
  display: block;
  outline: none;
  padding: 0;
  z-index: 1066;
  -webkit-box-shadow: none;
  box-shadow: none;
}
button::-moz-focus-inner {
  padding: 0;
  border: 0;
}
.mfp-close {
  width: 44px;
  height: 44px;
  line-height: 44px;
  position: absolute;
  right: 0;
  top: 0;
  text-decoration: none;
  text-align: center;
  opacity: 0.65;
  padding: 0 0 18px 10px;
  color: white;
  font-style: normal;
  font-size: 28px;
  font-family: Arial, Baskerville, monospace;
}
.mfp-close:hover,
.mfp-close:focus {
  opacity: 1;
}
.mfp-close:active {
  top: 1px;
}
.mfp-close-btn-in .mfp-close {
  color: #333333;
}
.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
  color: white;
  right: -6px;
  text-align: right;
  padding-right: 6px;
  width: 100%;
}
.mfp-counter {
  position: absolute;
  top: 0;
  right: 0;
  color: #cccccc;
  font-size: 12px;
  line-height: 18px;
}
.mfp-arrow {
  position: absolute;
  opacity: 0.65;
  margin: 0;
  top: 50%;
  margin-top: -55px;
  padding: 0;
  width: 90px;
  height: 110px;
  -webkit-tap-highlight-color: transparent;
}
.mfp-arrow:active {
  margin-top: -54px;
}
.mfp-arrow:hover,
.mfp-arrow:focus {
  opacity: 1;
}
.mfp-arrow:before,
.mfp-arrow:after,
.mfp-arrow .mfp-b,
.mfp-arrow .mfp-a {
  content: '';
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  left: 0;
  top: 0;
  margin-top: 35px;
  margin-left: 35px;
  border: medium inset transparent;
}
.mfp-arrow:after,
.mfp-arrow .mfp-a {
  border-top-width: 13px;
  border-bottom-width: 13px;
  top: 8px;
}
.mfp-arrow:before,
.mfp-arrow .mfp-b {
  border-top-width: 21px;
  border-bottom-width: 21px;
}
.mfp-arrow-left {
  left: 0;
}
.mfp-arrow-left:after,
.mfp-arrow-left .mfp-a {
  border-right: 17px solid white;
  margin-left: 31px;
}
.mfp-arrow-left:before,
.mfp-arrow-left .mfp-b {
  margin-left: 25px;
  border-right: 27px solid #3f3f3f;
}
.mfp-arrow-right {
  right: 0;
}
.mfp-arrow-right:after,
.mfp-arrow-right .mfp-a {
  border-left: 17px solid white;
  margin-left: 39px;
}
.mfp-arrow-right:before,
.mfp-arrow-right .mfp-b {
  border-left: 27px solid #3f3f3f;
}
.mfp-iframe-holder {
  padding-top: 40px;
  padding-bottom: 40px;
}
.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 900px;
}
.mfp-iframe-holder .mfp-close {
  top: -40px;
}
.mfp-iframe-scaler {
  width: 100%;
  height: 0;
  overflow: hidden;
  padding-top: 56.25%;
}
.mfp-iframe-scaler iframe {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
  background: black;
}
/* Main image in popup */

img.mfp-img {
  width: auto;
  max-width: 100%;
  height: auto;
  display: block;
  line-height: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 40px 0 40px;
  margin: 0 auto;
}
/* The shadow behind the image */

.mfp-figure {
  line-height: 0;
}
.mfp-figure:after {
  content: '';
  position: absolute;
  left: 0;
  top: 40px;
  bottom: 40px;
  display: block;
  right: 0;
  width: auto;
  height: auto;
  z-index: -1;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
  background: #444444;
}
.mfp-figure small {
  color: #bdbdbd;
  display: block;
  font-size: 12px;
  line-height: 14px;
}
.mfp-bottom-bar {
  margin-top: -36px;
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  cursor: auto;
}
.mfp-title {
  text-align: left;
  line-height: 18px;
  color: #f3f3f3;
  word-wrap: break-word;
  padding-right: 36px;
}
.mfp-image-holder .mfp-content {
  max-width: 100%;
}
.mfp-gallery .mfp-image-holder .mfp-figure {
  cursor: pointer;
}
@media screen and (max-width: 800px) and (orientation: landscape), screen and (max-height: 300px) {
  /**
         * Remove all paddings around the image on small screen
         */
  
  .mfp-img-mobile .mfp-image-holder {
    padding-left: 0;
    padding-right: 0;
  }
  .mfp-img-mobile img.mfp-img {
    padding: 0;
  }
  .mfp-img-mobile .mfp-figure:after {
    top: 0;
    bottom: 0;
  }
  .mfp-img-mobile .mfp-figure small {
    display: inline;
    margin-left: 5px;
  }
  .mfp-img-mobile .mfp-bottom-bar {
    background: rgba(0, 0, 0, 0.6);
    bottom: 0;
    margin: 0;
    top: auto;
    padding: 3px 5px;
    position: fixed;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  .mfp-img-mobile .mfp-bottom-bar:empty {
    padding: 0;
  }
  .mfp-img-mobile .mfp-counter {
    right: 5px;
    top: 3px;
  }
  .mfp-img-mobile .mfp-close {
    top: 0;
    right: 0;
    width: 35px;
    height: 35px;
    line-height: 35px;
    background: rgba(0, 0, 0, 0.6);
    position: fixed;
    text-align: center;
    padding: 0;
  }
}
@media all and (max-width: 900px) {
  .mfp-arrow {
    -webkit-transform: scale(0.75);
    transform: scale(0.75);
  }
  .mfp-arrow-left {
    -webkit-transform-origin: 0;
    transform-origin: 0;
  }
  .mfp-arrow-right {
    -webkit-transform-origin: 100%;
    transform-origin: 100%;
  }
  .mfp-container {
    padding-left: 6px;
    padding-right: 6px;
  }
}
.mfp-ie7 .mfp-img {
  padding: 0;
}
.mfp-ie7 .mfp-bottom-bar {
  width: 600px;
  left: 50%;
  margin-left: -300px;
  margin-top: 5px;
  padding-bottom: 5px;
}
.mfp-ie7 .mfp-container {
  padding: 0;
}
.mfp-ie7 .mfp-content {
  padding-top: 44px;
}
.mfp-ie7 .mfp-close {
  top: 0;
  right: 0;
  padding-top: 0;
}
.autocomplete-suggestions {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #999999;
  background: white;
  cursor: default;
  overflow: auto;
  -webkit-box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64);
  -moz-box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64);
  box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64);
}
.autocomplete-suggestion {
  padding: 2px 5px;
  white-space: nowrap;
  overflow: hidden;
}
.autocomplete-no-suggestion {
  padding: 2px 5px;
}
.autocomplete-selected {
  background: #f0f0f0;
}
.autocomplete-suggestions strong {
  font-weight: bold;
  color: black;
}
.autocomplete-group {
  padding: 2px 5px;
  font-weight: bold;
  font-size: 16px;
  color: black;
  display: block;
  border-bottom: 1px solid black;
}
/*
 *  webui popover plugin  - v1.2.6
 *  A lightWeight popover plugin with jquery ,enchance the  popover plugin of bootstrap with some awesome new features. It works well with bootstrap ,but bootstrap is not necessary!
 *  https://github.com/sandywalker/webui-popover
 *
 *  Made by Sandy Duan
 *  Under MIT License
 */

.webui-popover-content {
  display: none;
}
/*  webui popover  */

.webui-popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9010;
  display: none;
  min-width: 50px;
  min-height: 32px;
  padding: 1px;
  text-align: left;
  white-space: normal;
  background-color: #303030;
  background-clip: padding-box;
  border: 1px solid #999999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}
.webui-popover .close {
  color: white;
  text-shadow: none;
}
.webui-popover.top,
.webui-popover.top-left,
.webui-popover.top-right {
  margin-top: -10px;
}
.webui-popover.right,
.webui-popover.right-top,
.webui-popover.right-bottom {
  margin-left: 10px;
}
.webui-popover.bottom,
.webui-popover.bottom-left,
.webui-popover.bottom-right {
  margin-top: 10px;
}
.webui-popover.left,
.webui-popover.left-top,
.webui-popover.left-bottom {
  margin-left: -10px;
}
.webui-popover.pop {
  -webkit-transform: scale(0.8);
  -o-transform: scale(0.8);
  transform: scale(0.8);
  -webkit-transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  -o-transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  transition: transform 0.15s cubic-bezier(0.3, 0, 0, 1.5);
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.pop-out {
  -webkit-transition-property: "opacity,transform";
  -o-transition-property: "opacity,transform";
  transition-property: "opacity,transform";
  -webkit-transition: 0.15s linear;
  -o-transition: 0.15s linear;
  transition: 0.15s linear;
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.fade,
.webui-popover.fade-out {
  -webkit-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.out {
  opacity: 0;
  filter: alpha(opacity=0);
}
.webui-popover.in {
  -webkit-transform: none;
  -o-transform: none;
  transform: none;
  opacity: 1;
  filter: alpha(opacity=100);
}
.webui-popover .webui-popover-content {
  padding: 9px 14px;
  overflow: auto;
  display: block;
}
.webui-popover-title {
  font-size: 14px;
  font-weight: bold;
  line-height: 18px;
  margin: 0;
  padding: 8px 14px;
  font-size: 12px;
  background-color: #282828;
  border-bottom: 1px solid #1b1b1b;
  border-radius: 5px 5px 0 0;
}
.webui-popover-content {
  padding: 9px 14px;
  overflow: auto;
  display: none;
}
.webui-popover-inverse {
  background-color: #333333;
  color: #eeeeee;
}
.webui-popover-inverse .webui-popover-title {
  background: #333333;
  border-bottom: 1px solid #3b3b3b;
  color: #eeeeee;
}
.webui-no-padding .webui-popover-content {
  padding: 0;
}
.webui-no-padding .list-group-item {
  border-right: none;
  border-left: none;
}
.webui-no-padding .list-group-item:first-child {
  border-top: 0;
}
.webui-no-padding .list-group-item:last-child {
  border-bottom: 0;
}
.webui-popover > .webui-arrow,
.webui-popover > .webui-arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.webui-popover > .webui-arrow {
  border-width: 11px;
}
.webui-popover > .webui-arrow:after {
  border-width: 10px;
  content: "";
}
.webui-popover.top > .webui-arrow,
.webui-popover.top-right > .webui-arrow,
.webui-popover.top-left > .webui-arrow {
  bottom: -11px;
  left: 50%;
  margin-left: -11px;
  border-top-color: #666666;
  border-top-color: rgba(0, 0, 0, 0.25);
  border-bottom-width: 0;
}
.webui-popover.top > .webui-arrow:after,
.webui-popover.top-right > .webui-arrow:after,
.webui-popover.top-left > .webui-arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-top-color: #303030;
  border-bottom-width: 0;
}
.webui-popover.right > .webui-arrow,
.webui-popover.right-top > .webui-arrow,
.webui-popover.right-bottom > .webui-arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #666666;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.webui-popover.right > .webui-arrow:after,
.webui-popover.right-top > .webui-arrow:after,
.webui-popover.right-bottom > .webui-arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #303030;
}
.webui-popover.bottom > .webui-arrow,
.webui-popover.bottom-right > .webui-arrow,
.webui-popover.bottom-left > .webui-arrow {
  top: -11px;
  left: 50%;
  margin-left: -11px;
  border-bottom-color: #666666;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  border-top-width: 0;
}
.webui-popover.bottom > .webui-arrow:after,
.webui-popover.bottom-right > .webui-arrow:after,
.webui-popover.bottom-left > .webui-arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-bottom-color: #303030;
  border-top-width: 0;
}
.webui-popover.left > .webui-arrow,
.webui-popover.left-top > .webui-arrow,
.webui-popover.left-bottom > .webui-arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #666666;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.webui-popover.left > .webui-arrow:after,
.webui-popover.left-top > .webui-arrow:after,
.webui-popover.left-bottom > .webui-arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-bottom-color: #303030;
  bottom: -10px;
}
.webui-popover-inverse.top > .webui-arrow,
.webui-popover-inverse.top-left > .webui-arrow,
.webui-popover-inverse.top-right > .webui-arrow,
.webui-popover-inverse.top > .webui-arrow:after,
.webui-popover-inverse.top-left > .webui-arrow:after,
.webui-popover-inverse.top-right > .webui-arrow:after {
  border-top-color: #303030;
}
.webui-popover-inverse.right > .webui-arrow,
.webui-popover-inverse.right-top > .webui-arrow,
.webui-popover-inverse.right-bottom > .webui-arrow,
.webui-popover-inverse.right > .webui-arrow:after,
.webui-popover-inverse.right-top > .webui-arrow:after,
.webui-popover-inverse.right-bottom > .webui-arrow:after {
  border-right-color: #303030;
}
.webui-popover-inverse.bottom > .webui-arrow,
.webui-popover-inverse.bottom-left > .webui-arrow,
.webui-popover-inverse.bottom-right > .webui-arrow,
.webui-popover-inverse.bottom > .webui-arrow:after,
.webui-popover-inverse.bottom-left > .webui-arrow:after,
.webui-popover-inverse.bottom-right > .webui-arrow:after {
  border-bottom-color: #303030;
}
.webui-popover-inverse.left > .webui-arrow,
.webui-popover-inverse.left-top > .webui-arrow,
.webui-popover-inverse.left-bottom > .webui-arrow,
.webui-popover-inverse.left > .webui-arrow:after,
.webui-popover-inverse.left-top > .webui-arrow:after,
.webui-popover-inverse.left-bottom > .webui-arrow:after {
  border-left-color: #303030;
}
.webui-popover i.icon-refresh:before {
  content: "";
}
.webui-popover i.icon-refresh {
  display: block;
  width: 30px;
  height: 30px;
  font-size: 20px;
  top: 50%;
  left: 50%;
  position: absolute;
  margin-left: -15px;
  margin-right: -15px;
  background: url(../img/loading.gif) no-repeat;
}
@-webkit-keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}
.webui-popover-backdrop {
  background-color: rgba(0, 0, 0, 0.65);
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9008;
}
/*! ========================================================================
 * Bootstrap Toggle: bootstrap-toggle.css v2.2.0
 * http://www.bootstraptoggle.com
 * ========================================================================
 * Copyright 2014 Min Hur, The New York Times Company
 * Licensed under MIT
 * ======================================================================== */

.checkbox label .toggle,
.checkbox-inline .toggle {
  margin-left: -20px;
  margin-right: 5px;
}
.toggle {
  position: relative;
  overflow: hidden;
}
.toggle input[type=checkbox] {
  display: none;
}
.toggle-group {
  position: absolute;
  width: 200%;
  top: 0;
  bottom: 0;
  left: 0;
  transition: left 0.35s;
  -webkit-transition: left 0.35s;
  -moz-user-select: none;
  -webkit-user-select: none;
}
.toggle.off .toggle-group {
  left: -100%;
}
.toggle-on {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 50%;
  margin: 0;
  border: 0;
  border-radius: 0;
}
.toggle-off {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  right: 0;
  margin: 0;
  border: 0;
  border-radius: 0;
}
.toggle-handle {
  position: relative;
  margin: 0 auto;
  padding-top: 0;
  padding-bottom: 0;
  height: 100%;
  width: 0;
  border-width: 0 1px;
}
.toggle.btn {
  min-width: 59px;
  min-height: 34px;
}
.toggle-on.btn {
  padding-right: 24px;
}
.toggle-off.btn {
  padding-left: 24px;
}
.toggle.btn-lg {
  min-width: 79px;
  min-height: 45px;
}
.toggle-on.btn-lg {
  padding-right: 31px;
}
.toggle-off.btn-lg {
  padding-left: 31px;
}
.toggle-handle.btn-lg {
  width: 40px;
}
.toggle.btn-sm {
  min-width: 50px;
  min-height: 30px;
}
.toggle-on.btn-sm {
  padding-right: 20px;
}
.toggle-off.btn-sm {
  padding-left: 20px;
}
.toggle.btn-xs {
  min-width: 35px;
  min-height: 22px;
}
.toggle-on.btn-xs {
  padding-right: 12px;
}
.toggle-off.btn-xs {
  padding-left: 12px;
}
/*!
 * Datetimepicker for Bootstrap 3
 * version : 4.17.37
 * https://github.com/Eonasdan/bootstrap-datetimepicker/
 */
.bootstrap-datetimepicker-widget {
  list-style: none;
}
.bootstrap-datetimepicker-widget.dropdown-menu {
  margin: 2px 0;
  padding: 4px;
  width: 19em;
}
@media (min-width: 768px) {
  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {
    width: 38em;
  }
}
@media (min-width: 992px) {
  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {
    width: 38em;
  }
}
@media (min-width: 1200px) {
  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {
    width: 38em;
  }
}
.bootstrap-datetimepicker-widget.dropdown-menu:before,
.bootstrap-datetimepicker-widget.dropdown-menu:after {
  content: '';
  display: inline-block;
  position: absolute;
}
.bootstrap-datetimepicker-widget.dropdown-menu.bottom:before {
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #464545;
  border-bottom-color: #303030;
  top: -7px;
  left: 7px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.bottom:after {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid rgba(0, 0, 0, 0.15);
  top: -6px;
  left: 8px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.top:before {
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 7px solid #464545;
  border-top-color: #303030;
  bottom: -7px;
  left: 6px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.top:after {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid rgba(0, 0, 0, 0.15);
  bottom: -6px;
  left: 7px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.pull-right:before {
  left: auto;
  right: 6px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.pull-right:after {
  left: auto;
  right: 7px;
}
.bootstrap-datetimepicker-widget .list-unstyled {
  margin: 0;
}
.bootstrap-datetimepicker-widget a[data-action] {
  padding: 6px 0;
}
.bootstrap-datetimepicker-widget a[data-action]:active {
  box-shadow: none;
}
.bootstrap-datetimepicker-widget .timepicker-hour,
.bootstrap-datetimepicker-widget .timepicker-minute,
.bootstrap-datetimepicker-widget .timepicker-second {
  width: 54px;
  font-weight: bold;
  font-size: 1.2em;
  margin: 0;
}
.bootstrap-datetimepicker-widget button[data-action] {
  padding: 6px;
}
.bootstrap-datetimepicker-widget .btn[data-action="incrementHours"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Increment Hours";
}
.bootstrap-datetimepicker-widget .btn[data-action="incrementMinutes"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Increment Minutes";
}
.bootstrap-datetimepicker-widget .btn[data-action="decrementHours"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Decrement Hours";
}
.bootstrap-datetimepicker-widget .btn[data-action="decrementMinutes"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Decrement Minutes";
}
.bootstrap-datetimepicker-widget .btn[data-action="showHours"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Show Hours";
}
.bootstrap-datetimepicker-widget .btn[data-action="showMinutes"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Show Minutes";
}
.bootstrap-datetimepicker-widget .btn[data-action="togglePeriod"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Toggle AM/PM";
}
.bootstrap-datetimepicker-widget .btn[data-action="clear"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Clear the picker";
}
.bootstrap-datetimepicker-widget .btn[data-action="today"]::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Set the date to today";
}
.bootstrap-datetimepicker-widget .picker-switch {
  text-align: center;
}
.bootstrap-datetimepicker-widget .picker-switch::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Toggle Date and Time Screens";
}
.bootstrap-datetimepicker-widget .picker-switch td {
  padding: 0;
  margin: 0;
  height: auto;
  width: auto;
  line-height: inherit;
}
.bootstrap-datetimepicker-widget .picker-switch td span {
  line-height: 2.5;
  height: 2.5em;
  width: 100%;
}
.bootstrap-datetimepicker-widget table {
  width: 100%;
  margin: 0;
}
.bootstrap-datetimepicker-widget table td,
.bootstrap-datetimepicker-widget table th {
  text-align: center;
  border-radius: 4px;
}
.bootstrap-datetimepicker-widget table th {
  height: 20px;
  line-height: 20px;
  width: 20px;
}
.bootstrap-datetimepicker-widget table th.picker-switch {
  width: 145px;
}
.bootstrap-datetimepicker-widget table th.disabled,
.bootstrap-datetimepicker-widget table th.disabled:hover {
  background: none;
  color: #999999;
  cursor: not-allowed;
}
.bootstrap-datetimepicker-widget table th.prev::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Previous Month";
}
.bootstrap-datetimepicker-widget table th.next::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
  content: "Next Month";
}
.bootstrap-datetimepicker-widget table thead tr:first-child th {
  cursor: pointer;
}
.bootstrap-datetimepicker-widget table thead tr:first-child th:hover {
  background: #375a7f;
}
.bootstrap-datetimepicker-widget table td {
  height: 54px;
  line-height: 54px;
  width: 54px;
}
.bootstrap-datetimepicker-widget table td.cw {
  font-size: 0.8em;
  height: 20px;
  line-height: 20px;
  color: #999999;
}
.bootstrap-datetimepicker-widget table td.day {
  height: 20px;
  line-height: 20px;
  width: 20px;
}
.bootstrap-datetimepicker-widget table td.day:hover,
.bootstrap-datetimepicker-widget table td.hour:hover,
.bootstrap-datetimepicker-widget table td.minute:hover,
.bootstrap-datetimepicker-widget table td.second:hover {
  background: #375a7f;
  cursor: pointer;
}
.bootstrap-datetimepicker-widget table td.old,
.bootstrap-datetimepicker-widget table td.new {
  color: #999999;
}
.bootstrap-datetimepicker-widget table td.today {
  position: relative;
}
.bootstrap-datetimepicker-widget table td.today:before {
  content: '';
  display: inline-block;
  border: solid transparent;
  border-width: 0 0 7px 7px;
  border-bottom-color: #375a7f;
  border-top-color: #303030;
  position: absolute;
  bottom: 4px;
  right: 4px;
}
.bootstrap-datetimepicker-widget table td.active,
.bootstrap-datetimepicker-widget table td.active:hover {
  background-color: #375a7f;
  color: white;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.bootstrap-datetimepicker-widget table td.active.today:before {
  border-bottom-color: white;
}
.bootstrap-datetimepicker-widget table td.disabled,
.bootstrap-datetimepicker-widget table td.disabled:hover {
  background: none;
  color: #999999;
  cursor: not-allowed;
}
.bootstrap-datetimepicker-widget table td span {
  display: inline-block;
  width: 54px;
  height: 54px;
  line-height: 54px;
  margin: 2px 1.5px;
  cursor: pointer;
  border-radius: 4px;
}
.bootstrap-datetimepicker-widget table td span:hover {
  background: #375a7f;
}
.bootstrap-datetimepicker-widget table td span.active {
  background-color: #375a7f;
  color: white;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.bootstrap-datetimepicker-widget table td span.old {
  color: #999999;
}
.bootstrap-datetimepicker-widget table td span.disabled,
.bootstrap-datetimepicker-widget table td span.disabled:hover {
  background: none;
  color: #999999;
  cursor: not-allowed;
}
.bootstrap-datetimepicker-widget.usetwentyfour td.hour {
  height: 27px;
  line-height: 27px;
}
.bootstrap-datetimepicker-widget.wider {
  width: 21em;
}
.bootstrap-datetimepicker-widget .datepicker-decades .decade {
  line-height: 1.8em !important;
}
.input-group.date .input-group-addon {
  cursor: pointer;
}
.timepicker-picker .btn {
  width: 100%;
}
.timepicker-picker .btn [class^="icon-"],
.timepicker-picker .btn [class*=" icon-"] {
  width: 100%;
}
.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
@media (min-width: 992px) {
  .container-fluid {
    margin-right: auto;
    margin-left: auto;
    padding-left: 30px;
    padding-right: 30px;
  }
}
footer {
  margin-bottom: 2em;
}
.link-icon {
  color: inherit;
}
.link-icon.text-muted {
  color: #999999;
}
.link-icon.text-primary {
  color: #375a7f;
}
a.link-icon.text-primary:hover,
a.link-icon.text-primary:focus {
  color: #28415b;
}
.link-icon.text-success {
  color: white;
}
a.link-icon.text-success:hover,
a.link-icon.text-success:focus {
  color: #e6e6e6;
}
.link-icon.text-info {
  color: white;
}
a.link-icon.text-info:hover,
a.link-icon.text-info:focus {
  color: #e6e6e6;
}
.link-icon.text-warning {
  color: white;
}
a.link-icon.text-warning:hover,
a.link-icon.text-warning:focus {
  color: #e6e6e6;
}
.link-icon.text-danger {
  color: white;
}
a.link-icon.text-danger:hover,
a.link-icon.text-danger:focus {
  color: #e6e6e6;
}
.link-icon,
.link-icon:hover,
.link-icon:focus {
  text-decoration: none !important;
}
.cursor-progress,
.cursor-progress * {
  cursor: progress !important;
}
.cursor-wait,
.cursor-wait * {
  cursor: wait !important;
}
.loading-panel {
  background-image: url('../img/loading.gif');
  background-position: center 22px;
  background-repeat: no-repeat;
  height: 100px;
}
.loading-panel-content {
  position: relative;
  text-align: center;
  top: 60px;
  font-weight: bold;
  padding-left: 12px;
}
.quick-filter-base-width {
  width: 290px;
}
@media (max-width: 992px) and (min-width: 768px) {
  .quick-filter-base-width {
    width: 250px;
  }
}
.quick-filter-toolbar {
  width: 290px;
}
@media (max-width: 992px) and (min-width: 768px) {
  .quick-filter-toolbar {
    width: 250px;
  }
}
@media (max-width: 768px) {
  .quick-filter-toolbar {
    width: 100%;
  }
}
.quick-filter-toolbar .input-group {
  width: 100%;
}
.quick-filter-toolbar .form-group {
  margin-bottom: 5px;
}
.quick-filter-toolbar .dropdown-menu {
  width: 290px;
  margin-top: -1px;
  padding: 6px 10px;
}
@media (max-width: 992px) and (min-width: 768px) {
  .quick-filter-toolbar .dropdown-menu {
    width: 250px;
  }
}
.quick-filter-toolbar .input-group-btn .dropdown-toggle {
  border-radius: 0;
}
.quick-filter-separator {
  margin-top: 0px;
  margin-bottom: 10px;
}
pre {
  margin-bottom: 0;
  font-size: 11px;
}
.pagination > li.pagination-spacer a {
  background-color: inherit;
  cursor: default;
  padding-left: 10px;
  padding-right: 10px;
}
.pagination > li.pagination-spacer + li a {
  border-left-width: 1px;
}
.pagination > li.caption a {
  color: white;
}
.pagination > li.caption a:hover {
  color: white;
}
.pgui-overlay {
  width: 200px;
  text-align: center;
  border: 1px solid;
  background-color: white;
  color: #111111;
  height: 100px;
  border-radius: 6px;
}
.pgui-overlay .comment {
  line-height: 100px;
  font-size: larger;
  font-weight: bold;
}
.feature-list .label {
  font-size: 0.55em;
  position: relative;
  top: -4px;
}
.pgui-pagination > .pagination > ul {
  white-space: nowrap;
}
.pgui-pagination > .pagination > ul > li {
  display: inline-block;
}
*:first-child + html .pgui-pagination > .pagination > ul > li {
  display: inline;
}
.modal-big-length {
  position: absolute;
  max-height: none;
}
.control-label {
  word-wrap: break-word;
}
.navbar-fixed-top {
  z-index: 4;
}
body {
  padding-top: 60px;
}
.highlight {
  background-color: #c87f0a;
  padding: 0.15em 0;
}
.commented {
  border-bottom: 1px dotted white;
}
.text-lg {
  font-size: 1.5em;
}
.pgui-chart {
  position: relative;
  color: white;
  border-color: #bfbfbf;
}
.pgui-chart-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -16px;
  margin-left: -16px;
  width: 32px;
  height: 32px;
}
.pgui-breadcrumb-siblings {
  margin-left: 0.5em;
}
.pgui-home-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.pgui-home-col {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.pgui-home-item {
  width: 100%;
  max-width: 100%;
  background-color: #303030;
  border-radius: 15px;
  display: block;
  padding: 1.5em;
}
.pgui-home-item-wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 30px;
  width: 100%;
  max-width: 100%;
}
.pgui-home-item a {
  font-size: 1.5em;
}
.pgui-home-group {
  margin-bottom: 17px;
}
.pgui-home-group-default.pgui-home-row {
  margin-top: 34px;
}
.pgui-home-group-default.pgui-home-row:first-child {
  margin-top: 17px;
}
.pro-feature-list li {
  margin: 10px;
}
.dropdown-sub-menu > a:after {
  border-color: transparent transparent transparent white;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  content: "";
  display: block;
  float: right;
  height: 0;
  margin-right: -10px;
  margin-top: 5px;
  width: 0;
}
.dropdown-menu.sub-menu {
  left: 100%;
  top: 0;
  margin-top: -1px;
}
.dropdown-sub-menu:hover .dropdown-menu {
  display: block;
}
.dropdown-menu.selection-filters {
  position: relative;
  border: 0;
  box-shadow: none;
  display: block;
}
.modal {
  text-align: center;
  padding: 0 !important;
}
.modal:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -4px;
}
.modal-dialog {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}
.modal-top .modal-dialog {
  vertical-align: top;
}
.SQLGeneratorEvaluationVersion {
  margin-bottom: -5px;
  padding-top: 0.5em;
  padding-bottom: 0.5em;
}
@media (max-width: 768px) {
  .SQLGeneratorEvaluationVersion {
    float: left;
    width: 87%;
  }
}
@media (max-width: 500px) {
  .SQLGeneratorEvaluationVersion {
    float: left;
    width: 83%;
    padding-left: 0.5em;
    padding-right: 0.5em;
  }
}
.SQLGeneratorEvaluationVersion-head {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 24px;
}
@media (max-width: 1100px) {
  .SQLGeneratorEvaluationVersion-head {
    font-size: 20px;
  }
}
@media (max-width: 930px) {
  .SQLGeneratorEvaluationVersion-head {
    font-size: 16px;
  }
}
@media (max-width: 460px) {
  .SQLGeneratorEvaluationVersion-head {
    font-size: 14px;
  }
}
.SQLGeneratorEvaluationVersion-justify {
  width: 9000px;
  max-width: 100%;
  height: 0;
  display: block;
}
@media (max-width: 768px) {
  .SQLGeneratorEvaluationVersion-justify {
    display: none;
  }
}
.form-actions {
  background-color: #303030;
  border-top: 1px solid transparent;
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 10px 0 5px;
}
.form-actions-top {
  border-bottom: 1px solid transparent;
  border-top: 0;
  margin-top: 0;
}
.form-actions .form-group {
  margin-bottom: 0;
}
.form-actions .btn-toolbar > .btn-group {
  margin-bottom: 5px;
}
.btn-loading {
  background-image: url('../img/btn-loading.png') !important;
  background-position: center 0;
  background-repeat: repeat-x;
  -webkit-animation: btn-loading 2.5s linear infinite;
  animation: btn-loading 2.5s linear infinite;
}
@-webkit-keyframes btn-loading {
  0% {
    background-position: 0;
  }
  100% {
    background-position: 126px;
  }
}
@keyframes btn-loading {
  0% {
    background-position: 0;
  }
  100% {
    background-position: 126px;
  }
}
.page-header.form-header {
  border-bottom: 0;
}
.required-mark {
  color: #e74c3c;
}
.pgui-login {
  max-width: 364px;
  min-width: 195px;
  padding: 17px 25.5px;
  margin: 1em auto;
}
.pgui-login-avatar {
  width: 80.75px;
  height: 80.75px;
  margin: 0.4em auto 1.8em;
  border-radius: 50%;
}
.pgui-login-footer {
  border-top: 1px solid #888888;
  padding-top: 15px;
  font-size: 85%;
}
.pgui-login h3 {
  text-align: center;
}
#registrationForm .form-error-container,
#recoveringPasswordForm .form-error-container,
#resetPasswordForm .form-error-container {
  margin-bottom: 0px;
}
table.pgui-cascading-editor {
  width: 100%;
  table-layout: fixed;
}
table.pgui-cascading-editor tr > td:first-child {
  text-align: right;
  padding-right: 1em;
  width: 25%;
  white-space: nowrap;
}
table.pgui-cascading-editor tr > td {
  padding-bottom: 2px;
}
table.pgui-cascading-editor tr td .select2-container {
  width: 100% !important;
}
.form-horizontal .form-group {
  margin-left: 0;
  margin-right: 0;
}
.form-horizontal .form-group-label {
  padding-right: 0;
  margin-bottom: 0;
}
.form-horizontal .form-group .help-block {
  margin-bottom: 0;
}
.form-horizontal .form-group .control-label {
  padding-right: 0;
}
.form-horizontal .form-group-label .control-label {
  width: 100%;
}
.form-horizontal .col-input .input-checkbox,
.form-horizontal .col-input .input-range {
  padding-top: 5px;
}
.form-horizontal .col-input .toggle.btn-xs {
  top: 3px;
}
.form-horizontal .col-input .toggle.btn-sm {
  top: 2px;
}
.form-editor-container {
  margin-top: 15px;
}
.form-error-container-bottom {
  margin-top: 20px;
  margin-bottom: -20px;
}
.form-horizontal .col-input {
  min-height: 29px;
}
.form-collection-actions {
  margin-bottom: 15px;
}
.input-checkbox {
  display: inline-block;
  margin-right: 4px;
}
.form-horizontal .input-checkbox {
  display: block;
  margin-right: 0;
}
.form-control-nested-form .select2-choice,
.form-control-nested-form .select2-choices {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.form-static .form-group {
  margin-bottom: 0;
}
.form-static .control-label {
  margin-bottom: 0;
}
.form-static:not(.form-horizontal) .form-control-static {
  padding-top: 0;
}
#form-group-fields-to-be-updated {
  margin-bottom: 0;
}
#form-group-fields-to-be-updated hr {
  margin-top: 15px;
  margin-bottom: 15px;
}
#form-group-fields-to-be-updated .select2-choices {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
#form-group-fields-to-be-updated .input-group-addon {
  cursor: pointer;
}
.form-add-another-record {
  margin-top: 20px;
}
.form-tabs-container {
  margin: 0 5px 20px 5px;
}
.signature-pad {
  height: auto;
  position: relative;
}
.signature-pad canvas {
  border: 1px solid #f1f1f1;
  border-radius: 4px;
}
.pgui-toggle-checkbox {
  display: none;
}
@font-face {
  font-family: 'phpgenerator';
  src: url('../fonts/phpgenerator.eot?izi13q');
  src: url('../fonts/phpgenerator.eot?izi13q#iefix') format('embedded-opentype'), url('../fonts/phpgenerator.ttf?izi13q') format('truetype'), url('../fonts/phpgenerator.woff?izi13q') format('woff'), url('../fonts/phpgenerator.svg?izi13q#phpgenerator') format('svg');
  font-weight: normal;
  font-style: normal;
}
[class^="icon-"],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'phpgenerator' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-selection-filter:before {
  content: "\e90f";
}
.icon-custom-filter:before {
  content: "\e90e";
}
.icon-exclamation:before {
  content: "\e90d";
}
.icon-folder-open:before {
  content: "\e90c";
}
.icon-folder-open-o:before {
  content: "\e90b";
}
.icon-file:before {
  content: "\e90a";
}
.icon-upload:before {
  content: "\e909";
}
.icon-twitter-square:before {
  content: "\f081";
}
.icon-facebook-square:before {
  content: "\f082";
}
.icon-twitter:before {
  content: "\f099";
}
.icon-facebook:before {
  content: "\f09a";
}
.icon-facebook-f:before {
  content: "\f09a";
}
.icon-google-plus:before {
  content: "\f0d5";
}
.icon-youtube-square:before {
  content: "\f166";
}
.icon-youtube:before {
  content: "\f167";
}
.icon-windows:before {
  content: "\f17a";
}
.icon-home:before {
  content: "\e908";
}
.icon-filter-alt:before {
  content: "\e907";
}
.icon-today:before {
  content: "\e900";
}
.icon-copy:before {
  content: "\e906";
}
.icon-file-doc:before {
  content: "\e901";
}
.icon-file-pdf:before {
  content: "\e902";
}
.icon-file-xls:before {
  content: "\e903";
}
.icon-file-xml:before {
  content: "\e904";
}
.icon-file-csv:before {
  content: "\e905";
}
.icon-folder-o:before {
  content: "\e600";
}
.icon-folder:before {
  content: "\e601";
}
.icon-play:before {
  content: "\e602";
}
.icon-disable:before {
  content: "\e603";
}
.icon-question:before {
  content: "\e604";
}
.icon-settings:before {
  content: "\e605";
}
.icon-view:before {
  content: "\e606";
}
.icon-edit:before {
  content: "\e607";
}
.icon-page:before {
  content: "\e608";
}
.icon-rss:before {
  content: "\e609";
}
.icon-time:before {
  content: "\e60a";
}
.icon-sort:before {
  content: "\e60b";
}
.icon-export:before {
  content: "\e60c";
}
.icon-filter:before {
  content: "\e60e";
}
.icon-permissions:before {
  content: "\e610";
}
.icon-delete-selected:before {
  content: "\e611";
}
.icon-keys-pk-fk:before {
  content: "\e612";
}
.icon-keys-fk:before {
  content: "\e613";
}
.icon-add-condition:before {
  content: "\e614";
}
.icon-keys-pk:before {
  content: "\e615";
}
.icon-operator-ends-with:before {
  content: "\e616";
}
.icon-operator-begins-with:before {
  content: "\e617";
}
.icon-add-group:before {
  content: "\e618";
}
.icon-operator-is-like:before {
  content: "\e619";
}
.icon-search:before {
  content: "\e61a";
}
.icon-user:before {
  content: "\e61b";
}
.icon-ok:before {
  content: "\e61c";
}
.icon-remove:before {
  content: "\e61d";
}
.icon-filter-reset:before {
  content: "\e61e";
}
.icon-download:before {
  content: "\e61f";
}
.icon-page-refresh:before {
  content: "\e620";
}
.icon-print-page:before {
  content: "\e621";
}
.icon-chevron-left:before {
  content: "\e622";
}
.icon-chevron-right:before {
  content: "\e623";
}
.icon-arrow-left:before {
  content: "\e624";
}
.icon-plus:before {
  content: "\e625";
}
.icon-minus:before {
  content: "\e626";
}
.icon-datetime-picker:before {
  content: "\e627";
}
.icon-chevron-up:before {
  content: "\e628";
}
.icon-chevron-down:before {
  content: "\e629";
}
.icon-password-change:before {
  content: "\e62a";
}
.icon-list:before {
  content: "\e62b";
}
.icon-operator-is-not-like:before {
  content: "\e62c";
}
.icon-detail-additional:before {
  content: "\e62d";
}
.icon-export-csv:before {
  content: "\e62e";
}
.icon-detail-plus:before {
  content: "\e62f";
}
.icon-operator-is-blank:before {
  content: "\e630";
}
.icon-operator-is-not-blank:before {
  content: "\e631";
}
.icon-detail-minus:before {
  content: "\e632";
}
.icon-sort-asc:before {
  content: "\e633";
}
.icon-sort-desc:before {
  content: "\e634";
}
.icon-export-pdf:before {
  content: "\e635";
}
.icon-export-word:before {
  content: "\e636";
}
.icon-export-excel:before {
  content: "\e637";
}
.icon-export-xml:before {
  content: "\e638";
}
.icon-user-add:before {
  content: "\e639";
}
.icon-user-delete:before {
  content: "\e63a";
}
.icon-old-copy:before {
  content: "\e63b";
}
.icon-operator-contains:before {
  content: "\2208";
}
.icon-operator-does-not-contain:before {
  content: "\2209";
}
.icon-and:before {
  content: "\222a";
}
.icon-or:before {
  content: "\2229";
}
.icon-operator-equals:before {
  content: "=";
}
.icon-operator-does-not-equal:before {
  content: "\2260";
}
.icon-operator-greater:before {
  content: "\003e";
}
.icon-operator-greater-or-equal:before {
  content: "\2265";
}
.icon-operator-less:before {
  content: "\003c";
}
.icon-operator-less-or-equal:before {
  content: "\2264";
}
.btn [class^="icon-"],
.btn [class*=" icon-"],
[class^="icon-operator-"],
[class*=" icon-operator-"] {
  display: inline-block;
  width: 1em;
  text-align: center;
}
.icon-enable:before {
  content: "\e61c";
}
.sidebar {
  position: fixed;
  width: 190px;
  float: left;
  height: calc(100% - 41px);
  top: 41px;
  left: -190px;
  bottom: 0;
  z-index: 5;
  background: #303030;
  border-right: 1px solid #464545;
  transition: left 0.3s ease-out;
  overflow-y: auto;
}
.sidebar .nav .nav-divider {
  opacity: 0.5;
  margin: 3px 8px;
  margin-top: 1px !important;
  margin-bottom: 1px !important;
}
.sidebar-backdrop {
  display: none;
}
.sidebar .content {
  padding-top: 8px;
}
@media (max-width: 991px) {
  .sidebar {
    top: 0;
    height: 100vh;
    padding-bottom: 40px;
    left: -210px;
  }
  .sidebar-active {
    overflow: hidden;
  }
  .sidebar-active .sidebar {
    left: 0;
  }
  .sidebar-active .sidebar-backdrop {
    display: block;
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background-color: black;
    opacity: 0.3;
    z-index: 4;
    height: 100%;
  }
  .sidebar-active .icon-toggle-sidebar:before {
    content: "\e622";
  }
}
@media (min-width: 992px) {
  .sidebar-desktop-active .sidebar-owner {
    padding-left: 190px;
  }
  .sidebar-desktop-active .sidebar {
    left: 0;
  }
  .sidebar-desktop-active .icon-toggle-sidebar:before {
    content: "\e622";
  }
}
.icon-toggle-sidebar:before {
  content: "\e623";
}
.toggle-sidebar {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  height: 40px;
  min-width: 32px;
  width: 34px;
  margin-right: 15px;
}
.toggle-sidebar .icon-toggle-sidebar {
  width: 100%;
  height: 34px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
  color: white;
}
.toggle-sidebar .icon-toggle-sidebar:hover {
  color: #00bc8c;
}
.sidebar-owner {
  transition: padding-left 0.3s ease-out;
}
.sidebar-nav-item {
  position: relative;
  display: block;
  padding: 3px 8px;
  border-radius: 0;
  cursor: pointer;
}
.sidebar-nav-item [class^="icon-"],
.sidebar-nav-item [class*=" icon-"] {
  margin-right: 0.3em;
  width: 1em;
  position: relative;
  display: inline-block;
}
.sidebar-nav-item .link-icon > [class^="icon-"],
.sidebar-nav-item .link-icon > [class*=" icon-"] {
  margin-right: 0;
}
.sidebar-nav-item:before {
  content: ' ';
  display: inline-block;
  width: 1.3em;
}
.active > .sidebar-nav-item,
.active > .sidebar-nav-item:hover,
.active > .sidebar-nav-item:focus {
  color: white;
  background-color: #375a7f;
}
.sidebar-nav-head > .sidebar-nav-item .caret {
  transform: rotate(0deg);
  transition: transform 0.2s ease;
}
.sidebar-nav-head > .sidebar-nav-item.collapsed .caret {
  transform: rotate(-90deg);
}
.sidebar-nav-head > .sidebar-nav-item:before {
  display: none;
}
.sidebar-nav-head .nav > *:last-child {
  margin-bottom: 1em;
}
.sidebar-nav > *:first-child .sidebar-nav-head > .sidebar-nav-item {
  margin-top: 0;
}
.nav-vertical-divider {
  height: 24px;
  width: 1px;
  margin: 8px 8px;
  background: #777777;
  opacity: 0.7;
}
.navbar-inverse .nav-vertical-divider {
  background: white;
}
@media (max-width: 768px) {
  .navbar-collapse .nav-vertical-divider {
    display: none;
  }
}
.pg-inline-edit-container {
  text-align: left;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}
.pg-inline-edit-container-loading {
  text-align: center;
}
.pg-inline-edit-container .form-error-container {
  margin-bottom: 0;
}
.addition-block:before,
.addition-block:after {
  content: " ";
  display: table;
}
.addition-block:after {
  clear: both;
}
.addition-block > div {
  margin-bottom: 1em;
}
@media (max-width: 480px) {
  .addition-block-right {
    float: left !important;
    clear: both;
  }
}
@media (min-width: 1200px) {
  .page-settings-hightlight-lg label {
    color: white;
    color: #00bc8c;
  }
  a.page-settings-hightlight-lg label:hover,
  a.page-settings-hightlight-lg label:focus {
    color: #e6e6e6;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .page-settings-hightlight-md label {
    color: white;
    color: #00bc8c;
  }
  a.page-settings-hightlight-md label:hover,
  a.page-settings-hightlight-md label:focus {
    color: #e6e6e6;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .page-settings-hightlight-sm label {
    color: white;
    color: #00bc8c;
  }
  a.page-settings-hightlight-sm label:hover,
  a.page-settings-hightlight-sm label:focus {
    color: #e6e6e6;
  }
}
@media (max-width: 767px) {
  .page-settings-hightlight-xs label {
    color: white;
    color: #00bc8c;
  }
  a.page-settings-hightlight-xs label:hover,
  a.page-settings-hightlight-xs label:focus {
    color: #e6e6e6;
  }
}
.page-settings-label-container {
  width: 50%;
  vertical-align: top !important;
}
.page-settings-control-container select {
  width: 100% !important;
}
.page-settings-control-container input {
  width: 50% !important;
}
.page-settings-cardscount {
  padding: 0;
}
.page-settings-cardscount-table {
  background: transparent;
}
.page-settings-cardscount label {
  margin-bottom: 0;
}
.pg-row.active .well {
  background-color: #464545;
}
.pg-row.success .well {
  background-color: #00bc8c;
}
.pg-row.info .well {
  background-color: #3498db;
}
.pg-row.warning .well {
  background-color: #f39c12;
}
.pg-row.danger .well {
  background-color: #e74c3c;
}
.pg-row-checkbox {
  font-family: 'phpgenerator';
  color: white;
  color: #e74c3c;
}
a.pg-row-checkbox:hover,
a.pg-row-checkbox:focus {
  color: #e6e6e6;
}
.pg-row-checkbox:before {
  content: "\e61d";
}
.pg-row-checkbox.checked {
  color: white;
  color: #00bc8c;
}
a.pg-row-checkbox.checked:hover,
a.pg-row-checkbox.checked:focus {
  color: #e6e6e6;
}
.pg-row-checkbox.checked:before {
  content: "\e61c";
}
.pg-row-list:before,
.pg-row-list:after {
  content: '';
  display: none;
}
.table th.details,
.table td.details {
  text-align: left;
  width: 40px;
}
.table th.filterable {
  position: relative;
  padding-right: 32px;
}
.table thead {
  background-color: #222222;
}
.table thead tr th .icon-sort-asc,
.table thead tr th .icon-sort-desc {
  margin-left: 4px;
}
.table thead tr th.sortable {
  cursor: pointer;
}
.table thead.header-bordered,
.table thead.header-bordered > tr > th,
.table thead.header-bordered > tr > td {
  border: 1px solid #464545;
}
.table tbody tr td {
  vertical-align: middle;
}
.table tbody tr td.line-number {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
.table tbody tr td .empty-grid {
  margin-bottom: 0;
}
.table-totals {
  width: inherit;
}
.table .row-selection {
  text-align: left;
}
.table .row-selection ul {
  margin-bottom: 0;
  margin-left: 0;
}
.table .row-selection ul input[type="checkbox"] {
  margin: 0;
}
.table .row-selection ul li {
  padding: 0;
}
.table .row-selection ul .link-icon {
  font-size: 1.2em;
}
.detail-quick-access {
  white-space: nowrap;
}
.expand-details,
.expand-all-details {
  display: inline-block;
}
.expand-all-details .icon-detail-plus,
.expand-details .icon-detail-plus,
.expand-all-details .icon-detail-minus,
.expand-details .icon-detail-minus {
  display: none;
}
.expand-all-details.collapsed .icon-detail-plus,
.expand-details.collapsed .icon-detail-plus {
  display: inline;
}
.expand-all-details.expanded .icon-detail-minus,
.expand-details.expanded .icon-detail-minus {
  display: inline;
}
.expand-details + .link-icon {
  margin-left: 0.15em;
}
.pg-row-list .operation-column .link-icon,
.pg-row-list .details .link-icon {
  padding: 0.3em;
  display: inline-block;
  font-size: 1.2em;
}
.pg-row-list .details .link-icon {
  padding-right: 0.15em;
}
.pg-row-list .details .link-icon + .link-icon {
  padding-left: 0.15em;
  margin-left: 0;
}
thead .details .link-icon {
  padding: 0 0.3em;
  display: inline-block;
  font-size: 1.2em;
  position: relative;
  top: 0.15em;
}
.operation-item {
  display: inline-block;
  margin: 0;
}
@media (max-width: 992px) {
  .operation-item {
    margin: 0;
  }
}
.operation-column {
  white-space: nowrap;
}
.grid-card-column-filter {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #303030;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  -webkit-box-shadow: none;
  box-shadow: none;
  margin-bottom: 1em;
  padding: 0;
}
.grid-card-column-filter blockquote {
  border-color: #dddddd;
  border-color: rgba(0, 0, 0, 0.15);
}
.grid-card div.pg-row-list {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: stretch;
  -webkit-align-items: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -webkit-align-content: stretch;
  -ms-flex-line-pack: stretch;
  align-content: stretch;
}
.grid-card-item {
  margin-bottom: 30px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.grid-card-item > .well {
  width: 100%;
  max-width: 100%;
  border-color: rgba(55, 90, 127, 0.5);
  margin-bottom: 0;
  background: #303030;
}
.grid-card-item .row-selection {
  padding: 0.3em 0;
}
.grid-card-item .line-number {
  padding: 0.3em 0;
  margin-top: 0.1em;
}
.grid-card-item-control {
  margin-bottom: 0.8em;
}
.grid-card-item-control .details {
  margin-right: 0.7em;
}
.grid-card-item-select {
  line-height: 18px;
  margin-right: 0.9em;
}
.grid-card-item-select input {
  margin-top: 0;
}
.grid-card-item-data {
  clear: both;
}
.grid-card-item-data .table {
  width: 100%;
  margin-bottom: 0;
  table-layout: fixed;
}
.grid-card-item-data .table th {
  width: 30%;
  text-align: right;
  padding-right: 1em;
}
.grid-card-item-details-expanded .well:after,
.grid-card-item-details-expanded .well:before {
  content: '';
  position: absolute;
  left: 50%;
  bottom: 0;
  width: 0;
  height: 0;
  border-style: solid;
}
.grid-card-item-details-expanded .well:after {
  z-index: 1;
  bottom: -33px;
  margin-left: -15px;
  border-color: transparent transparent #303030 transparent;
  border-width: 15px;
}
.grid-card-item-details-expanded .well:before {
  bottom: -35px;
  margin-left: -19px;
  border-color: transparent transparent rgba(55, 90, 127, 0.5) transparent;
  border-width: 19px;
}
.grid-card .line-number {
  margin-right: 10px;
}
.grid-card .empty-grid {
  text-align: center;
  width: 100%;
}
.pgui-field-options {
  display: block;
  margin: 8px 0;
}
.pgui-field-embedded-video {
  display: inline-block;
  position: relative;
  opacity: 1;
  cursor: pointer;
}
.pgui-field-embedded-video-thumb {
  width: 100px;
  height: 75px;
}
.pgui-field-embedded-video-preloader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.3s ease;
}
.pgui-field-embedded-video-icon,
.pgui-field-embedded-video-thumb {
  opacity: 1;
  transition: opacity 0.3s ease;
}
.pgui-field-embedded-video-fade {
  opacity: 0;
  transition: opacity 0.3s ease;
}
.pgui-field-embedded-video-icon {
  font-size: 40px;
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-shadow: 0 0 5px black;
}
.pgui-field-embedded-video-iframe {
  width: 100%;
  height: 100%;
  min-height: 320px;
}
@media (max-width: 767px) {
  .pg-row .pgui-field-embedded-video-thumb {
    display: none;
    width: 24px;
    height: 24px;
  }
  .pg-row .pgui-field-embedded-video-icon {
    font-size: 24px;
    color: #0ce3ac;
    text-shadow: none;
  }
}
.webui-popover-cell-edit {
  min-width: 200px;
  max-width: 400px;
}
.webui-popover-cell-edit .webui-popover-content {
  min-height: 30px;
  overflow: visible;
}
.webui-popover-cell-edit .form-error-container {
  margin-top: 0;
  margin-bottom: 0;
}
.webui-popover i.icon-refresh {
  background-size: 30px;
  margin-top: -15px;
}
.webui-popover-backdrop {
  background: none;
}
.pgui-cell-edit-button {
  display: block;
  position: absolute;
  right: 2px;
  top: 2px;
  font-size: 16px;
  color: #0ce3ac;
  cursor: pointer;
}
.pgui-cell-edit {
  position: relative;
}
.grid-details .close {
  color: white;
}
.grid-details-table {
  text-align: left;
  border-left: 2px solid #375a7f;
}
.grid-details-table .table {
  background-color: transparent !important;
}
.grid-details-table:hover {
  background-color: transparent !important;
}
.grid-details .pgui-pagination .pagination {
  margin-top: 0;
}
.grid-details-loading {
  text-align: center;
  padding: 1em;
}
.grid-details-tabs {
  margin-bottom: 1em;
}
.grid-details .tab-content {
  padding: 1em;
  padding-top: 0;
}
.select2-drop-active {
  z-index: 9999;
  border: 1px solid #66afe9 !important;
}
.column-filter {
  min-width: 170px;
  max-width: 300px;
}
.column-filter-trigger {
  text-decoration: none !important;
  color: white;
  position: absolute;
  padding: 10px;
  margin-top: -10px;
}
.column-filter-trigger:hover,
.column-filter-trigger:active,
.column-filter-trigger:focus {
  color: #0ce3ac;
  text-decoration: none;
}
.column-filter-trigger-active {
  color: #0ce3ac;
}
.column-filter-component {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow-x: hidden;
}
.column-filter-component hr {
  margin: 2px 0;
}
.column-filter-choices {
  max-height: 200px;
  overflow-y: auto;
  padding: 4px 0;
}
.column-filter-choices label {
  display: inline;
  max-width: inherit;
  font-weight: 400;
  white-space: nowrap;
  vertical-align: middle;
  line-height: 2;
}
.column-filter-choices .has-children {
  margin-left: -20px;
}
.column-filter-choices-children {
  margin-left: 30px;
  display: none;
}
.column-filter-choices-children-has-children {
  margin-left: 12px;
}
.column-filter-choices-caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid \9;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  border-width: 5px;
  margin-right: 4px;
  cursor: pointer;
  margin-top: -2px;
  transform: rotate(-90deg);
}
.column-filter-choices-caret-wrapper {
  color: inherit;
  text-decoration: none !important;
  vertical-align: middle;
}
.column-filter-choices-caret-wrapper.collapsed .column-filter-choices-caret {
  transform: rotate(0deg);
}
.column-filter-choices .checkbox:last-of-type {
  margin-bottom: 0;
}
.column-filter-separator {
  margin: 0;
}
.column-filter-separator:first-of-type {
  margin-top: 4px;
}
.column-filter .btn-toolbar {
  margin-top: 12px;
}
.column-filter-search {
  position: relative;
  width: 100%;
  margin-bottom: 12px;
}
.column-filter-search input.column-filter-search-input {
  padding-right: 29px;
}
.column-filter-search-clear {
  position: absolute;
  width: 29px;
  top: 50%;
  right: 0;
  margin-top: -14.5px;
  line-height: 29px;
  color: #464545;
  text-align: center;
  text-decoration: none;
  opacity: 0.6;
  z-index: 2;
}
.column-filter-search-clear:hover,
.column-filter-search-clear:focus,
.column-filter-search-clear:active {
  text-decoration: none;
  color: #464545;
  opacity: 1;
}
.column-filter-search-empty {
  margin-bottom: 8px;
}
.column-filter-searching {
  margin-bottom: 8px;
}
.column-filter-searching img {
  max-width: 18px;
}
.filter-status {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}
.filter-status-value {
  position: relative;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1.428571429;
  border-radius: 4px;
  border-radius: 8px;
  margin-bottom: 1em;
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 200px;
  padding-right: 40px;
  margin-right: 8px;
}
.filter-status-value:last-of-type {
  margin-right: 0;
}
.filter-status-value-controls {
  position: absolute;
  top: 0;
  right: 0;
  padding: 4px 8px;
  font-size: 12px;
  line-height: 1.428571429;
  border-radius: 4px;
}
.filter-status-value-controls > a {
  text-decoration: none !important;
  padding: 0 4px;
}
.filter-status-value-controls > a:hover {
  opacity: 0.5;
}
.filter-status-value-expr {
  margin-left: 0.25em;
}
.filter-status-value-disabled .filter-status-value-expr {
  text-decoration: line-through;
}
.filter-status-value-disabled-component {
  text-decoration: line-through;
}
.filter-status-value-editable {
  padding-right: 48px;
}
.filter-status-value-quick-filter {
  color: white;
  border: 1px solid white;
  background-color: #f39c12;
}
.filter-status-value-quick-filter .filter-status-value-icon,
.filter-status-value-quick-filter .filter-status-value-controls,
.filter-status-value-quick-filter .filter-status-value-controls > a,
.filter-status-value-quick-filter .filter-status-value-controls > a:hover {
  color: white;
}
.filter-status-value-column-filter {
  color: white;
  border: 1px solid white;
  background-color: #3498db;
}
.filter-status-value-column-filter .filter-status-value-icon,
.filter-status-value-column-filter .filter-status-value-controls,
.filter-status-value-column-filter .filter-status-value-controls > a,
.filter-status-value-column-filter .filter-status-value-controls > a:hover {
  color: white;
}
.filter-status-value-filter-builder {
  color: white;
  border: 1px solid white;
  background-color: #00bc8c;
}
.filter-status-value-filter-builder .filter-status-value-icon,
.filter-status-value-filter-builder .filter-status-value-controls,
.filter-status-value-filter-builder .filter-status-value-controls > a,
.filter-status-value-filter-builder .filter-status-value-controls > a:hover {
  color: white;
}
.filter-status-value-selection-filter {
  color: white;
  border: 1px solid white;
  background-color: #f39c12;
}
.filter-status-value-selection-filter .filter-status-value-icon,
.filter-status-value-selection-filter .filter-status-value-controls,
.filter-status-value-selection-filter .filter-status-value-controls > a,
.filter-status-value-selection-filter .filter-status-value-controls > a:hover {
  color: white;
}
.filter-status-value-selection-filter i.filter-status-value-icon {
  font-size: 1.4em;
}
.filter-status .btn-group {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
@media (max-width: 992px) {
  .filter-status {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
  .filter-status-value {
    margin-right: 0;
  }
}
.filter-builder .input-group {
  table-layout: fixed;
}
.filter-builder-group-wrapper {
  padding: 8px;
  box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.2);
  margin: 4px 0;
}
.filter-builder-group-disabled .filter-builder-group-operator:first-of-type {
  text-decoration: line-through;
}
.filter-builder-group-operator {
  line-height: 28px;
}
.filter-builder-group-operator .dropdown {
  display: inline-block;
}
.filter-builder-group-operator .dropdown > .dropdown-toggle {
  cursor: pointer;
  text-transform: lowercase;
  color: #e74c3c;
  padding: 1px 2px 3px 2px;
}
.filter-builder-group-operator .dropdown > .dropdown-toggle.btn-default,
.filter-builder-group-operator .dropdown > .dropdown-toggle.btn-default:hover,
.filter-builder-group-operator .dropdown.open > .dropdown-toggle.btn-default {
  text-decoration: none;
  background: transparent;
  box-shadow: none;
  border: 0 none;
}
.filter-builder-group-operator .dropdown > .dropdown-toggle.btn-default .text-underline,
.filter-builder-group-operator .dropdown > .dropdown-toggle.btn-default:hover .text-underline,
.filter-builder-group-operator .dropdown.open > .dropdown-toggle.btn-default .text-underline {
  text-decoration: underline;
}
.filter-builder-group-content {
  table-layout: fixed;
  width: 100%;
  margin: 8px 0;
}
.filter-builder-condition {
  font-size: 0;
  margin-bottom: 4px;
}
.filter-builder-condition td {
  padding-bottom: 4px;
  vertical-align: middle;
}
.filter-builder-group + .filter-builder-condition td {
  padding-top: 4px;
}
.filter-builder-condition-field,
.filter-builder-condition-operator {
  width: 25%;
  padding-right: 4px;
}
.filter-builder-condition-value {
  padding-right: 4px;
}
.filter-builder-condition-value-multiple {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 12px;
}
.filter-builder-condition-value-multiple > div {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}
.filter-builder-condition-value-divider {
  line-height: 29px;
  padding: 0 4px;
}
.filter-builder-condition-actions {
  width: 56px;
}
.filter-builder-condition-actions a {
  border: none;
}
.filter-builder-condition-actions a:hover {
  border: none;
}

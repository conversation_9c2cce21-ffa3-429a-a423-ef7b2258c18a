/* ===================================================================
   Composants Small et Select2 - Thème Minimaliste
   =================================================================== */

/* ===================================================================
   COMPOSANTS SMALL
   =================================================================== */

/* Boutons small */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    font-weight: 500;
}

.btn-sm i {
    font-size: 0.7rem;
}

/* Inputs small */
.form-control-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.25rem;
}

/* Selects small */
.form-select-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.25rem;
}

/* Labels small */
.form-label-sm {
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: var(--text-color);
}

/* Cards small */
.card-sm {
    padding: 0.75rem;
    border-radius: 0.375rem;
}

.card-sm .card-header {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.card-sm .card-body {
    padding: 0.75rem;
}

/* Tables small */
.table-sm th,
.table-sm td {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Badges small */
.badge-sm {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
    border-radius: 0.2rem;
}

/* ===================================================================
   SELECT2 THEME CLASSIC
   =================================================================== */

/* Container principal */
.select2-container {
    width: 100% !important;
    font-size: 0.75rem;
}

.select2-container--classic .select2-selection--single {
    height: 1.875rem !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 0.25rem !important;
    background-color: var(--white) !important;
    padding: 0 !important;
}

.select2-container--classic .select2-selection--single .select2-selection__rendered {
    color: var(--text-color) !important;
    line-height: 1.875rem !important;
    padding-left: 0.5rem !important;
    padding-right: 1.5rem !important;
    font-size: 0.75rem !important;
}

.select2-container--classic .select2-selection--single .select2-selection__arrow {
    height: 1.875rem !important;
    right: 0.5rem !important;
    width: 1rem !important;
}

.select2-container--classic .select2-selection--single .select2-selection__arrow b {
    border-color: var(--text-muted) transparent transparent transparent !important;
    border-style: solid !important;
    border-width: 4px 4px 0 4px !important;
    height: 0 !important;
    left: 50% !important;
    margin-left: -4px !important;
    margin-top: -2px !important;
    position: absolute !important;
    top: 50% !important;
    width: 0 !important;
}

/* Container ouvert */
.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent var(--text-muted) transparent !important;
    border-width: 0 4px 4px 4px !important;
}

/* Dropdown */
.select2-dropdown {
    border: 1px solid var(--border-color) !important;
    border-radius: 0.25rem !important;
    box-shadow: var(--shadow-md) !important;
    background-color: var(--white) !important;
    font-size: 0.75rem !important;
}

.select2-container--classic .select2-results__option {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.75rem !important;
    line-height: 1.5 !important;
    color: var(--text-color) !important;
}

.select2-container--classic .select2-results__option--highlighted[aria-selected] {
    background-color: var(--primary-color) !important;
    color: var(--white) !important;
}

.select2-container--classic .select2-results__option[aria-selected=true] {
    background-color: var(--gray-100) !important;
    color: var(--text-color) !important;
}

/* Search box */
.select2-container--classic .select2-search--dropdown .select2-search__field {
    border: 1px solid var(--border-color) !important;
    border-radius: 0.25rem !important;
    padding: 0.25rem 0.5rem !important;
    font-size: 0.75rem !important;
    margin: 0.25rem !important;
    width: calc(100% - 0.5rem) !important;
}

/* Focus states */
.select2-container--classic.select2-container--focus .select2-selection--single {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.1) !important;
}

/* Disabled state */
.select2-container--classic.select2-container--disabled .select2-selection--single {
    background-color: var(--gray-100) !important;
    color: var(--text-muted) !important;
    cursor: not-allowed !important;
}

/* ===================================================================
   IFRAME FIXES
   =================================================================== */

/* Assurer que les iframes sont cliquables */
.tab-content iframe {
    pointer-events: auto !important;
    z-index: 1 !important;
    position: relative !important;
}

/* Corriger les problèmes de z-index */
.tab-pane {
    position: relative !important;
    z-index: 1 !important;
}

.tab-pane.active {
    z-index: 2 !important;
}

/* Assurer que les modales sont au-dessus des iframes */
.modal {
    z-index: 9999 !important;
}

.modal-backdrop {
    z-index: 9998 !important;
}

/* ===================================================================
   AMÉLIORATIONS GÉNÉRALES
   =================================================================== */

/* Espacement cohérent pour les petits composants */
.form-group-sm {
    margin-bottom: 0.5rem;
}

.form-group-sm .form-label {
    margin-bottom: 0.25rem;
    font-size: 0.75rem;
}

/* Action buttons small */
.action-buttons-sm {
    display: flex;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.action-buttons-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Table responsive small */
.table-responsive-sm {
    font-size: 0.75rem;
}

/* Stats cards small */
.stats-card-sm {
    padding: 0.75rem;
    text-align: center;
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    box-shadow: var(--shadow-sm);
}

.stats-card-sm .stats-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.stats-card-sm .stats-label {
    font-size: 0.65rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===================================================================
   RESPONSIVE ADJUSTMENTS
   =================================================================== */

@media (max-width: 768px) {
    .select2-container {
        font-size: 0.7rem;
    }
    
    .select2-container--classic .select2-selection--single {
        height: 1.75rem !important;
    }
    
    .select2-container--classic .select2-selection--single .select2-selection__rendered {
        line-height: 1.75rem !important;
        font-size: 0.7rem !important;
    }
    
    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
    
    .form-control-sm,
    .form-select-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
}

/* ===================================================================
   PRINT STYLES
   =================================================================== */

@media print {
    .select2-container,
    .select2-dropdown {
        display: none !important;
    }
    
    .form-control,
    .form-select {
        border: 1px solid #000 !important;
        background: transparent !important;
    }
}

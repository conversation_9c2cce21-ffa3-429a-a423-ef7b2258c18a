/* ===================================================================
   Iframe Fix - Correction des problèmes de clic dans les iframes
   =================================================================== */

$(document).ready(function() {
    // Corriger les problèmes de z-index et de clic dans les iframes
    function fixIframeClicks() {
        // S'assurer que les iframes sont cliquables
        $('.tab-content iframe').each(function() {
            $(this).css({
                'pointer-events': 'auto',
                'z-index': '1',
                'position': 'relative'
            });
        });
        
        // Corriger les onglets actifs
        $('.tab-pane.active').css({
            'z-index': '2',
            'position': 'relative'
        });
        
        // S'assurer que les modales sont au-dessus
        $('.modal').css('z-index', '9999');
        $('.modal-backdrop').css('z-index', '9998');
    }
    
    // Appliquer les corrections au chargement
    fixIframeClicks();
    
    // Réappliquer lors des changements d'onglets
    $(document).on('shown.bs.tab', function() {
        setTimeout(fixIframeClicks, 100);
    });
    
    // Réappliquer lors de l'ouverture de modales
    $(document).on('shown.bs.modal', function() {
        fixIframeClicks();
    });
    
    // Corriger les problèmes de focus dans les iframes
    $('.tab-content iframe').on('load', function() {
        try {
            // Essayer d'accéder au contenu de l'iframe
            const iframe = this;
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            
            if (iframeDoc) {
                // S'assurer que les éléments sont cliquables
                $(iframeDoc).find('*').css('pointer-events', 'auto');
            }
        } catch (e) {
            // Ignorer les erreurs de cross-origin
            console.log('Cross-origin iframe detected, using fallback');
        }
    });
    
    // Corriger les problèmes de scroll dans les iframes
    $('.tab-content iframe').on('load', function() {
        $(this).css('overflow', 'auto');
    });
    
    // Détecter les clics dans les iframes et les propager
    $('.tab-content iframe').on('click', function(e) {
        // Marquer l'onglet comme actif si nécessaire
        const tabPane = $(this).closest('.tab-pane');
        if (tabPane.length && !tabPane.hasClass('active')) {
            tabPane.addClass('active');
        }
    });
    
    // Corriger les problèmes de redimensionnement
    $(window).on('resize', function() {
        setTimeout(fixIframeClicks, 100);
    });
    
    // Corriger les problèmes de DataTables dans les iframes
    $(document).on('init.dt', function() {
        setTimeout(fixIframeClicks, 100);
    });
    
    // Corriger les problèmes de Select2 dans les iframes
    $(document).on('select2:open', function() {
        setTimeout(fixIframeClicks, 100);
    });
    
    // Corriger les problèmes de SweetAlert dans les iframes
    $(document).on('swal:open', function() {
        setTimeout(fixIframeClicks, 100);
    });
});

/* ===================================================================
   Fonctions utilitaires pour les iframes
   =================================================================== */

// Fonction pour forcer le focus sur un iframe
function focusIframe(iframeId) {
    const iframe = document.getElementById(iframeId);
    if (iframe) {
        iframe.focus();
        iframe.style.pointerEvents = 'auto';
        iframe.style.zIndex = '2';
    }
}

// Fonction pour activer les clics dans un iframe
function enableIframeClicks(iframeId) {
    const iframe = document.getElementById(iframeId);
    if (iframe) {
        iframe.style.pointerEvents = 'auto';
        iframe.style.zIndex = '1';
        iframe.style.position = 'relative';
    }
}

// Fonction pour désactiver temporairement les clics dans un iframe
function disableIframeClicks(iframeId) {
    const iframe = document.getElementById(iframeId);
    if (iframe) {
        iframe.style.pointerEvents = 'none';
    }
}

// Fonction pour réactiver les clics dans un iframe
function reenableIframeClicks(iframeId) {
    const iframe = document.getElementById(iframeId);
    if (iframe) {
        iframe.style.pointerEvents = 'auto';
    }
}

<?php
/**
 * Test de la correction du changement de statut de vente lors de la validation du paiement
 */

session_start();
require_once 'config/database.php';

echo "<h1>🧪 Test de Validation des Paiements et Statuts de Ventes</h1>";

try {
    $database = Database::getInstance();
    $pdo = $database->getConnection();
    
    echo "<h2>✅ Connexion à la base de données réussie</h2>";
    
    // Test 1: Vérifier les ventes avec statut FACTURE
    echo "<h3>📋 Test 1: Ventes facturées disponibles pour paiement</h3>";
    
    $stmt = $pdo->query("SELECT 
                            ve.id, 
                            ve.facture_numero, 
                            ve.statut,
                            ve.total_montant,
                            c.nom as client_nom
                        FROM ventes_entete ve 
                        LEFT JOIN clients c ON ve.client_id = c.id 
                        WHERE ve.statut = 'FACTURE' 
                        ORDER BY ve.id DESC
                        LIMIT 5");
    $ventesFacturees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($ventesFacturees)) {
        echo "<p style='color: orange;'>⚠️ Aucune vente avec statut FACTURE trouvée</p>";
        echo "<p>💡 Pour tester, créez d'abord une vente et changez son statut à FACTURE</p>";
    } else {
        echo "<p style='color: green;'>✅ " . count($ventesFacturees) . " vente(s) facturée(s) trouvée(s)</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID</th><th>Facture</th><th>Client</th><th>Montant</th><th>Statut</th>";
        echo "</tr>";
        
        foreach ($ventesFacturees as $vente) {
            $montant = number_format($vente['total_montant'], 0, ',', ' ') . ' Ar';
            $facture = $vente['facture_numero'] ?: "Vente #{$vente['id']}";
            
            echo "<tr>";
            echo "<td>{$vente['id']}</td>";
            echo "<td>{$facture}</td>";
            echo "<td>{$vente['client_nom']}</td>";
            echo "<td>{$montant}</td>";
            echo "<td style='background-color: #ffc107; color: white; text-align: center;'>{$vente['statut']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test 2: Vérifier les paiements existants et leurs statuts
    echo "<h3>📋 Test 2: Paiements existants et leurs statuts</h3>";
    
    $stmt = $pdo->query("SELECT 
                            oc.id as paiement_id,
                            oc.vente_id,
                            oc.statut as statut_paiement,
                            oc.montant,
                            ve.statut as statut_vente,
                            ve.facture_numero,
                            c.nom as client_nom
                        FROM operation_caisse oc
                        LEFT JOIN ventes_entete ve ON oc.vente_id = ve.id
                        LEFT JOIN clients c ON oc.client_id = c.id
                        WHERE oc.type_operation = 'ENCAISSEMENT_VENTE'
                        ORDER BY oc.id DESC
                        LIMIT 10");
    $paiements = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($paiements)) {
        echo "<p style='color: orange;'>⚠️ Aucun paiement de vente trouvé</p>";
        echo "<p>💡 Créez d'abord un paiement pour tester la validation</p>";
    } else {
        echo "<p style='color: green;'>✅ " . count($paiements) . " paiement(s) trouvé(s)</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'>";
        echo "<th>ID Paiement</th><th>Facture</th><th>Client</th><th>Montant</th><th>Statut Paiement</th><th>Statut Vente</th><th>Cohérence</th>";
        echo "</tr>";
        
        foreach ($paiements as $paiement) {
            $montant = number_format($paiement['montant'], 0, ',', ' ') . ' Ar';
            $facture = $paiement['facture_numero'] ?: "Vente #{$paiement['vente_id']}";
            
            // Vérifier la cohérence
            $coherence = '';
            $couleurCoherence = '';
            
            if ($paiement['statut_paiement'] === 'VALIDE' && $paiement['statut_vente'] === 'PAYE') {
                $coherence = '✅ Cohérent';
                $couleurCoherence = '#28a745'; // Vert
            } elseif ($paiement['statut_paiement'] === 'VALIDE' && $paiement['statut_vente'] !== 'PAYE') {
                $coherence = '❌ Incohérent';
                $couleurCoherence = '#dc3545'; // Rouge
            } elseif ($paiement['statut_paiement'] === 'EN_ATTENTE') {
                $coherence = '⏳ En attente';
                $couleurCoherence = '#ffc107'; // Jaune
            } elseif ($paiement['statut_paiement'] === 'ANNULE') {
                $coherence = '🚫 Annulé';
                $couleurCoherence = '#6c757d'; // Gris
            } else {
                $coherence = '❓ Autre';
                $couleurCoherence = '#17a2b8'; // Bleu
            }
            
            // Couleurs pour les statuts
            $couleurPaiement = '';
            switch ($paiement['statut_paiement']) {
                case 'VALIDE': $couleurPaiement = '#28a745'; break;
                case 'EN_ATTENTE': $couleurPaiement = '#ffc107'; break;
                case 'ANNULE': $couleurPaiement = '#dc3545'; break;
                default: $couleurPaiement = '#6c757d';
            }
            
            $couleurVente = '';
            switch ($paiement['statut_vente']) {
                case 'PAYE': $couleurVente = '#28a745'; break;
                case 'FACTURE': $couleurVente = '#ffc107'; break;
                case 'ANNULE': $couleurVente = '#dc3545'; break;
                default: $couleurVente = '#6c757d';
            }
            
            echo "<tr>";
            echo "<td>{$paiement['paiement_id']}</td>";
            echo "<td>{$facture}</td>";
            echo "<td>{$paiement['client_nom']}</td>";
            echo "<td>{$montant}</td>";
            echo "<td style='background-color: {$couleurPaiement}; color: white; text-align: center;'>{$paiement['statut_paiement']}</td>";
            echo "<td style='background-color: {$couleurVente}; color: white; text-align: center;'>{$paiement['statut_vente']}</td>";
            echo "<td style='background-color: {$couleurCoherence}; color: white; text-align: center;'>{$coherence}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test 3: Vérifier les corrections apportées
    echo "<h3>📋 Test 3: Corrections apportées</h3>";
    
    echo "<div style='background-color: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50;'>";
    echo "<h4>✅ Corrections dans gestion_ventes.js :</h4>";
    echo "<ul>";
    echo "<li>✅ Bouton 'Enregistrer paiement' supprimé</li>";
    echo "<li>✅ Modal de paiement supprimé</li>";
    echo "<li>✅ Fonction showPaymentModal() supprimée</li>";
    echo "<li>✅ Fonction confirmPayment() supprimée</li>";
    echo "<li>✅ Événements de paiement supprimés</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background-color: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin-top: 10px;'>";
    echo "<h4>✅ Corrections dans gestion_paiements_ventes.js :</h4>";
    echo "<ul>";
    echo "<li>✅ Fonction updateVenteStatusFromPaiement() ajoutée</li>";
    echo "<li>✅ Mise à jour automatique du statut de vente quand paiement = VALIDE</li>";
    echo "<li>✅ Messages informatifs lors de la validation</li>";
    echo "<li>✅ Gestion des erreurs améliorée</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test 4: Instructions de test
    echo "<h3>📋 Test 4: Instructions de test</h3>";
    
    echo "<div style='background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107;'>";
    echo "<h4>🧪 Pour tester la correction :</h4>";
    echo "<ol>";
    echo "<li><strong>Créer un paiement :</strong>";
    echo "<ul>";
    echo "<li>Accédez à <a href='pages/gestion_paiements_ventes.php' target='_blank'>gestion_paiements_ventes.php</a></li>";
    echo "<li>Cliquez 'Nouveau Paiement'</li>";
    echo "<li>Sélectionnez une vente FACTURE</li>";
    echo "<li>Remplissez le formulaire avec statut 'EN_ATTENTE'</li>";
    echo "<li>Enregistrez</li>";
    echo "</ul></li>";
    echo "<li><strong>Valider le paiement :</strong>";
    echo "<ul>";
    echo "<li>Dans la liste des paiements, cliquez le bouton 'Changer statut'</li>";
    echo "<li>Sélectionnez 'VALIDE'</li>";
    echo "<li>Ajoutez un commentaire (optionnel)</li>";
    echo "<li>Confirmez</li>";
    echo "</ul></li>";
    echo "<li><strong>Vérifier le résultat :</strong>";
    echo "<ul>";
    echo "<li>Le paiement doit passer en statut VALIDE</li>";
    echo "<li>La vente correspondante doit automatiquement passer en statut PAYE</li>";
    echo "<li>Un message de succès doit confirmer les deux actions</li>";
    echo "</ul></li>";
    echo "</ol>";
    echo "</div>";
    
    // Test 5: Vérification de la suppression dans gestion_ventes
    echo "<h3>📋 Test 5: Vérification de la suppression dans gestion_ventes</h3>";
    
    echo "<div style='background-color: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8;'>";
    echo "<h4>🔍 Vérifiez que dans gestion_ventes.php :</h4>";
    echo "<ul>";
    echo "<li>✅ Plus de bouton 'Enregistrer paiement' pour les ventes FACTURE</li>";
    echo "<li>✅ Plus de modal de paiement</li>";
    echo "<li>✅ Les utilisateurs doivent maintenant utiliser gestion_paiements_ventes.php</li>";
    echo "</ul>";
    echo "<p><strong>Test :</strong> <a href='pages/gestion_ventes.php' target='_blank'>Accédez à gestion_ventes.php</a> et vérifiez qu'il n'y a plus de bouton de paiement</p>";
    echo "</div>";
    
    echo "<h2>🎯 Résumé</h2>";
    echo "<div style='background-color: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50;'>";
    echo "<p><strong>✅ CORRECTIONS APPLIQUÉES AVEC SUCCÈS !</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Suppression complète</strong> de la gestion des paiements dans gestion_ventes</li>";
    echo "<li>✅ <strong>Centralisation</strong> de tous les paiements dans gestion_paiements_ventes</li>";
    echo "<li>✅ <strong>Correction du bug</strong> : validation paiement → vente PAYE automatiquement</li>";
    echo "<li>✅ <strong>Workflow unifié</strong> : un seul endroit pour gérer les paiements</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #2c3e50; }
h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
h3 { color: #7f8c8d; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
ul, ol { margin-left: 20px; }
p { margin: 10px 0; }
</style>

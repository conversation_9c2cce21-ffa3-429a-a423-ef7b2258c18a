/* ===================================================================
   Global Styles - Styles globaux et utilitaires
   =================================================================== */

/* Import du thème principal */
@import url('erp-theme.css');

/* ===================================================================
   RESET ET NORMALISATION
   =================================================================== */

*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    margin: 0;
    padding: 0;
    font-family: var(--font-family);
    background-color: var(--light-bg);
    color: var(--text-color);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===================================================================
   TYPOGRAPHIE
   =================================================================== */

h1, h2, h3, h4, h5, h6 {
    margin: 0 0 var(--spacing-md) 0;
    font-weight: 600;
    line-height: 1.2;
    color: var(--text-color);
}

h1 { font-size: var(--font-size-xxl); }
h2 { font-size: var(--font-size-xl); }
h3 { font-size: var(--font-size-lg); }
h4 { font-size: var(--font-size-base); }
h5 { font-size: var(--font-size-sm); }
h6 { font-size: var(--font-size-xs); }

p {
    margin: 0 0 var(--spacing-md) 0;
    line-height: 1.6;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-base);
}

a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===================================================================
   LISTES
   =================================================================== */

ul, ol {
    margin: 0 0 var(--spacing-md) 0;
    padding-left: var(--spacing-lg);
}

li {
    margin-bottom: var(--spacing-xs);
}

/* ===================================================================
   IMAGES
   =================================================================== */

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.img-responsive {
    width: 100%;
    height: auto;
}

.img-circle {
    border-radius: 50%;
}

.img-rounded {
    border-radius: var(--border-radius);
}

/* ===================================================================
   TABLEAUX
   =================================================================== */

table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
}

th, td {
    text-align: left;
    vertical-align: top;
}

/* ===================================================================
   FORMULAIRES
   =================================================================== */

fieldset {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin: 0 0 var(--spacing-md) 0;
}

legend {
    font-weight: 600;
    color: var(--text-color);
    padding: 0 var(--spacing-sm);
}

/* ===================================================================
   UTILITAIRES DE DISPLAY
   =================================================================== */

.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-table { display: table !important; }
.d-table-row { display: table-row !important; }
.d-table-cell { display: table-cell !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* ===================================================================
   UTILITAIRES DE FLEXBOX
   =================================================================== */

.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-fill { flex: 1 1 auto !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

/* ===================================================================
   UTILITAIRES DE POSITION
   =================================================================== */

.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* ===================================================================
   UTILITAIRES DE TAILLE
   =================================================================== */

.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

.mw-100 { max-width: 100% !important; }
.mh-100 { max-height: 100% !important; }

/* ===================================================================
   UTILITAIRES DE MARGES
   =================================================================== */

.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--spacing-xs) !important; }
.ml-2 { margin-left: var(--spacing-sm) !important; }
.ml-3 { margin-left: var(--spacing-md) !important; }
.ml-4 { margin-left: var(--spacing-lg) !important; }
.ml-5 { margin-left: var(--spacing-xl) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--spacing-xs) !important; }
.mr-2 { margin-right: var(--spacing-sm) !important; }
.mr-3 { margin-right: var(--spacing-md) !important; }
.mr-4 { margin-right: var(--spacing-lg) !important; }
.mr-5 { margin-right: var(--spacing-xl) !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: var(--spacing-xs) !important; margin-right: var(--spacing-xs) !important; }
.mx-2 { margin-left: var(--spacing-sm) !important; margin-right: var(--spacing-sm) !important; }
.mx-3 { margin-left: var(--spacing-md) !important; margin-right: var(--spacing-md) !important; }
.mx-4 { margin-left: var(--spacing-lg) !important; margin-right: var(--spacing-lg) !important; }
.mx-5 { margin-left: var(--spacing-xl) !important; margin-right: var(--spacing-xl) !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--spacing-xs) !important; margin-bottom: var(--spacing-xs) !important; }
.my-2 { margin-top: var(--spacing-sm) !important; margin-bottom: var(--spacing-sm) !important; }
.my-3 { margin-top: var(--spacing-md) !important; margin-bottom: var(--spacing-md) !important; }
.my-4 { margin-top: var(--spacing-lg) !important; margin-bottom: var(--spacing-lg) !important; }
.my-5 { margin-top: var(--spacing-xl) !important; margin-bottom: var(--spacing-xl) !important; }

/* ===================================================================
   UTILITAIRES DE PADDING
   =================================================================== */

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--spacing-xs) !important; }
.pt-2 { padding-top: var(--spacing-sm) !important; }
.pt-3 { padding-top: var(--spacing-md) !important; }
.pt-4 { padding-top: var(--spacing-lg) !important; }
.pt-5 { padding-top: var(--spacing-xl) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--spacing-xs) !important; }
.pb-2 { padding-bottom: var(--spacing-sm) !important; }
.pb-3 { padding-bottom: var(--spacing-md) !important; }
.pb-4 { padding-bottom: var(--spacing-lg) !important; }
.pb-5 { padding-bottom: var(--spacing-xl) !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: var(--spacing-xs) !important; }
.pl-2 { padding-left: var(--spacing-sm) !important; }
.pl-3 { padding-left: var(--spacing-md) !important; }
.pl-4 { padding-left: var(--spacing-lg) !important; }
.pl-5 { padding-left: var(--spacing-xl) !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: var(--spacing-xs) !important; }
.pr-2 { padding-right: var(--spacing-sm) !important; }
.pr-3 { padding-right: var(--spacing-md) !important; }
.pr-4 { padding-right: var(--spacing-lg) !important; }
.pr-5 { padding-right: var(--spacing-xl) !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: var(--spacing-xs) !important; padding-right: var(--spacing-xs) !important; }
.px-2 { padding-left: var(--spacing-sm) !important; padding-right: var(--spacing-sm) !important; }
.px-3 { padding-left: var(--spacing-md) !important; padding-right: var(--spacing-md) !important; }
.px-4 { padding-left: var(--spacing-lg) !important; padding-right: var(--spacing-lg) !important; }
.px-5 { padding-left: var(--spacing-xl) !important; padding-right: var(--spacing-xl) !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--spacing-xs) !important; padding-bottom: var(--spacing-xs) !important; }
.py-2 { padding-top: var(--spacing-sm) !important; padding-bottom: var(--spacing-sm) !important; }
.py-3 { padding-top: var(--spacing-md) !important; padding-bottom: var(--spacing-md) !important; }
.py-4 { padding-top: var(--spacing-lg) !important; padding-bottom: var(--spacing-lg) !important; }
.py-5 { padding-top: var(--spacing-xl) !important; padding-bottom: var(--spacing-xl) !important; }

/* ===================================================================
   UTILITAIRES DE TEXTE
   =================================================================== */

.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

.text-nowrap { white-space: nowrap !important; }
.text-wrap { white-space: normal !important; }

.text-break { word-break: break-word !important; }

.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.text-muted { color: var(--text-muted) !important; }
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }

.font-weight-light { font-weight: 300 !important; }
.font-weight-normal { font-weight: 400 !important; }
.font-weight-bold { font-weight: 700 !important; }

.font-italic { font-style: italic !important; }

/* ===================================================================
   UTILITAIRES DE VISIBILITÉ
   =================================================================== */

.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }

/* ===================================================================
   UTILITAIRES DE OVERFLOW
   =================================================================== */

.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

/* ===================================================================
   UTILITAIRES DE BORDURES
   =================================================================== */

.border { border: 1px solid var(--border-color) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid var(--border-color) !important; }
.border-right { border-right: 1px solid var(--border-color) !important; }
.border-bottom { border-bottom: 1px solid var(--border-color) !important; }
.border-left { border-left: 1px solid var(--border-color) !important; }

.rounded { border-radius: var(--border-radius) !important; }
.rounded-0 { border-radius: 0 !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-circle { border-radius: 50% !important; }

/* ===================================================================
   UTILITAIRES DE OMBRES
   =================================================================== */

.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* ===================================================================
   UTILITAIRES DE COULEURS DE FOND
   =================================================================== */

.bg-transparent { background-color: transparent !important; }
.bg-white { background-color: var(--white) !important; }
.bg-light { background-color: var(--light-bg) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

/* ===================================================================
   UTILITAIRES RESPONSIVE
   =================================================================== */

@media (max-width: 576px) {
    .d-sm-none { display: none !important; }
    .d-sm-inline { display: inline !important; }
    .d-sm-inline-block { display: inline-block !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }
    .d-sm-inline-flex { display: inline-flex !important; }
    .d-sm-grid { display: grid !important; }
}

@media (max-width: 768px) {
    .d-md-none { display: none !important; }
    .d-md-inline { display: inline !important; }
    .d-md-inline-block { display: inline-block !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
    .d-md-inline-flex { display: inline-flex !important; }
    .d-md-grid { display: grid !important; }
}

@media (max-width: 992px) {
    .d-lg-none { display: none !important; }
    .d-lg-inline { display: inline !important; }
    .d-lg-inline-block { display: inline-block !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
    .d-lg-inline-flex { display: inline-flex !important; }
    .d-lg-grid { display: grid !important; }
}

@media (max-width: 1200px) {
    .d-xl-none { display: none !important; }
    .d-xl-inline { display: inline !important; }
    .d-xl-inline-block { display: inline-block !important; }
    .d-xl-block { display: block !important; }
    .d-xl-flex { display: flex !important; }
    .d-xl-inline-flex { display: inline-flex !important; }
    .d-xl-grid { display: grid !important; }
}

/* ===================================================================
   UTILITAIRES D'ACCESSIBILITÉ
   =================================================================== */

.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
}

/* ===================================================================
   UTILITAIRES DE PRINT
   =================================================================== */

@media print {
    .d-print-none { display: none !important; }
    .d-print-inline { display: inline !important; }
    .d-print-inline-block { display: inline-block !important; }
    .d-print-block { display: block !important; }
    .d-print-flex { display: flex !important; }
    .d-print-inline-flex { display: inline-flex !important; }
    .d-print-grid { display: grid !important; }
}

/* ===================================================================
   ANIMATIONS GLOBALES
   =================================================================== */

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.animate-fadeIn { animation: fadeIn var(--transition-slow); }
.animate-fadeOut { animation: fadeOut var(--transition-slow); }
.animate-slideInUp { animation: slideInUp var(--transition-slow); }
.animate-slideInDown { animation: slideInDown var(--transition-slow); }
.animate-slideInLeft { animation: slideInLeft var(--transition-slow); }
.animate-slideInRight { animation: slideInRight var(--transition-slow); }

/* ===================================================================
   UTILITAIRES DE TRANSITION
   =================================================================== */

.transition-none { transition: none !important; }
.transition-all { transition: all var(--transition-base) !important; }
.transition-opacity { transition: opacity var(--transition-base) !important; }
.transition-transform { transition: transform var(--transition-base) !important; }

/* ===================================================================
   UTILITAIRES DE CURSOR
   =================================================================== */

.cursor-pointer { cursor: pointer !important; }
.cursor-default { cursor: default !important; }
.cursor-not-allowed { cursor: not-allowed !important; }
.cursor-move { cursor: move !important; }
.cursor-grab { cursor: grab !important; }
.cursor-grabbing { cursor: grabbing !important; }

/* ===================================================================
   UTILITAIRES DE USER SELECT
   =================================================================== */

.user-select-none { user-select: none !important; }
.user-select-auto { user-select: auto !important; }
.user-select-text { user-select: text !important; }
.user-select-all { user-select: all !important; }

/* ===================================================================
   UTILITAIRES DE POINTER EVENTS
   =================================================================== */

.pointer-events-none { pointer-events: none !important; }
.pointer-events-auto { pointer-events: auto !important; }

/* ===================================================================
   UTILITAIRES DE Z-INDEX
   =================================================================== */

.z-0 { z-index: 0 !important; }
.z-1 { z-index: 1 !important; }
.z-2 { z-index: 2 !important; }
.z-3 { z-index: 3 !important; }
.z-4 { z-index: 4 !important; }
.z-5 { z-index: 5 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }

{"name": "DejaVuSansCondensed-Oblique", "type": "TTF", "desc": {"CapHeight": 729, "XHeight": 547, "FontBBox": "[-914 -350 1493 1068]", "Flags": 68, "Ascent": 928, "Descent": -236, "Leading": 0, "ItalicAngle": -11, "StemV": 87, "MissingWidth": 540}, "unitsPerEm": 2048, "up": -63, "ut": 44, "strp": 259, "strs": 50, "ttffile": "C:\\xampp1\\htdocs\\erp3\\vendor\\mpdf\\mpdf\\src\\Config/../../ttfonts/DejaVuSansCondensed-Oblique.ttf", "TTCfontID": 0, "originalsize": 609268, "sip": false, "smp": false, "BMPselected": true, "fontkey": "dejavusanscondensedI", "panose": " 0 0 2 b 6 6 3 3 4 b 2 4", "haskerninfo": true, "haskernGPOS": true, "hassmallcapsGSUB": false, "fontmetrics": "win", "useOTL": 255, "rtlPUAstr": "\\x{0EF01}-\\x{0EF19}", "GSUBScriptLang": {"DFLT": "DFLT ", "arab": "DFLT KUR  SND  URD  ", "armn": "DFLT ", "brai": "DFLT ", "cans": "DFLT ", "cher": "DFLT ", "cyrl": "DFLT MKD  SRB  ", "geor": "DFLT ", "grek": "DFLT ", "hani": "DFLT ", "hebr": "DFLT ", "kana": "DFLT ", "lao ": "DFLT ", "latn": "DFLT CAT  ESP  GAL  ISM  KSM  LSM  MOL  NSM  ROM  SKS  SSM  ", "math": "DFLT ", "nko ": "DFLT ", "ogam": "DFLT ", "runr": "DFLT ", "tfng": "DFLT ", "thai": "DFLT "}, "GSUBFeatures": {"DFLT": {"DFLT": {"ccmp": [2], "dlig": [11], "case": [27]}}, "arab": {"DFLT": {"ccmp": [0, 2]}, "KUR ": {"ccmp": [2]}, "SND ": {"ccmp": [2]}, "URD ": {"ccmp": [2]}}, "armn": {"DFLT": {"ccmp": [2], "dlig": [8]}}, "brai": {"DFLT": {"ccmp": [2]}}, "cans": {"DFLT": {"ccmp": [2]}}, "cher": {"DFLT": {"ccmp": [2]}}, "cyrl": {"DFLT": {"ccmp": [1, 2]}, "MKD ": {"ccmp": [1, 2], "locl": [4]}, "SRB ": {"ccmp": [1, 2], "locl": [4]}}, "geor": {"DFLT": {"ccmp": [2]}}, "grek": {"DFLT": {"ccmp": [1, 2], "aalt": [14], "salt": [15]}}, "hani": {"DFLT": {"ccmp": [2]}}, "hebr": {"DFLT": {"ccmp": [0, 2, 3], "hlig": [7], "aalt": [12], "salt": [13]}}, "kana": {"DFLT": {"ccmp": [2]}}, "lao ": {"DFLT": {"ccmp": [2]}}, "latn": {"DFLT": {"ccmp": [1, 2], "liga": [6], "dlig": [9], "hlig": [10], "salt": [16], "aalt": [17], "case": [27]}, "CAT ": {"case": [27]}, "ESP ": {"case": [27]}, "GAL ": {"case": [27]}, "ISM ": {"ccmp": [1, 2], "locl": [5], "liga": [6], "dlig": [9], "hlig": [10], "salt": [16], "aalt": [17]}, "KSM ": {"ccmp": [1, 2], "locl": [5], "liga": [6], "dlig": [9], "hlig": [10], "salt": [16], "aalt": [17]}, "LSM ": {"ccmp": [1, 2], "locl": [5], "liga": [6], "dlig": [9], "hlig": [10], "salt": [16], "aalt": [17]}, "MOL ": {"ccmp": [1, 2], "liga": [6], "dlig": [9], "hlig": [10], "salt": [16], "aalt": [17]}, "NSM ": {"ccmp": [1, 2], "locl": [5], "liga": [6], "dlig": [9], "hlig": [10], "salt": [16], "aalt": [17]}, "ROM ": {"ccmp": [1, 2], "liga": [6], "dlig": [9], "hlig": [10], "salt": [16], "aalt": [17]}, "SKS ": {"ccmp": [1, 2], "locl": [5], "liga": [6], "dlig": [9], "hlig": [10], "salt": [16], "aalt": [17]}, "SSM ": {"ccmp": [1, 2], "locl": [5], "liga": [6], "dlig": [9], "hlig": [10], "salt": [16], "aalt": [17]}}, "math": {"DFLT": {"ccmp": [2]}}, "nko ": {"DFLT": {"ccmp": [0, 2]}}, "ogam": {"DFLT": {"ccmp": [2]}}, "runr": {"DFLT": {"ccmp": [2]}}, "tfng": {"DFLT": {"ccmp": [2]}}, "thai": {"DFLT": {"ccmp": [2]}}}, "GSUBLookups": [{"Type": 6, "Flag": 1, "SubtableCount": 1, "Subtables": [582998], "MarkFilteringSet": ""}, {"Type": 6, "Flag": 0, "SubtableCount": 1, "Subtables": [583082], "MarkFilteringSet": ""}, {"Type": 6, "Flag": 0, "SubtableCount": 10, "Subtables": [583978, 584066, 584154, 584242, 584330, 584418, 584484, 584556, 584628, 584700], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 1, "SubtableCount": 1, "Subtables": [584766], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [584804], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [584816], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [584828], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 1, "SubtableCount": 1, "Subtables": [584888], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [584912], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [584988], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [585012], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [585036], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 1, "SubtableCount": 1, "Subtables": [585192], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 1, "SubtableCount": 1, "Subtables": [585204], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585216], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585254], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585292], "MarkFilteringSet": ""}, {"Type": 3, "Flag": 0, "SubtableCount": 1, "Subtables": [585322], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 1, "SubtableCount": 1, "Subtables": [585380], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585392], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585450], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585512], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585528], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585544], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585560], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585576], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585592], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [585618], "MarkFilteringSet": ""}], "GPOSScriptLang": {"DFLT": "DFLT ", "arab": "DFLT KUR  SND  URD  ", "armn": "DFLT ", "brai": "DFLT ", "cans": "DFLT ", "cher": "DFLT ", "cyrl": "DFLT MKD  SRB  ", "geor": "DFLT ", "grek": "DFLT ", "hani": "DFLT ", "hebr": "DFLT ", "kana": "DFLT ", "lao ": "DFLT ", "latn": "DFLT ISM  KSM  LSM  MOL  NSM  ROM  SKS  SSM  ", "math": "DFLT ", "nko ": "DFLT ", "ogam": "DFLT ", "runr": "DFLT ", "tfng": "DFLT ", "thai": "DFLT "}, "GPOSFeatures": {"DFLT": {"DFLT": {"kern": [11]}}, "arab": {"DFLT": {"mark": [3, 4, 5], "kern": [11]}, "KUR ": {"kern": [11]}, "SND ": {"kern": [11]}, "URD ": {"kern": [11]}}, "armn": {"DFLT": {"kern": [11]}}, "brai": {"DFLT": {"kern": [11]}}, "cans": {"DFLT": {"kern": [11]}}, "cher": {"DFLT": {"kern": [11]}}, "cyrl": {"DFLT": {"mkmk": [2], "mark": [8, 9], "kern": [11]}, "MKD ": {"mkmk": [2], "mark": [8, 9], "kern": [11]}, "SRB ": {"mkmk": [2], "mark": [8, 9], "kern": [11]}}, "geor": {"DFLT": {"kern": [11]}}, "grek": {"DFLT": {"mkmk": [2], "mark": [8, 9], "kern": [11]}}, "hani": {"DFLT": {"kern": [11]}}, "hebr": {"DFLT": {"mark": [3, 4, 5], "kern": [11]}}, "kana": {"DFLT": {"kern": [11]}}, "lao ": {"DFLT": {"mkmk": [0, 1], "mark": [6, 7], "kern": [11]}}, "latn": {"DFLT": {"mkmk": [2], "mark": [8, 9], "kern": [10, 11]}, "ISM ": {"mkmk": [2], "mark": [8, 9], "kern": [10, 11]}, "KSM ": {"mkmk": [2], "mark": [8, 9], "kern": [10, 11]}, "LSM ": {"mkmk": [2], "mark": [8, 9], "kern": [10, 11]}, "MOL ": {"mkmk": [2], "mark": [8, 9], "kern": [10, 11]}, "NSM ": {"mkmk": [2], "mark": [8, 9], "kern": [10, 11]}, "ROM ": {"mkmk": [2], "mark": [8, 9], "kern": [10, 11]}, "SKS ": {"mkmk": [2], "mark": [8, 9], "kern": [10, 11]}, "SSM ": {"mkmk": [2], "mark": [8, 9], "kern": [10, 11]}}, "math": {"DFLT": {"kern": [11]}}, "nko ": {"DFLT": {"mark": [3, 4, 5], "kern": [11]}}, "ogam": {"DFLT": {"kern": [11]}}, "runr": {"DFLT": {"kern": [11]}}, "tfng": {"DFLT": {"kern": [11]}}, "thai": {"DFLT": {"kern": [11]}}}, "GPOSLookups": [{"Type": 6, "Flag": 0, "SubtableCount": 1, "Subtables": [561840], "MarkFilteringSet": ""}, {"Type": 6, "Flag": 0, "SubtableCount": 1, "Subtables": [561990], "MarkFilteringSet": ""}, {"Type": 6, "Flag": 0, "SubtableCount": 2, "Subtables": [562048, 562486], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 1, "SubtableCount": 1, "Subtables": [563692], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 1, "SubtableCount": 1, "Subtables": [564106], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 1, "SubtableCount": 1, "Subtables": [564368], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [564446], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [564878], "MarkFilteringSet": ""}, {"Type": 5, "Flag": 0, "SubtableCount": 1, "Subtables": [565180], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 6, "Subtables": [565622, 565998, 567450, 567578, 568086, 572234], "MarkFilteringSet": ""}, {"Type": 2, "Flag": 0, "SubtableCount": 1, "Subtables": [578474], "MarkFilteringSet": ""}, {"Type": 2, "Flag": 0, "SubtableCount": 1, "Subtables": [581776], "MarkFilteringSet": ""}], "kerninfo": {"45": {"84": -72, "86": -40, "87": -17, "88": -35, "89": -72, "221": -72, "356": -72, "376": -72}, "65": {"44": 18, "46": 18, "58": 18, "59": 18, "84": -72, "86": -49, "87": -17, "354": -72, "356": -72, "372": -17, "538": -72, "7808": -17, "7810": -17, "7812": -17, "8217": -132, "8221": -132}, "66": {"45": -17, "89": -17, "221": -17, "374": -17, "376": -17, "562": -17, "7922": -17}, "67": {"83": 18, "346": 18, "348": 18, "350": 18, "352": 18, "536": 18, "8217": -21, "8221": -21}, "70": {"44": -128, "45": -54, "46": -128, "58": -35, "59": -35, "65": -58, "117": -40, "192": -58, "193": -58, "194": -58, "195": -58, "196": -58, "249": -40, "250": -40, "251": -40, "252": -40, "256": -58, "258": -58, "260": -58, "361": -40, "363": -40, "365": -40, "367": -40, "369": -40, "371": -40}, "75": {"45": -100, "67": -26, "79": -26, "85": -17, "97": -35, "101": -35, "111": -35, "117": -26, "121": -26, "199": -26, "210": -26, "211": -26, "212": -26, "213": -26, "214": -26, "216": -26, "217": -17, "218": -17, "219": -17, "220": -17, "224": -35, "225": -35, "226": -35, "227": -35, "228": -35, "229": -35, "230": -17, "232": -35, "233": -35, "234": -35, "235": -35, "242": -35, "243": -35, "244": -35, "245": -35, "246": -35, "248": -44, "249": -26, "250": -26, "251": -26, "252": -26, "253": -26, "255": -26, "262": -26, "268": -26, "283": -35, "338": -26, "339": -44, "366": -17, "367": -26}, "76": {"45": -77, "79": -30, "84": -67, "86": -72, "87": -40, "89": -95, "121": -35, "210": -30, "211": -30, "212": -30, "213": -30, "214": -30, "216": -30, "221": -95, "253": -35, "255": -35, "338": -30, "356": -67, "376": -95, "8216": -91, "8217": -179, "8220": -91, "8221": -179}, "79": {"44": -40, "46": -40, "58": 18, "59": 18, "88": -26}, "80": {"44": -137, "45": -54, "46": -137, "58": -17, "59": -17, "65": -49, "192": -49, "193": -49, "194": -49, "195": -49, "196": -49}, "81": {"45": 18}, "83": {"83": -35, "350": -35, "352": -35}, "84": {"44": -118, "45": -142, "46": -118, "58": -91, "59": -91, "65": -91, "97": -104, "99": -123, "101": -123, "111": -123, "114": -77, "115": -118, "117": -100, "119": -81, "121": -86, "192": -91, "193": -91, "194": -91, "195": -91, "196": -91, "224": -104, "225": -104, "226": -104, "227": -104, "228": -104, "229": -104, "230": -123, "231": -123, "232": -123, "233": -123, "234": -123, "235": -123, "242": -123, "243": -123, "244": -123, "245": -123, "246": -123, "248": -123, "249": -100, "250": -100, "251": -100, "252": -100, "253": -86, "255": -86, "263": -123, "269": -123, "283": -123, "339": -123, "341": -77, "345": -77, "351": -118, "353": -118, "367": -100}, "86": {"44": -114, "45": -86, "46": -114, "58": -26, "59": -26, "65": -67, "97": -63, "101": -58, "111": -58, "117": -17, "192": -67, "193": -67, "194": -67, "195": -67, "196": -67, "224": -63, "225": -63, "226": -63, "227": -63, "228": -63, "229": -63, "230": -72, "232": -58, "233": -58, "234": -58, "235": -58, "242": -58, "243": -58, "244": -58, "245": -58, "246": -58, "248": -58, "249": -17, "250": -17, "251": -17, "252": -17, "283": -58, "339": -58, "367": -17}, "87": {"44": -81, "45": -77, "46": -81, "65": -49, "97": -54, "114": -17, "192": -49, "193": -49, "194": -49, "195": -49, "196": -49, "224": -54, "225": -54, "226": -54, "227": -54, "228": -54, "229": -54, "230": -54, "341": -17, "345": -17, "8217": -17, "8221": -17}, "88": {"45": -91, "67": -17, "79": -35, "199": -17, "210": -35, "211": -35, "212": -35, "213": -35, "214": -35, "216": -17, "262": -17, "268": -17, "338": -26}, "89": {"44": -128, "45": -137, "46": -128, "58": -86, "59": -86, "65": -77, "67": -17, "79": -17, "97": -91, "101": -109, "105": -17, "111": -109, "117": -91, "192": -77, "193": -77, "194": -77, "195": -77, "196": -77, "199": -17, "210": -17, "211": -17, "212": -17, "213": -17, "214": -17, "216": -17, "224": -91, "225": -91, "226": -91, "227": -91, "228": -91, "229": -91, "230": -91, "232": -109, "233": -109, "234": -109, "235": -109, "242": -109, "243": -109, "244": -109, "245": -109, "246": -109, "248": -109, "249": -91, "250": -91, "251": -91, "252": -91, "262": -17, "268": -17, "283": -109, "338": -17, "339": -109, "367": -91, "8216": -21, "8220": -21}, "90": {"45": -49}, "102": {"44": -17, "45": -17, "46": -17, "8216": 68, "8217": 45, "8220": 68, "8221": 45}, "114": {"44": -91, "45": -54, "46": -91}, "118": {"44": -81, "46": -81}, "119": {"44": -63, "46": -63}, "121": {"44": -77, "45": -35, "46": -77}, "192": {"44": 18, "46": 18, "58": 18, "59": 18, "84": -72, "86": -49, "87": -17, "354": -72, "356": -72, "372": -17, "538": -72, "7808": -17, "7810": -17, "7812": -17, "8217": -132, "8221": -132}, "193": {"44": 18, "46": 18, "58": 18, "59": 18, "84": -72, "86": -49, "87": -17, "354": -72, "356": -72, "372": -17, "538": -72, "7808": -17, "7810": -17, "7812": -17, "8217": -132, "8221": -132}, "194": {"44": 18, "46": 18, "58": 18, "59": 18, "84": -72, "86": -49, "87": -17, "354": -17, "356": -72, "372": -17, "538": -17, "7808": -17, "7810": -17, "7812": -17, "8217": -132, "8221": -132}, "195": {"44": 18, "46": 18, "58": 18, "59": 18, "84": -72, "86": -49, "87": -17, "354": -72, "356": -72, "372": -17, "538": -72, "7808": -17, "7810": -17, "7812": -17, "8217": -132, "8221": -132}, "196": {"44": 18, "46": 18, "58": 18, "59": 18, "84": -72, "86": -49, "87": -17, "354": -72, "356": -72, "372": -17, "538": -72, "7808": -17, "7810": -17, "7812": -17, "8217": -132, "8221": -132}, "198": {"45": -17}, "199": {"83": 18, "346": 18, "348": 18, "350": 18, "352": 18, "536": 18, "8217": -21, "8221": -21}, "208": {"45": 18}, "210": {"44": -40, "46": -40, "58": 18, "59": 18, "88": -26}, "211": {"44": -40, "46": -40, "58": 18, "59": 18, "88": -26}, "212": {"44": -40, "46": -40, "58": 18, "59": 18, "88": -26}, "213": {"44": -40, "46": -40, "58": 18, "59": 18, "88": -26}, "214": {"44": -40, "46": -40, "58": 18, "59": 18, "88": -26}, "216": {"44": -40, "45": 18, "46": -40, "58": 18, "59": 18, "88": -17}, "221": {"44": -128, "45": -137, "46": -128, "58": -86, "59": -86, "65": -77, "67": -17, "79": -17, "97": -91, "101": -109, "105": -17, "111": -109, "117": -91, "192": -77, "193": -77, "194": -77, "195": -77, "196": -77, "199": -17, "210": -17, "211": -17, "212": -17, "213": -17, "214": -17, "216": -17, "224": -91, "225": -91, "226": -91, "227": -91, "228": -91, "229": -91, "230": -91, "232": -109, "233": -109, "234": -109, "235": -109, "242": -109, "243": -109, "244": -109, "245": -109, "246": -109, "248": -109, "249": -91, "250": -91, "251": -91, "252": -91, "262": -17, "268": -17, "283": -109, "338": -17, "339": -109, "367": -91, "8216": -21, "8220": -21}, "222": {"44": -72, "45": 36, "46": -72}, "253": {"44": -77, "45": -35, "46": -77}, "255": {"44": -77, "45": -35, "46": -77}, "256": {"44": 18, "46": 18, "58": 18, "59": 18, "84": -72, "86": -49, "87": -17, "354": -72, "356": -72, "372": -17, "538": -72, "7808": -17, "7810": -17, "7812": -17, "8217": -132, "8221": -132}, "258": {"44": 18, "46": 18, "58": 18, "59": 18, "84": -72, "86": -49, "87": -17, "354": -72, "356": -72, "372": -17, "538": -72, "7808": -17, "7810": -17, "7812": -17, "8217": -132, "8221": -132}, "260": {"44": 18, "46": 18, "58": 18, "59": 18, "84": -72, "86": -49, "87": -17, "354": -72, "356": -72, "372": -17, "538": -72, "7808": -17, "7810": -17, "7812": -17, "8217": -132, "8221": -132}, "262": {"83": 18, "346": 18, "348": 18, "350": 18, "352": 18, "536": 18, "8217": -21, "8221": -21}, "264": {"83": 18, "346": 18, "348": 18, "350": 18, "352": 18, "536": 18, "8217": -21, "8221": -21}, "266": {"83": 18, "346": 18, "348": 18, "350": 18, "352": 18, "536": 18, "8217": -21, "8221": -21}, "268": {"83": 18, "346": 18, "348": 18, "350": 18, "352": 18, "536": 18, "8217": -21, "8221": -21}, "313": {"45": -77, "79": -30, "84": -67, "86": -72, "87": -40, "89": -95, "121": -35, "210": -30, "211": -30, "212": -30, "213": -30, "214": -30, "216": -30, "221": -95, "253": -35, "255": -35, "338": -30, "356": -67, "376": -95, "8216": -91, "8217": -179, "8220": -91, "8221": -179}, "317": {"45": -77, "79": -30, "84": -67, "86": -72, "87": -40, "89": -95, "121": -35, "210": -30, "211": -30, "212": -30, "213": -30, "214": -30, "216": -30, "221": -95, "253": -35, "255": -35, "338": -30, "356": -67, "376": -95, "8216": -91, "8217": -179, "8220": -91, "8221": -179}, "321": {"45": -77, "79": -30, "84": -67, "86": -72, "87": -40, "89": -95, "121": -35, "210": -30, "211": -30, "212": -30, "213": -30, "214": -30, "216": -30, "221": -95, "253": -35, "255": -35, "338": -30, "356": -67, "376": -95, "8216": -91, "8217": -179, "8220": -91, "8221": -179}, "341": {"44": -91, "45": -54, "46": -91}, "345": {"44": -91, "45": -54, "46": -91}, "350": {"83": -35, "350": -35, "352": -35}, "352": {"83": -35, "350": -35, "352": -35}, "356": {"44": -118, "45": -142, "46": -118, "58": -91, "59": -91, "65": -91, "97": -104, "99": -123, "101": -123, "111": -123, "114": -77, "115": -118, "117": -100, "119": -81, "121": -86, "192": -91, "193": -91, "194": -91, "195": -91, "196": -91, "224": -104, "225": -104, "226": -104, "227": -104, "228": -104, "229": -104, "230": -123, "231": -123, "232": -123, "233": -123, "234": -123, "235": -123, "242": -123, "243": -123, "244": -123, "245": -123, "246": -123, "248": -123, "249": -100, "250": -100, "251": -100, "252": -100, "253": -86, "255": -86, "263": -123, "269": -123, "283": -123, "339": -123, "341": -77, "345": -77, "351": -118, "353": -118, "367": -100}, "376": {"44": -128, "45": -137, "46": -128, "58": -86, "59": -86, "65": -77, "67": -17, "79": -17, "97": -91, "101": -109, "105": -17, "111": -109, "117": -91, "192": -77, "193": -77, "194": -77, "195": -77, "196": -77, "199": -17, "210": -17, "211": -17, "212": -17, "213": -17, "214": -17, "216": -17, "224": -91, "225": -91, "226": -91, "227": -91, "228": -91, "229": -91, "230": -91, "232": -109, "233": -109, "234": -109, "235": -109, "242": -109, "243": -109, "244": -109, "245": -109, "246": -109, "248": -109, "249": -91, "250": -91, "251": -91, "252": -91, "262": -17, "268": -17, "283": -109, "338": -17, "339": -109, "367": -91, "8216": -21, "8220": -21}, "381": {"45": -49}, "699": {"65": -118, "86": 36, "89": 22, "192": -118, "193": -118, "194": -118, "195": -118, "196": -118, "198": -146, "221": 22, "376": 22}, "8208": {"84": -72, "86": -40, "87": -17, "88": -35, "89": -72, "221": -72, "356": -72, "376": -72}, "8216": {"65": -118, "86": 36, "89": 22, "192": -118, "193": -118, "194": -118, "195": -118, "196": -118, "198": -146, "221": 22, "376": 22}, "8218": {"67": -35, "79": -35, "81": -35, "84": -109, "86": -128, "87": -72, "89": -165, "102": -35, "118": -54, "119": -35, "121": -44, "199": -35, "210": -35, "211": -35, "212": -35, "213": -35, "214": -35, "216": -26, "221": -165, "253": -44, "255": -44, "262": -35, "268": -35, "338": -35, "356": -109, "376": -165, "64257": -35, "64258": -35}, "8220": {"65": -118, "86": 36, "89": 22, "192": -118, "193": -118, "194": -118, "195": -118, "196": -118, "198": -146, "221": 22, "376": 22}, "8222": {"67": -35, "79": -35, "81": -35, "84": -109, "86": -128, "87": -72, "89": -165, "102": -35, "118": -72, "119": -35, "121": -54, "199": -35, "210": -35, "211": -35, "212": -35, "213": -35, "214": -35, "216": -26, "221": -165, "253": -54, "255": -54, "262": -35, "268": -35, "338": -35, "356": -109, "376": -165, "64257": -35, "64258": -35}, "42788": {"44": -118, "45": -142, "46": -118, "58": -91, "59": -91, "65": -91, "97": -104, "99": -123, "101": -123, "111": -123, "114": -77, "115": -118, "117": -100, "119": -81, "121": -86, "192": -91, "193": -91, "194": -91, "195": -91, "196": -91, "224": -104, "225": -104, "226": -104, "227": -104, "228": -104, "229": -104, "230": -123, "231": -123, "232": -123, "233": -123, "234": -123, "235": -123, "242": -123, "243": -123, "244": -123, "245": -123, "246": -123, "248": -123, "249": -100, "250": -100, "251": -100, "252": -100, "253": -86, "255": -86, "263": -123, "269": -123, "283": -123, "339": -123, "341": -77, "345": -77, "351": -118, "353": -118, "367": -100}, "42816": {"45": -100, "67": -26, "79": -26, "85": -17, "97": -35, "101": -35, "111": -35, "117": -26, "121": -26, "199": -26, "210": -26, "211": -26, "212": -26, "213": -26, "214": -26, "216": -26, "217": -17, "218": -17, "219": -17, "220": -17, "224": -35, "225": -35, "226": -35, "227": -35, "228": -35, "229": -35, "230": -17, "232": -35, "233": -35, "234": -35, "235": -35, "242": -35, "243": -35, "244": -35, "245": -35, "246": -35, "248": -44, "249": -26, "250": -26, "251": -26, "252": -26, "253": -26, "255": -26, "262": -26, "268": -26, "283": -35, "338": -26, "339": -44, "366": -17, "367": -26}, "61185": {"61209": -19}, "61186": {"61209": -38}, "61187": {"61209": -45}, "61188": {"61209": -48}, "61189": {"61209": -19}, "61191": {"61209": -19}, "61192": {"61209": -38}, "61193": {"61209": -45}, "61194": {"61209": -38}, "61195": {"61209": -19}, "61197": {"61209": -19}, "61198": {"61209": -38}, "61199": {"61209": -45}, "61200": {"61209": -38}, "61201": {"61209": -19}, "61203": {"61209": -19}, "61204": {"61209": -48}, "61205": {"61209": -45}, "61206": {"61209": -38}, "61207": {"61209": -19}}}
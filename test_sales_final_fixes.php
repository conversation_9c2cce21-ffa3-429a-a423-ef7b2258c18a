<?php
/**
 * Test des corrections finales du système de ventes
 * 1. Validation des prix non obligatoire
 * 2. Dépôts avec libelle au lieu de nom
 * 3. Grade et Qualité en select
 * 4. Correction SHOW COLUMNS dans paiements
 */

require_once 'config/config.php';
require_once CONFIG_PATH . '/database.php';

// Connexion à la base de données
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<h2>✅ Connexion à la base de données réussie</h2>";
} catch (PDOException $e) {
    die("<h2>❌ Erreur de connexion : " . $e->getMessage() . "</h2>");
}

echo "<h1>Test des Corrections Finales du Système de Ventes</h1>";

// Test 1: Vérification des dépôts
echo "<h3>Test 1: Vérification des dépôts (libelle vs nom)</h3>";

try {
    $stmt = $pdo->query("SELECT id, libelle FROM depot WHERE est_actif = 1");
    $depots = $stmt->fetchAll();
    
    if (count($depots) > 0) {
        echo "✅ Dépôts trouvés avec colonne 'libelle':<br>";
        foreach ($depots as $depot) {
            echo "- ID: {$depot['id']}, Libellé: {$depot['libelle']}<br>";
        }
    } else {
        echo "⚠️ Aucun dépôt actif trouvé<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur lors du test des dépôts : " . $e->getMessage() . "<br>";
}

// Test 2: Test de la requête information_schema pour operation_caisse
echo "<h3>Test 2: Vérification des colonnes operation_caisse</h3>";

try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM information_schema.columns 
                          WHERE table_schema = DATABASE() 
                          AND table_name = 'operation_caisse' 
                          AND column_name IN ('vente_id', 'client_id')");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result['count'] >= 2) {
        echo "✅ Les colonnes vente_id et client_id existent dans operation_caisse<br>";
        
        // Vérifier la structure complète
        $stmt = $pdo->query("DESCRIBE operation_caisse");
        $columns = $stmt->fetchAll();
        
        echo "Colonnes disponibles dans operation_caisse:<br>";
        foreach ($columns as $column) {
            echo "- {$column['Field']} ({$column['Type']})<br>";
        }
        
    } else {
        echo "❌ Colonnes manquantes dans operation_caisse (trouvées: {$result['count']}/2)<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ Erreur lors du test information_schema : " . $e->getMessage() . "<br>";
}

// Test 3: Test de création d'une vente sans prix
echo "<h3>Test 3: Création d'une vente sans prix (validation assouplie)</h3>";

try {
    // Récupérer un client et un produit pour le test
    $stmt = $pdo->query("SELECT id FROM clients LIMIT 1");
    $client = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT id FROM produits LIMIT 1");
    $produit = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT id FROM depot WHERE est_actif = 1 LIMIT 1");
    $depot = $stmt->fetch();
    
    if ($client && $produit && $depot) {
        echo "✅ Données de test disponibles:<br>";
        echo "- Client ID: {$client['id']}<br>";
        echo "- Produit ID: {$produit['id']}<br>";
        echo "- Dépôt ID: {$depot['id']}<br>";
        
        // Simuler une création de vente sans prix
        $_POST = [
            'action' => 'create',
            'table' => 'ventes_entete',
            'data' => json_encode([
                'client_id' => $client['id'],
                'n_domiciliation' => 'TEST-NOPRIX-' . date('YmdHis'),
                'total_montant' => 0.00, // Pas de prix
                'total_remise' => 0.00,
                'date_vente' => date('Y-m-d'),
                'statut' => 'EN COURS',
                'facture_numero' => 'TEST-NOPRIX-' . date('YmdHis'),
                'facture_date' => date('Y-m-d'),
                'cree_par' => 'test_system'
            ])
        ];
        
        $_SESSION['user'] = ['role' => 'admin'];
        $_SESSION['role'] = 'admin';
        
        ob_start();
        include 'includes/traitement.php';
        $output = ob_get_clean();
        
        $response = json_decode($output, true);
        
        if ($response && $response['success']) {
            echo "✅ Création de vente sans prix réussie - ID: " . ($response['id'] ?? 'N/A') . "<br>";
            
            $testVenteId = $response['id'] ?? null;
            if ($testVenteId) {
                // Créer un détail sans prix
                $_POST = [
                    'action' => 'create',
                    'table' => 'ventes_details',
                    'data' => json_encode([
                        'vente_id' => $testVenteId,
                        'produit_id' => $produit['id'],
                        'depot_id' => $depot['id'],
                        'grade' => 'Standard',
                        'qualite' => 'Bio',
                        'qte_tonnes' => 1.500,
                        'qte_dernier_stock' => 0,
                        'nbre_lot' => 2,
                        'cree_par' => 'test_system'
                    ])
                ];
                
                ob_start();
                include 'includes/traitement.php';
                $output = ob_get_clean();
                
                $response = json_decode($output, true);
                
                if ($response && $response['success']) {
                    echo "✅ Création de détail sans prix réussie<br>";
                } else {
                    echo "❌ Échec création détail: " . ($response['message'] ?? 'Erreur inconnue') . "<br>";
                }
                
                // Nettoyer - supprimer la vente de test
                $_POST = [
                    'action' => 'delete',
                    'table' => 'ventes_entete',
                    'id' => $testVenteId
                ];
                
                ob_start();
                include 'includes/traitement.php';
                $output = ob_get_clean();
                
                echo "🧹 Vente de test supprimée<br>";
            }
        } else {
            echo "❌ Échec création vente: " . ($response['message'] ?? 'Erreur inconnue') . "<br>";
        }
        
    } else {
        echo "⚠️ Données de test manquantes (client, produit ou dépôt)<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur lors du test de création : " . $e->getMessage() . "<br>";
}

// Test 4: Test des valeurs de Grade et Qualité
echo "<h3>Test 4: Validation des valeurs Grade et Qualité</h3>";

$grades = ['Standard', 'Supérieur', 'Premium', 'Vanille'];
$qualites = ['Bio', 'Conventionnel'];

echo "✅ Grades disponibles: " . implode(', ', $grades) . "<br>";
echo "✅ Qualités disponibles: " . implode(', ', $qualites) . "<br>";

// Résumé
echo "<h3>Résumé des Tests</h3>";

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h5>✅ Corrections appliquées:</h5>";
echo "<ul>";
echo "<li><strong>Validation prix:</strong> Prix non obligatoire pour création de vente</li>";
echo "<li><strong>Dépôts:</strong> Utilisation de 'libelle' au lieu de 'nom'</li>";
echo "<li><strong>Grade/Qualité:</strong> Convertis en listes déroulantes</li>";
echo "<li><strong>Paiements:</strong> Requête information_schema au lieu de SHOW COLUMNS</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h5>🚀 Pages à tester:</h5>";
echo "<p><a href='pages/gestion_ventes.php' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>📊 Gestion des Ventes</a></p>";
echo "<p><a href='pages/gestion_paiements_ventes.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>💳 Gestion des Paiements de Ventes</a></p>";
echo "</div>";

echo "<p><small>Test effectué le " . date('Y-m-d H:i:s') . "</small></p>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Corrections Finales Système Ventes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h3 { color: #0056b3; margin-top: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <!-- Le contenu PHP est affiché ci-dessus -->
</body>
</html>

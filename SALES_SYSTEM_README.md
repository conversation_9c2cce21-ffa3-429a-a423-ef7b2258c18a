# 🚀 Système de Gestion des Ventes - ERP Cacao

## 📋 Vue d'ensemble

Ce système de gestion des ventes a été créé pour compléter le système ERP Cacao existant. Il réplique exactement le workflow des achats mais l'adapte pour les opérations de vente avec une interface utilisateur améliorée.

## 🎯 Fonctionnalités Principales

### ✨ Workflow des Ventes (5 étapes)
1. **SAISIE** - Création initiale de la vente avec produits, quantités, prix
2. **CONFIRME** - Validation de la vente
3. **EXPEDIE** - Marquage comme expédiée
4. **FACTURE** - Génération de la facture
5. **PAYE** - Enregistrement du paiement avec intégration caisse

### 🎨 Interface Utilisateur Améliorée
- **Design moderne** avec Bootstrap Cerulean theme
- **Cartes interactives** avec effets hover et animations
- **Indicateurs de statut** avec badges colorés
- **Modal plein écran** pour la saisie des ventes
- **Tableaux responsifs** avec DataTables
- **Filtres avancés** par statut, date, client
- **Statistiques en temps réel** (6 métriques)

### 💰 Gestion des Devises
- **Conversion automatique** EUR/AR avec cours personnalisable
- **Calculs en temps réel** des montants
- **Support multi-devises** pour l'export

### 📊 Intégration Caisse
- **Enregistrement des paiements** dans `operation_caisse`
- **Modes de paiement** : Espèces, Chèque, Virement
- **Suivi des références** (numéros de chèque, virements)
- **Statuts de paiement** : En attente, Validé, Annulé

## 📁 Structure des Fichiers

```
├── pages/
│   └── gestion_ventes.php          # Page principale (508 lignes)
├── assets/js/
│   └── gestion_ventes.js           # JavaScript complet (1321 lignes)
├── sql/
│   └── extend_operation_caisse_for_sales.sql  # Extension DB
└── test_sales_setup.php            # Script de test et configuration
```

## 🗄️ Structure de Base de Données

### Tables Principales

#### `ventes_entete`
- **ID** : Identifiant unique
- **client_id** : Référence vers la table clients
- **facture_numero** : Numéro de facture/référence
- **date_vente** : Date de la vente
- **facture_date** : Date de facturation
- **total_montant** : Montant total en Ariary
- **valeur_euro** : Valeur en Euros
- **cours_devise** : Cours de change utilisé
- **statut** : SAISIE, CONFIRME, EXPEDIE, FACTURE, PAYE, ANNULE
- **lieu_exportation** : Lieu d'exportation

#### `ventes_details`
- **vente_id** : Référence vers ventes_entete
- **produit_id** : Référence vers produits
- **grade** : Grade du produit (Grade 1, Grade 2, etc.)
- **qualite** : Qualité (Premium, Standard, etc.)
- **quantite_tonnes** : Quantité en tonnes
- **prix_unitaire** : Prix unitaire
- **montant_ligne** : Montant de la ligne
- **date_expedition** : Date d'expédition prévue

#### `operation_caisse` (Étendue)
- **vente_id** : Référence vers ventes_entete (NOUVEAU)
- **client_id** : Référence vers clients (NOUVEAU)
- **type_operation** : ENCAISSEMENT_VENTE, AVANCE_CLIENT, etc. (ÉTENDU)
- **mode_paiement** : ESPECE, CHEQUE, VIREMENT
- **montant** : Montant du paiement
- **reference_paiement** : Numéro chèque/virement
- **statut** : EN_ATTENTE, VALIDE, ANNULE

## 🚀 Installation et Configuration

### 1. Prérequis
- Système ERP Cacao existant fonctionnel
- Tables `clients`, `produits`, `depot`, `unites` présentes
- PHP 7.4+ avec PDO MySQL
- Serveur web (Apache/Nginx)

### 2. Installation

#### Étape 1 : Copier les fichiers
```bash
# Copier les fichiers dans le répertoire ERP
cp pages/gestion_ventes.php /path/to/erp/pages/
cp assets/js/gestion_ventes.js /path/to/erp/assets/js/
cp sql/extend_operation_caisse_for_sales.sql /path/to/erp/sql/
```

#### Étape 2 : Étendre la base de données
```sql
-- Exécuter le script d'extension
mysql -u username -p database_name < sql/extend_operation_caisse_for_sales.sql
```

#### Étape 3 : Configurer les permissions RBAC
```php
// Ajouter dans votre système RBAC
$rbac->addPermission('ventes.view', 'Voir les ventes');
$rbac->addPermission('ventes.create', 'Créer des ventes');
$rbac->addPermission('ventes.edit', 'Modifier les ventes');
$rbac->addPermission('ventes.delete', 'Supprimer les ventes');
```

#### Étape 4 : Test de configuration
```bash
# Accéder au script de test
http://votre-domaine/erp/test_sales_setup.php
```

### 3. Vérification
- ✅ Toutes les tables sont créées
- ✅ Les données de test sont présentes
- ✅ L'interface est accessible
- ✅ Les permissions sont configurées

## 🎮 Guide d'Utilisation

### Créer une Nouvelle Vente
1. Cliquer sur **"Nouvelle Vente"**
2. Sélectionner le **client**
3. Remplir les **informations générales**
4. Ajouter des **produits** avec quantités et prix
5. Vérifier les **totaux** et conversion devise
6. **Enregistrer** la vente

### Workflow de Traitement
1. **SAISIE** → Cliquer "Confirmer" → **CONFIRME**
2. **CONFIRME** → Cliquer "Expédier" → **EXPEDIE**
3. **EXPEDIE** → Cliquer "Facturer" → **FACTURE**
4. **FACTURE** → Cliquer "Paiement" → **PAYE**

### Enregistrer un Paiement
1. Cliquer sur l'icône **💳** pour une vente facturée
2. Choisir le **mode de paiement**
3. Saisir le **montant** et la **référence**
4. **Confirmer** le paiement

## 📊 Fonctionnalités Avancées

### Filtres et Recherche
- **Filtrage par statut** : Tous, Saisie, Confirmé, etc.
- **Filtrage par date** : Période personnalisable
- **Filtrage par client** : Sélection multiple
- **Recherche en temps réel** dans le tableau

### Statistiques Dashboard
- **Total Ventes** : Nombre total de ventes
- **Montant Total** : Chiffre d'affaires total
- **Clients** : Nombre de clients uniques
- **En Attente** : Ventes en cours de traitement
- **À Facturer** : Ventes expédiées non facturées
- **Payés** : Ventes entièrement payées

### Actions en Lot
- **Sélection multiple** avec checkbox
- **Annulation en lot** des ventes sélectionnées
- **Export** (fonctionnalité future)

## 🔧 Personnalisation

### Modifier les Statuts
```javascript
// Dans gestion_ventes.js, fonction getStatutBadge()
const badges = {
    'NOUVEAU_STATUT': '<span class="badge bg-info">Nouveau</span>'
};
```

### Ajouter des Champs
```sql
-- Ajouter une colonne à ventes_entete
ALTER TABLE ventes_entete ADD COLUMN nouveau_champ VARCHAR(255);
```

### Personnaliser l'Interface
```css
/* Dans le <style> de gestion_ventes.php */
.sales-card {
    border-left: 4px solid #votre-couleur;
}
```

## 🐛 Dépannage

### Problèmes Courants

#### 1. Tables manquantes
```bash
# Vérifier l'existence des tables
SHOW TABLES LIKE 'ventes_%';
```

#### 2. Erreurs de permissions
```php
// Vérifier les permissions RBAC
$rbac->hasPermission('ventes.view');
```

#### 3. Problèmes JavaScript
```javascript
// Ouvrir la console développeur (F12)
// Vérifier les erreurs dans l'onglet Console
```

#### 4. Problèmes de base de données
```sql
-- Vérifier la structure
DESCRIBE ventes_entete;
DESCRIBE operation_caisse;
```

## 📈 Améliorations Futures

### Phase 2 - Fonctionnalités Avancées
- [ ] **Génération PDF** des factures
- [ ] **Envoi par email** automatique
- [ ] **Intégration comptable** avancée
- [ ] **Rapports détaillés** avec graphiques
- [ ] **API REST** pour intégrations externes

### Phase 3 - Optimisations
- [ ] **Cache Redis** pour les performances
- [ ] **Notifications temps réel** avec WebSockets
- [ ] **Interface mobile** responsive
- [ ] **Synchronisation offline** avec PWA

## 🤝 Support

### Contacts
- **Développeur** : Augment Agent
- **Documentation** : Ce fichier README
- **Tests** : `test_sales_setup.php`

### Ressources
- **Code source** : Entièrement documenté
- **Base de données** : Scripts SQL fournis
- **Interface** : Bootstrap + DataTables + Select2

---

**🎉 Le système de gestion des ventes est maintenant prêt à être utilisé !**

*Dernière mise à jour : 2025-01-06*

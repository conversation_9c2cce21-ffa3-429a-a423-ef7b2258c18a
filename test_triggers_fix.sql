-- ===================================================================
-- SCRIPT DE TEST POUR VÉRIFIER LES TRIGGERS CORRIGÉS
-- ===================================================================

-- Supprimer les triggers existants s'ils existent
DROP TRIGGER IF EXISTS `tr_achat_detail_insert`;
DROP TRIGGER IF EXISTS `tr_achat_detail_update`;
DROP TRIGGER IF EXISTS `tr_achat_detail_delete`;

-- Créer les triggers corrigés
DELIMITER $$

-- Trigger pour INSERT dans achat_detail
CREATE TRIGGER `tr_achat_detail_insert` 
AFTER INSERT ON `achat_detail`
FOR EACH ROW
BEGIN
    -- Dé<PERSON>larer toutes les variables au début
    DECLARE unite_stock_id INT;
    DECLARE ref_achat VARCHAR(100);
    
    -- Récupérer l'unité de stock du produit
    SELECT unite_stock_id INTO unite_stock_id FROM produits WHERE id = NEW.produit_id;
    
    -- Récupérer la référence de l'achat
    SELECT reference_achat INTO ref_achat FROM achat_entete WHERE id = NEW.achat_entete_id;
    
    -- Insérer le mouvement d'entrée
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        reference_document, lot_numero, motif, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'ENTREE', NEW.qte_nette_controlee,
        unite_stock_id, ref_achat, NEW.lot_numero, 
        CONCAT('Validation achat ', ref_achat, ' - ', NEW.qte_nette_controlee, ' kg'), 
        NEW.date_creation, NEW.cree_par
    );
END$$

-- Trigger pour UPDATE dans achat_detail
CREATE TRIGGER `tr_achat_detail_update` 
AFTER UPDATE ON `achat_detail`
FOR EACH ROW
BEGIN
    -- Déclarer toutes les variables au début
    DECLARE unite_stock_id INT;
    DECLARE ref_achat VARCHAR(100);
    
    -- Supprimer l'ancien mouvement
    DELETE FROM mouvements_stock 
    WHERE reference_document = (SELECT reference_achat FROM achat_entete WHERE id = NEW.achat_entete_id)
    AND produit_id = NEW.produit_id AND depot_id = NEW.depot_id;
    
    -- Récupérer l'unité de stock du produit
    SELECT unite_stock_id INTO unite_stock_id FROM produits WHERE id = NEW.produit_id;
    
    -- Récupérer la référence de l'achat
    SELECT reference_achat INTO ref_achat FROM achat_entete WHERE id = NEW.achat_entete_id;
    
    -- Insérer le nouveau mouvement d'entrée
    INSERT INTO mouvements_stock (
        produit_id, depot_id, type_mouvement, quantite, unite_id,
        reference_document, lot_numero, motif, date_mouvement, cree_par
    ) VALUES (
        NEW.produit_id, NEW.depot_id, 'ENTREE', NEW.qte_nette_controlee,
        unite_stock_id, ref_achat, NEW.lot_numero, 
        CONCAT('Validation achat ', ref_achat, ' - ', NEW.qte_nette_controlee, ' kg'), 
        NEW.date_creation, NEW.cree_par
    );
END$$

-- Trigger pour DELETE dans achat_detail
CREATE TRIGGER `tr_achat_detail_delete` 
AFTER DELETE ON `achat_detail`
FOR EACH ROW
BEGIN
    -- Supprimer le mouvement correspondant
    DELETE FROM mouvements_stock 
    WHERE reference_document = (SELECT reference_achat FROM achat_entete WHERE id = OLD.achat_entete_id)
    AND produit_id = OLD.produit_id AND depot_id = OLD.depot_id;
END$$

DELIMITER ;

-- Vérifier que les triggers ont été créés
SELECT 
    TRIGGER_NAME,
    EVENT_MANIPULATION,
    EVENT_OBJECT_TABLE,
    ACTION_TIMING
FROM INFORMATION_SCHEMA.TRIGGERS 
WHERE TRIGGER_SCHEMA = 'erp_cacao' 
AND TRIGGER_NAME LIKE 'tr_achat_detail_%'
ORDER BY TRIGGER_NAME;

-- Test d'insertion pour vérifier le fonctionnement
-- (Ceci ne sera exécuté que si les tables existent et contiennent des données)
/*
INSERT INTO achat_entete (reference_achat, fournisseur_id, date_achat, statut, cree_par) 
VALUES ('ACH-TEST-001', 1, CURDATE(), 'SAISIE', 'test');

INSERT INTO achat_detail (achat_entete_id, produit_id, depot_id, unite_achat_id, qte_brute_saisie, qte_nette_controlee, lot_numero, cree_par) 
VALUES (LAST_INSERT_ID(), 5, 1, 3, 100.00, 95.00, 'LOT-TEST-001', 'test');

-- Vérifier que le mouvement a été créé
SELECT * FROM mouvements_stock WHERE reference_document = 'ACH-TEST-001';
*/

-- ===== CORRECTION DE LA VUE PAIEMENTS DÉTAILLÉS =====
-- Correction de la vue en utilisant les colonnes existantes de la table producteurs

-- Supprimer la vue existante si elle existe
DROP VIEW IF EXISTS `v_paiements_detaille`;

-- Créer la vue corrigée
CREATE VIEW `v_paiements_detaille` AS
SELECT 
    oc.id,
    oc.achat_id,
    ae.reference_achat,
    p.nom as fournisseur_nom,
    p.contact as fournisseur_contact,
    p.site as fournisseur_site,
    p.classement as fournisseur_classement,
    oc.type_operation,
    oc.mode_paiement,
    oc.reference_paiement,
    oc.montant,
    oc.date_paiement,
    oc.effectue_par,
    oc.commentaires,
    oc.date_creation,
    oc.cree_par
FROM operation_caisse oc
LEFT JOIN achat_entete ae ON oc.achat_id = ae.id
LEFT JOIN producteurs p ON ae.fournisseur_id = p.id
WHERE oc.type_operation = 'PAIEMENT_ACHAT'
ORDER BY oc.date_paiement DESC;

-- ===== VÉRIFICATION DE LA VUE =====
-- Tester la vue pour s'assurer qu'elle fonctionne

SELECT * FROM v_paiements_detaille LIMIT 5;

-- ===== COMMENTAIRES =====
-- Cette vue corrigée utilise les colonnes disponibles dans la table producteurs :
-- - p.nom : Nom du fournisseur
-- - p.contact : Contact du fournisseur (au lieu de telephone)
-- - p.site : Site du fournisseur (au lieu de adresse)
-- - p.classement : Classement du fournisseur (LEAD FARMER, PETIT PLANTEUR, etc.)

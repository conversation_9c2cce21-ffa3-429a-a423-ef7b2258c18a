// ===== GESTION DES ACHATS =====

let achatsTable;
let currentAchatId = null;
let achatDetails = [];

$(document).ready(function() {
    // Initialisation
    initDataTables();
    loadAchats();
    loadFournisseurs();
    loadProduits();
    loadDepots();
    loadUnites();
    loadPlanteurs();
    
    // Event handlers
    setupEventHandlers();
    
    // Générer la référence d'achat
    generateReferenceAchat();
    
    // Initialiser les dates automatiquement
    setDefaultDates();
    
    // Initialiser Select2
    initSelect2();
});

// ===== INITIALISATION =====

function initDataTables() {
    if ($('#tableAchats').length && !$.fn.DataTable.isDataTable('#tableAchats')) {
        achatsTable = $('#tableAchats').DataTable({
            pageLength: 20,
            lengthMenu: [30, 100, 200],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            columnDefs: [
                { orderable: false, targets: [0, 7] }, // Colonnes checkbox et actions
                { className: "text-center", targets: [0, 5] }, // Statut centré
                { className: "text-end", targets: [6] } // Montant aligné à droite
            ]
        });
    }
}

// ===== CHARGEMENT DES DONNÉES =====

function loadAchats() {
    // Vérifier d'abord si les tables existent
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT COUNT(*) as table_exists 
              FROM information_schema.tables 
              WHERE table_schema = DATABASE() 
              AND table_name IN ('achat_entete', 'achat_detail')`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success && response.data.length > 0 && response.data[0].table_exists == 2) {
            // Les tables existent, charger les achats
            loadAchatsData();
        } else {
            // Les tables n'existent pas, afficher un message
            showAlert('Configuration requise', 
                'Les tables d\'achat n\'existent pas encore. Veuillez exécuter le script de configuration.', 
                'warning');
            displayAchats([]); // Afficher un tableau vide
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors de la vérification des tables:', error);
        displayAchats([]); // Afficher un tableau vide en cas d'erreur
    });
}

function loadAchatsData() {
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                ae.id,
                ae.reference_achat,
                p.nom as fournisseur_nom,
                ae.date_achat,
                ae.date_livraison,
                ae.statut,
                ae.frais_transport,
                COALESCE(SUM(ad.montant_ht), 0) as montant_total
              FROM achat_entete ae
              LEFT JOIN producteurs p ON ae.fournisseur_id = p.id
              LEFT JOIN achat_detail ad ON ae.id = ad.achat_entete_id
              GROUP BY ae.id
              ORDER BY ae.date_creation DESC`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            displayAchats(response.data);
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement des achats:', error);
        showAlert('Erreur', 'Impossible de charger les achats', 'error');
    });
}

function displayAchats(achats) {
    if (achatsTable) {
        achatsTable.clear();
        achats.forEach(achat => {
            const statutBadge = getStatutBadge(achat.statut);
            const montantFormatted = parseFloat(achat.montant_total).toLocaleString() + ' Ar';
            
            // Générer les boutons selon le statut
            let actionButtons = generateActionButtons(achat);
            
            achatsTable.row.add([
                `<input type="checkbox" value="${achat.id}">`,
                achat.reference_achat,
                achat.fournisseur_nom || 'N/A',
                achat.date_achat || 'N/A',
                achat.date_livraison || 'N/A',
                statutBadge,
                montantFormatted,
                actionButtons
            ]);
        });
        achatsTable.draw();
        
        // Mettre à jour les statistiques
        updateStatistics(achats);
    }
}

function displayAchatsFiltered(achats) {
    if (achatsTable) {
        achatsTable.clear();
        achats.forEach(achat => {
            const statutBadge = getStatutBadge(achat.statut);
            const montantFormatted = parseFloat(achat.montant_total).toLocaleString() + ' Ar';
            
            // Générer les boutons selon le statut
            let actionButtons = generateActionButtons(achat);
            
            achatsTable.row.add([
                `<input type="checkbox" value="${achat.id}">`,
                achat.reference_achat,
                achat.fournisseur_nom || 'N/A',
                achat.date_achat || 'N/A',
                achat.date_livraison || 'N/A',
                statutBadge,
                montantFormatted,
                actionButtons
            ]);
        });
        achatsTable.draw();
        
        // Ne pas mettre à jour les statistiques lors du filtrage
        // Les statistiques doivent toujours refléter toutes les données
    }
}

function updateStatistics(achats) {
    // Calculer les statistiques sur toutes les données, pas seulement celles filtrées
    loadAllStatistics();
}

function loadAllStatistics() {
    // Charger toutes les données pour les statistiques (sans filtres)
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'achat_entete',
        sql: `SELECT 
                ae.id,
                ae.statut,
                ae.fournisseur_id,
                COALESCE(SUM(ad.montant_ht), 0) as montant_total
              FROM achat_entete ae
              LEFT JOIN achat_detail ad ON ae.id = ad.achat_entete_id
              GROUP BY ae.id, ae.statut, ae.fournisseur_id
              ORDER BY ae.date_creation DESC`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            const allAchats = response.data;
            
            const totalAchats = allAchats.length;
            const montantTotal = allAchats.reduce((sum, achat) => sum + parseFloat(achat.montant_total || 0), 0);
            const fournisseurs = new Set(allAchats.map(achat => achat.fournisseur_id)).size;
            const enAttente = allAchats.filter(achat => achat.statut === 'SAISIE' || achat.statut === 'LIVRE').length;
            const aPayer = allAchats.filter(achat => achat.statut === 'A_PAYER').length;
            const payes = allAchats.filter(achat => achat.statut === 'PAYE').length;
            
            $('#statTotalAchats').text(totalAchats);
            $('#statMontantTotal').text(montantTotal.toLocaleString() + ' Ar');
            $('#statFournisseurs').text(fournisseurs);
            $('#statEnAttente').text(enAttente);
            $('#statAPayer').text(aPayer);
            $('#statPayes').text(payes);
        }
    }).fail(function() {
        console.error('Erreur lors du chargement des statistiques');
    });
}

function generateActionButtons(achat) {
    let buttons = '';
    
    // Bouton Voir (toujours disponible)
    buttons += `<button class="btn btn-sm btn-outline-info btn-view-achat" data-id="${achat.id}" title="Voir détails">
        <i class="fas fa-eye"></i>
    </button> `;
    
    // Bouton Modifier (seulement si SAISIE ou LIVRE)
    if (achat.statut === 'SAISIE' || achat.statut === 'LIVRE') {
        buttons += `<button class="btn btn-sm btn-outline-warning btn-edit-achat" data-id="${achat.id}" title="Modifier">
            <i class="fas fa-edit"></i>
        </button> `;
    }
    
    // Bouton Valider (seulement si SAISIE ou LIVRE)
    if (achat.statut === 'SAISIE' || achat.statut === 'LIVRE') {
        buttons += `<button class="btn btn-sm btn-outline-success btn-validate-achat" data-id="${achat.id}" title="Valider">
            <i class="fas fa-check"></i>
        </button> `;
    }
    
    // Bouton Passer au paiement (seulement si CONTROLE)
    if (achat.statut === 'CONTROLE') {
        buttons += `<button class="btn btn-sm btn-outline-primary btn-payment-achat" data-id="${achat.id}" title="Passer au paiement">
            <i class="fas fa-credit-card"></i>
        </button> `;
    }
    
    // Bouton Saisie petit planteur (si statut différent de SAISIE et CONTROLE)
    if (achat.statut !== 'SAISIE' && achat.statut !== 'CONTROLE') {
        buttons += `<button class="btn btn-sm btn-outline-info btn-planteurs-achat" data-id="${achat.id}" title="Saisie petit planteur">
            <i class="fas fa-users"></i>
        </button> `;
    }
    
    // Bouton Annuler (seulement si pas PAYE et pas ANNULE)
    if (achat.statut !== 'PAYE' && achat.statut !== 'ANNULE') {
        buttons += `<button class="btn btn-sm btn-outline-danger btn-cancel-achat" data-id="${achat.id}" title="Annuler">
            <i class="fas fa-times"></i>
        </button> `;
    }
    
    // Bouton Supprimer (seulement si SAISIE)
    if (achat.statut === 'SAISIE') {
        buttons += `<button class="btn btn-sm btn-outline-danger btn-delete-achat" data-id="${achat.id}" title="Supprimer">
            <i class="fas fa-trash"></i>
        </button> `;
    }
    
    // Bouton Envoyer par email (toujours disponible)
    buttons += `<button class="btn btn-sm btn-outline-info btn-email-achat" data-id="${achat.id}" title="Envoyer par email">
        <i class="fas fa-envelope"></i>
    </button> `;
    
    // Bouton Imprimer (toujours disponible)
    buttons += `<button class="btn btn-sm btn-outline-secondary btn-print-achat" data-id="${achat.id}" title="Imprimer">
        <i class="fas fa-print"></i>
    </button>`;
    
    return buttons;
}

function getStatutBadge(statut) {
    const badges = {
        'SAISIE': '<span class="badge bg-secondary status-badge">Saisie</span>',
        'LIVRE': '<span class="badge bg-info status-badge">Livré</span>',
        'CONTROLE': '<span class="badge bg-warning status-badge">Contrôlé</span>',
        'A_PAYER': '<span class="badge bg-danger status-badge">À payer</span>',
        'PAYE': '<span class="badge bg-success status-badge">Payé</span>',
        'ANNULE': '<span class="badge bg-dark status-badge">Annulé</span>'
    };
    return badges[statut] || '<span class="badge bg-secondary status-badge">Inconnu</span>';
}

// ===== CHARGEMENT DES DONNÉES DE RÉFÉRENCE =====

function loadFournisseurs() {
    // Charger seulement les producteurs avec classement LEAD FARMER ou FREELANCE (différent de PETIT PLANTEUR)
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'producteurs',
        sql: 'SELECT * FROM producteurs WHERE classement IN ("LEAD FARMER", "FREELANCE") ORDER BY nom ASC',
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            const select = $('#fournisseurAchat');
            const filterSelect = $('#filterFournisseur');
            
            select.empty().append('<option value="">Sélectionner un fournisseur</option>');
            filterSelect.empty().append('<option value="">Tous les fournisseurs</option>');
            
            // Ajouter les fournisseurs (LEAD FARMER et FREELANCE seulement)
            response.data.forEach(fournisseur => {
                select.append(`<option value="${fournisseur.id}">${fournisseur.nom} (${fournisseur.classement})</option>`);
                filterSelect.append(`<option value="${fournisseur.id}">${fournisseur.nom} (${fournisseur.classement})</option>`);
            });
        }
    });
}

function loadProduits() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'produits'
    }).done(function(response) {
        if (response.success) {
            window.produitsCache = {};
            response.data.forEach(produit => {
                window.produitsCache[produit.id] = produit;
            });
        }
    });
}

function loadDepots() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'depot'
    }).done(function(response) {
        if (response.success) {
            window.depotsCache = {};
            response.data.forEach(depot => {
                window.depotsCache[depot.id] = depot;
            });
        }
    });
}

function loadUnites() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'unites'
    }).done(function(response) {
        if (response.success) {
            window.unitesCache = {};
            response.data.forEach(unite => {
                window.unitesCache[unite.id] = unite;
            });
        }
    });
}

// ===== GESTION DES ÉVÉNEMENTS =====

function setupEventHandlers() {
    // Boutons d'action
    $(document).on('click', '.btn-view-achat', function() {
        const id = $(this).data('id');
        viewAchat(id);
    });
    
    $(document).on('click', '.btn-edit-achat', function() {
        const id = $(this).data('id');
        editAchat(id);
    });
    
    $(document).on('click', '.btn-suivi-achat', function() {
        const id = $(this).data('id');
        showSuiviAchat(id);
    });
    
    $(document).on('click', '.btn-delete-achat', function() {
        const id = $(this).data('id');
        deleteAchat(id);
    });
    
    // Nouveaux boutons d'action
    $(document).on('click', '.btn-validate-achat', function() {
        const id = $(this).data('id');
        validateAchat(id);
    });
    
    $(document).on('click', '.btn-cancel-achat', function() {
        const id = $(this).data('id');
        cancelAchat(id);
    });
    
    $(document).on('click', '.btn-payment-achat', function() {
        const id = $(this).data('id');
        setPaymentStatus(id);
    });
    
    $(document).on('click', '.btn-email-achat', function() {
        const id = $(this).data('id');
        sendEmailAchat(id);
    });
    
    $(document).on('click', '.btn-print-achat', function() {
        const id = $(this).data('id');
        printAchat(id);
    });
    
    $(document).on('click', '.btn-planteurs-achat', function() {
        const id = $(this).data('id');
        showPlanteursModal(id);
    });
    
    // Bouton d'annulation multiple (gardé pour compatibilité)
    $('#btnCancelAchats').on('click', function() {
        const selectedIds = getSelectedAchatIds();
        if (selectedIds.length === 0) {
            showAlert('Erreur', 'Veuillez sélectionner au moins un achat', 'warning');
            return;
        }
        cancelAchats(selectedIds);
    });
    
    // Filtres
    $('#btnApplyFilters').on('click', function() {
        applyFilters();
    });
    
    $('#btnResetFilters').on('click', function() {
        resetFilters();
    });
    
    // Application automatique des filtres lors du changement
    $('#filterStatus, #filterDateFrom, #filterDateTo, #filterFournisseur').on('change', function() {
        applyFilters();
    });
    
    // Sauvegarde d'achat
    $('#btnSaveAchat').on('click', function() {
        saveAchat();
    });
    
    // Bouton Nouvel Achat
    $('[data-bs-target="#modalAchat"]').click(function() {
        currentAchatId = null;
        resetAchatForm();
        // Mettre à jour le titre du modal et le bouton
        $('#modalAchatTitle').html('<i class="fas fa-shopping-cart"></i> Nouvel Achat');
        $('#btnSaveText').text('Enregistrer l\'Achat');
    });
    
    // Ajout de produit
    $('#btnAddProduitAchat').on('click', function() {
        addProduitAchat();
    });
    
    // Suppression de produit
    $(document).on('click', '.btn-remove-produit', function() {
        const row = $(this).closest('tr');
        const produitSelect = row.find('.produit-select');
        const produitNom = produitSelect.find('option:selected').text() || 'ce produit';
        
        Swal.fire({
            title: 'Supprimer le produit',
            text: `Êtes-vous sûr de vouloir supprimer ${produitNom} de cet achat ?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler',
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6'
        }).then((result) => {
            if (result.isConfirmed) {
                row.remove();
                updateTotals();
                showAlert('Succès', 'Produit supprimé de l\'achat', 'success');
            }
        });
    });
    
    // Changement de quantité/prix
    $(document).on('input', '.poids-net, .poids-brut, .nbr-sacs, .reduction', function() {
        updateRowCalculations($(this).closest('tr'));
        updateTotals();
    });
    
    // Calcul automatique du poids net quand le poids brut change
    $(document).on('input', '.poids-brut', function() {
        const row = $(this).closest('tr');
        const poidsBrut = parseFloat($(this).val()) || 0;
        
        if (poidsBrut > 0) {
            // Calculer automatiquement le poids net (3 kg fraîche = 1 kg sec)
            const poidsNetCalc = calculatePoidsNetFromBrut(poidsBrut);
            row.find('.poids-net').val(poidsNetCalc.toFixed(2));
            
            // Recalculer l'écart
            const ecart = poidsBrut - poidsNetCalc;
            row.find('.ecart').val(ecart.toFixed(2));
            
            // Calculer automatiquement le nombre de sacs si le poids brut est saisi manuellement
            const nbrSacsCalc = poidsBrut / 67; // 1 sac = 67 kg
            row.find('.nbr-sacs').val(nbrSacsCalc.toFixed(2));
            
            updateAchatDetailsCache();
        }
    });
    
    // Calcul automatique du poids brut quand le poids net change
    $(document).on('input', '.poids-net', function() {
        const row = $(this).closest('tr');
        const poidsNet = parseFloat($(this).val()) || 0;
        
        if (poidsNet > 0) {
            // Calculer le poids brut à partir du poids net (1 kg sec = 3 kg fraîche)
            const poidsBrutCalc = poidsNet * 3;
            row.find('.poids-brut').val(poidsBrutCalc.toFixed(2));
            
            // Recalculer l'écart
            const ecart = poidsBrutCalc - poidsNet;
            row.find('.ecart').val(ecart.toFixed(2));
            
            // Calculer le nombre de sacs
            const nbrSacsCalc = poidsBrutCalc / 67; // 1 sac = 67 kg
            row.find('.nbr-sacs').val(nbrSacsCalc.toFixed(2));
            
            updateAchatDetailsCache();
        }
    });
    
    // Calcul automatique du poids brut quand le nombre de sacs change
    $(document).on('input', '.nbr-sacs', function() {
        const row = $(this).closest('tr');
        const nbrSacs = parseFloat($(this).val()) || 0;
        
        if (nbrSacs > 0) {
            const poidsBrutCalc = nbrSacs * 67; // 1 sac = 67 kg
            row.find('.poids-brut').val(poidsBrutCalc);
            
            // Calculer automatiquement le poids net
            const poidsNetCalc = calculatePoidsNetFromBrut(poidsBrutCalc);
            row.find('.poids-net').val(poidsNetCalc.toFixed(2));
            
            // Calculer l'écart
            const ecart = poidsBrutCalc - poidsNetCalc;
            row.find('.ecart').val(ecart.toFixed(2));
            
            updateAchatDetailsCache();
        }
    });
    
    // Changement de produit
    $(document).on('change', '.produit-select', function() {
        const row = $(this).closest('tr');
        const produitId = $(this).val();
        if (produitId && window.produitsCache[produitId]) {
            const produit = window.produitsCache[produitId];
            
            // Calculer automatiquement le nombre de sacs à partir de la quantité de présentation
            if (produit.qte_presentation && produit.qte_presentation > 0) {
                row.find('.nbr-sacs').val(produit.qte_presentation);
                
                // Calculer le poids brut à partir du nombre de sacs (1 sac = 67 kg)
                const poidsBrutCalc = produit.qte_presentation * 67;
                row.find('.poids-brut').val(poidsBrutCalc);
                
                // Calculer automatiquement le poids net (3 kg fraîche = 1 kg sec)
                const poidsNetCalc = calculatePoidsNetFromBrut(poidsBrutCalc);
                row.find('.poids-net').val(poidsNetCalc.toFixed(2));
                
                // Calculer l'écart
                const ecart = poidsBrutCalc - poidsNetCalc;
                row.find('.ecart').val(ecart.toFixed(2));
                
                updateAchatDetailsCache();
            }
            
            // Le prix sera saisi à l'étape de paiement
            updateRowCalculations(row);
        }
    });
    
    // Changement de mode de paiement
    $('#modePaiement').on('change', function() {
        const mode = $(this).val();
        const referenceGroup = $('#referencePaiementGroup');
        const referenceLabel = $('#referencePaiementLabel');
        
        if (mode === 'CHEQUE') {
            referenceGroup.show();
            referenceLabel.text('Numéro de chèque');
            $('#referencePaiement').attr('placeholder', 'Numéro de chèque');
        } else if (mode === 'VIREMENT') {
            referenceGroup.show();
            referenceLabel.text('Référence virement');
            $('#referencePaiement').attr('placeholder', 'Référence virement');
        } else {
            referenceGroup.hide();
        }
    });
}

// ===== FONCTIONS CRUD =====

function viewAchat(id) {
    // Charger les données de l'achat avec les détails
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'achat_entete',
        sql: `SELECT 
                ae.*,
                p.nom as fournisseur_nom
              FROM achat_entete ae
              LEFT JOIN producteurs p ON ae.fournisseur_id = p.id
              WHERE ae.id = ?`,
        params: JSON.stringify([id])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const achat = response.data[0];
            
            // Charger les détails de l'achat
            $.post('../includes/traitement.php', {
                action: 'execute_sql',
                table: 'achat_detail',
                sql: `SELECT 
                        ad.*,
                        p.nom as produit_nom,
                        u.libelle as unite_libelle
                      FROM achat_detail ad
                      LEFT JOIN produits p ON ad.produit_id = p.id
                      LEFT JOIN unites u ON ad.unite_achat_id = u.id
                      WHERE ad.achat_entete_id = ?
                      ORDER BY ad.id`,
                params: JSON.stringify([id])
            }).done(function(detailsResponse) {
                if (detailsResponse.success) {
                    displayAchatDetails(achat, detailsResponse.data);
                } else {
                    showAlert('Erreur', 'Impossible de charger les détails', 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Impossible de charger les détails', 'error');
            });
        } else {
            showAlert('Erreur', 'Achat non trouvé', 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de charger l\'achat', 'error');
    });
}

function displayAchatDetails(achat, details) {
    const statutBadge = getStatutBadge(achat.statut);
    const montantTotal = details.reduce((sum, detail) => sum + parseFloat(detail.montant_ht || 0), 0);
    
    // Charger les noms des producteurs d'abord
    loadProducteursNames().then(producteursMap => {
        let detailsHtml = `
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Informations Générales</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr><td><strong>Référence:</strong></td><td>${achat.reference_achat}</td></tr>
                                <tr><td><strong>Fournisseur:</strong></td><td>${achat.fournisseur_nom || 'N/A'}</td></tr>
                                <tr><td><strong>Date d'achat:</strong></td><td>${achat.date_achat || 'N/A'}</td></tr>
                                <tr><td><strong>Date de livraison:</strong></td><td>${achat.date_livraison || 'N/A'}</td></tr>
                                <tr><td><strong>Statut:</strong></td><td>${statutBadge}</td></tr>
                                <tr><td><strong>Frais de transport:</strong></td><td>${parseFloat(achat.frais_transport || 0).toLocaleString()} Ar</td></tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Résumé</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <tr><td><strong>Nombre de produits:</strong></td><td>${details.length}</td></tr>
                                <tr><td><strong>Montant total:</strong></td><td>${montantTotal.toLocaleString()} Ar</td></tr>
                                <tr><td><strong>Créé le:</strong></td><td>${achat.date_creation}</td></tr>
                                <tr><td><strong>Créé par:</strong></td><td>${achat.cree_par || 'N/A'}</td></tr>
                                <tr><td><strong>Dernière modification:</strong></td><td>${achat.date_derniere_modif}</td></tr>
                                <tr><td><strong>Modifié par:</strong></td><td>${achat.dernier_modif_par || 'N/A'}</td></tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Détails des Produits</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>Produit</th>
                                            <th>N° Lot</th>
                                            <th>Poids Net</th>
                                            <th>Poids Brut</th>
                                            <th>Nbr. Sacs</th>
                                            <th>Écart</th>
                                            <th>Prix Unitaire</th>
                                            <th>Réduction</th>
                                            <th>Montant HT</th>
                                            <th>Petits Planteurs</th>
                                        </tr>
                                    </thead>
                                    <tbody>
        `;
    
    details.forEach(detail => {
        // Vérifier si des producteurs sont associés
        const hasProducteurs = detail.producteurs_json && detail.producteurs_json.trim() !== '';
        const rowClass = hasProducteurs ? '' : 'table-warning';
        
        // Générer le HTML des producteurs
        let producteursHtml = '<span class="text-muted">Aucun</span>';
        if (hasProducteurs) {
            try {
                const planteursData = JSON.parse(detail.producteurs_json);
                producteursHtml = '<ul class="list-unstyled mb-0">';
                planteursData.forEach(planteurData => {
                    const producteurNom = producteursMap[planteurData.producteur_id] || `ID: ${planteurData.producteur_id}`;
                    producteursHtml += `<li><small>• ${producteurNom} (${planteurData.qte} kg)</small></li>`;
                });
                producteursHtml += '</ul>';
            } catch (e) {
                producteursHtml = '<span class="text-danger">Erreur format</span>';
            }
        }
        
        detailsHtml += `
            <tr class="${rowClass}">
                <td>${detail.produit_nom || 'N/A'}</td>
                <td>${detail.lot_numero || 'N/A'}</td>
                <td>${parseFloat(detail.qte_nette_controlee || 0).toFixed(2)}</td>
                <td>${parseFloat(detail.qte_brute_saisie || 0).toFixed(2)}</td>
                <td>${parseFloat(detail.nombre_sacs || 0).toFixed(2)}</td>
                <td>${parseFloat(detail.ecart_controle || 0).toFixed(2)}</td>
                <td>${parseFloat(detail.prix_unitaire_net || 0).toLocaleString()} Ar</td>
                <td>${parseFloat(detail.reduction || 0).toLocaleString()} Ar</td>
                <td>${parseFloat(detail.montant_ht || 0).toLocaleString()} Ar</td>
                <td>${producteursHtml}</td>
            </tr>
        `;
    });
    
        detailsHtml += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('#detailsContent').html(detailsHtml);
        $('#modalDetailsAchat').modal('show');
    });
}

function loadProducteursNames() {
    return new Promise((resolve, reject) => {
        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'producteurs',
            sql: 'SELECT id, nom FROM producteurs WHERE classement = "PETIT PLANTEUR"',
            params: JSON.stringify([])
        }).done(function(response) {
            if (response.success) {
                const producteursMap = {};
                response.data.forEach(producteur => {
                    producteursMap[producteur.id] = producteur.nom;
                });
                resolve(producteursMap);
            } else {
                reject(response.message);
            }
        }).fail(function() {
            reject('Erreur lors du chargement des producteurs');
        });
    });
}

function editAchat(id) {
    currentAchatId = id;
    
    // Charger les données de l'achat
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'achat_entete',
        sql: `SELECT 
                ae.*,
                p.nom as fournisseur_nom
              FROM achat_entete ae
              LEFT JOIN producteurs p ON ae.fournisseur_id = p.id
              WHERE ae.id = ?`,
        params: JSON.stringify([id])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const achat = response.data[0];
            
            // Remplir le formulaire d'entête
            $('#referenceAchat').val(achat.reference_achat);
            $('#fournisseurAchat').val(achat.fournisseur_id);
            $('#dateAchat').val(achat.date_achat);
            $('#dateLivraison').val(achat.date_livraison);
            
            // Charger les détails de l'achat
            loadAchatDetails(id);
            
            // Mettre à jour le titre du modal et le bouton
            $('#modalAchatTitle').html('<i class="fas fa-edit"></i> Modifier l\'Achat');
            $('#btnSaveText').text('Modifier l\'Achat');
            
            // Ouvrir le modal
            $('#modalAchat').modal('show');
        } else {
            showAlert('Erreur', 'Achat non trouvé', 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement de l\'achat:', error);
        showAlert('Erreur', 'Impossible de charger les données de l\'achat', 'error');
    });
}

function deleteAchat(id) {
    showConfirm('Supprimer l\'achat', 'Voulez-vous supprimer cet achat ?', 'Oui, supprimer', 'Annuler')
        .then((result) => {
            if (result.isConfirmed) {
                $.post('../includes/traitement.php', {
                    action: 'delete',
                    table: 'achat_entete',
                    id: id
                }).done(function(response) {
                    if (response.success) {
                        showAlert('Succès', 'Achat supprimé', 'success');
                        loadAchats();
                    } else {
                        showAlert('Erreur', response.message, 'error');
                    }
                });
            }
        });
}

function saveAchat() {
    const enteteData = {
        reference_achat: $('#referenceAchat').val(),
        fournisseur_id: $('#fournisseurAchat').val(),
        date_achat: $('#dateAchat').val(),
        date_livraison: $('#dateLivraison').val(),
        statut: 'SAISIE' // Statut par défaut
    };
    
    if (!enteteData.fournisseur_id) {
        showAlert('Erreur', 'Veuillez sélectionner un fournisseur', 'error');
        return;
    }
    
    if (achatDetails.length === 0) {
        showAlert('Erreur', 'Veuillez ajouter au moins un produit', 'error');
        return;
    }
    
    if (currentAchatId) {
        // Mode modification
        $.post('../includes/traitement.php', {
            action: 'update',
            table: 'achat_entete',
            id: currentAchatId,
            data: JSON.stringify(enteteData)
        }).done(function(response) {
            if (response.success) {
                // Supprimer les anciens détails et recréer
                deleteAchatDetails(currentAchatId);
                // Sauvegarder les nouveaux détails
                saveAchatDetails(currentAchatId);
            } else {
                showAlert('Erreur', response.message, 'error');
            }
        });
    } else {
        // Mode création
        $.post('../includes/traitement.php', {
            action: 'create',
            table: 'achat_entete',
            data: JSON.stringify(enteteData)
        }).done(function(response) {
            if (response.success) {
                const achatId = response.id;
                // Sauvegarder les détails
                saveAchatDetails(achatId);
            } else {
                showAlert('Erreur', response.message, 'error');
            }
        });
    }
}

function saveAchatDetails(achatId) {
    const detailsData = achatDetails.map(detail => ({
        achat_entete_id: achatId,
        produit_id: detail.produit_id,
        depot_id: detail.depot_id,
        petit_planteur_id: detail.petit_planteur_id,
        unite_achat_id: detail.unite_id,
        qte_brute_saisie: detail.poids_brut,
        qte_nette_controlee: detail.poids_net,
        prix_unitaire_net: detail.prix_unitaire,
        montant_ht: detail.montant_ht,
        reduction: detail.reduction,
        stock_avant_entree: 0,
        lot_numero: detail.lot_numero,
        nombre_sacs: detail.nbr_sacs,
        ecart_controle: detail.ecart || 0
    }));
    
    $.post('../includes/traitement.php', {
        action: 'create_bulk',
        table: 'achat_detail',
        data: JSON.stringify(detailsData)
    }).done(function(response) {
        if (response.success) {
            showAlert('Succès', 'Achat enregistré avec succès', 'success');
            $('#modalAchat').modal('hide');
            loadAchats();
            resetAchatForm();
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    });
}


// ===== GESTION DES PRODUITS D'ACHAT =====

function addProduitAchat() {
    const row = `
        <tr>
            <td>
                <select class="form-select form-select-sm produit-select">
                    <option value="">Sélectionner un produit</option>
                    ${Object.values(window.produitsCache || {}).map(p => 
                        `<option value="${p.id}" data-prix="${p.prix_achat}">${p.nom}</option>`
                    ).join('')}
                </select>
            </td>
            <td><input type="text" class="form-control form-control-sm lot-numero" placeholder="N° Lot"></td>
            <td><input type="number" class="form-control form-control-sm poids-net" step="0.01" value="0"></td>
            <td><input type="number" class="form-control form-control-sm poids-brut" step="0.01" value="0"></td>
            <td><input type="number" class="form-control form-control-sm ecart" step="0.01" value="0"></td>
            <td><input type="number" class="form-control form-control-sm nbr-sacs" step="0.01" value="0"></td>
            <td><input type="number" class="form-control form-control-sm reduction" step="0.01" value="0"></td>
            <td>
                <button class="btn btn-sm btn-danger btn-remove-produit" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    $('#tableDetailsAchat tbody').append(row);
    
    // Initialiser Select2 sur les nouveaux éléments
    $('.produit-select').select2({
        theme: 'bootstrap-5',
        placeholder: 'Sélectionner...',
        allowClear: true
    });
    
    // Ajouter un événement pour calculer automatiquement le poids brut lors de la sélection
    $('.produit-select').on('change', function() {
        const row = $(this).closest('tr');
        const produitId = $(this).val();
        if (produitId && window.produitsCache[produitId]) {
            const produit = window.produitsCache[produitId];
            
            // Calculer automatiquement le nombre de sacs à partir de la quantité de présentation
            if (produit.qte_presentation && produit.qte_presentation > 0) {
                row.find('.nbr-sacs').val(produit.qte_presentation);
                
                // Calculer le poids brut à partir du nombre de sacs (1 sac = 67 kg)
                const poidsBrutCalc = produit.qte_presentation * 67;
                row.find('.poids-brut').val(poidsBrutCalc);
                
                // Calculer automatiquement le poids net (3 kg fraîche = 1 kg sec)
                const poidsNetCalc = calculatePoidsNetFromBrut(poidsBrutCalc);
                row.find('.poids-net').val(poidsNetCalc.toFixed(2));
                
                // Calculer l'écart
                const ecart = poidsBrutCalc - poidsNetCalc;
                row.find('.ecart').val(ecart.toFixed(2));
                
                updateAchatDetailsCache();
            }
        }
    });
}

function updateRowCalculations(row) {
    const poidsNet = parseFloat(row.find('.poids-net').val()) || 0;
    const poidsBrut = parseFloat(row.find('.poids-brut').val()) || 0;
    const nbrSacs = parseFloat(row.find('.nbr-sacs').val()) || 0;
    const reduction = parseFloat(row.find('.reduction').val()) || 0;
    const produitId = row.find('.produit-select').val();
    
    // Calcul automatique du nombre de sacs et poids brut à partir du produit sélectionné
    // Seulement si aucun champ n'a été saisi manuellement
    if (produitId && window.produitsCache[produitId] && nbrSacs === 0 && poidsBrut === 0 && poidsNet === 0) {
        const produit = window.produitsCache[produitId];
        if (produit.qte_presentation && produit.qte_presentation > 0) {
            // Calculer le nombre de sacs basé sur la quantité de présentation du produit
            row.find('.nbr-sacs').val(produit.qte_presentation);
            
            // Calculer le poids brut basé sur le nombre de sacs (1 sac = 67 kg)
            const poidsBrutCalc = produit.qte_presentation * 67;
            row.find('.poids-brut').val(poidsBrutCalc);
            
            // Calculer automatiquement le poids net (3 kg fraîche = 1 kg sec)
            const poidsNetCalc = calculatePoidsNetFromBrut(poidsBrutCalc);
            row.find('.poids-net').val(poidsNetCalc.toFixed(2));
            
            // Calculer l'écart
            const ecart = poidsBrutCalc - poidsNetCalc;
            row.find('.ecart').val(ecart.toFixed(2));
        }
    }
    
    // Recalculer l'écart si les poids ont été modifiés
    const poidsBrutActuel = parseFloat(row.find('.poids-brut').val()) || 0;
    const poidsNetActuel = parseFloat(row.find('.poids-net').val()) || 0;
    const ecart = poidsBrutActuel - poidsNetActuel;
    row.find('.ecart').val(ecart.toFixed(2));
    
    // Mettre à jour le cache des détails
    updateAchatDetailsCache();
}

// Fonction pour calculer le poids sec selon la règle : 3 kg fraîche → 1 kg sec
function calculatePoidsSec(poidsBrut) {
    return poidsBrut / 3;
}

/**
 * Calcule le poids net à partir du poids brut selon la règle 3 kg fraîche = 1 kg sec
 * @param {number} poidsBrut - Poids brut en kg fraîche
 * @returns {number} Poids net en kg sec
 */
function calculatePoidsNetFromBrut(poidsBrut) {
    return Math.round((poidsBrut / 3) * 100) / 100; // Arrondi à 2 décimales
}

// Fonction pour calculer le nombre de sacs secs nécessaires : 1 sac sec = 65 kg
function calculateSacsSecs(poidsSec) {
    return poidsSec / 65;
}

function updateAchatDetailsCache() {
    achatDetails = [];
    $('#tableDetailsAchat tbody tr').each(function() {
        const row = $(this);
        const produitId = row.find('.produit-select').val();
        const poidsNet = parseFloat(row.find('.poids-net').val()) || 0;
        const poidsBrut = parseFloat(row.find('.poids-brut').val()) || 0;
        const ecart = parseFloat(row.find('.ecart').val()) || 0;
        const reduction = parseFloat(row.find('.reduction').val()) || 0;
        const lotNumero = row.find('.lot-numero').val();
        const nbrSacs = parseFloat(row.find('.nbr-sacs').val()) || 0;
        
        if (produitId) {
            achatDetails.push({
                produit_id: parseInt(produitId),
                depot_id: 1, // Dépôt par défaut
                petit_planteur_id: null,
                unite_id: 3, // KG par défaut
                poids_net: poidsNet,
                poids_brut: poidsBrut,
                ecart: ecart,
                prix_unitaire: 0, // Prix saisi à l'étape paiement
                reduction: reduction,
                montant_ht: 0, // Calculé à l'étape paiement
                lot_numero: lotNumero,
                nbr_sacs: nbrSacs
            });
        }
    });
}

function updateTotals() {
    updateAchatDetailsCache();
    
    // Les montants seront calculés à l'étape de paiement
    const totalReduction = achatDetails.reduce((sum, detail) => sum + detail.reduction, 0);
    
    $('#montantHT').text('0 Ar');
    $('#montantReduction').text(totalReduction.toLocaleString() + ' Ar');
    $('#montantTTC').text('0 Ar');
}

// ===== FONCTIONS UTILITAIRES =====

function generateReferenceAchat() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const timestamp = Date.now().toString().slice(-6); // 6 chiffres au lieu de 4
    
    const reference = `ACH-${year}-${month}${day}-${timestamp}`;
    $('#referenceAchat').val(reference);
}

function setDefaultDates() {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const todayStr = today.toISOString().split('T')[0];
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    
    $('#dateAchat').val(todayStr);
    $('#dateLivraison').val(tomorrowStr);
}

function initSelect2() {
    // Initialiser Select2 sur les sélecteurs
    $('#fournisseurAchat').select2({
        theme: 'bootstrap-5',
        placeholder: 'Sélectionner un fournisseur',
        allowClear: true
    });
    
    $('#modePaiement').select2({
        theme: 'bootstrap-5',
        placeholder: 'Sélectionner un mode de paiement',
        allowClear: true
    });
}

function loadPlanteurs() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'producteurs'
    }).done(function(response) {
        if (response.success) {
            window.planteursCache = {};
            response.data.forEach(planteur => {
                window.planteursCache[planteur.id] = planteur;
            });
        }
    });
}

function getSelectedAchatIds() {
    const selectedIds = [];
    $('#tableAchats input[type="checkbox"]:checked').each(function() {
        const row = $(this).closest('tr');
        const id = row.find('button[data-id]').first().data('id');
        if (id) selectedIds.push(id);
    });
    return selectedIds;
}

function loadAchatDetails(achatId) {
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'achat_detail',
        sql: `SELECT 
                ad.*,
                p.nom as produit_nom
              FROM achat_detail ad
              LEFT JOIN produits p ON ad.produit_id = p.id
              WHERE ad.achat_entete_id = ?
              ORDER BY ad.id`,
        params: JSON.stringify([achatId])
    }).done(function(response) {
        if (response.success) {
            // Vider complètement le tableau des détails
            $('#tableDetailsAchat tbody').empty();
            achatDetails = []; // Réinitialiser l'array
            
            // Ajouter chaque détail
            response.data.forEach(detail => {
                addProduitAchatFromData(detail);
            });
            
            updateTotals();
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement des détails:', error);
        showAlert('Erreur', 'Impossible de charger les détails de l\'achat', 'error');
    });
}

function addProduitAchatFromData(detail) {
    const row = `
        <tr>
            <td>
                <select class="form-select form-select-sm produit-select">
                    <option value="">Sélectionner un produit</option>
                    ${Object.values(window.produitsCache || {}).map(p => 
                        `<option value="${p.id}" data-prix="${p.prix_achat}" ${p.id == detail.produit_id ? 'selected' : ''}>${p.nom}</option>`
                    ).join('')}
                </select>
            </td>
            <td><input type="text" class="form-control form-control-sm lot-numero" placeholder="N° Lot" value="${detail.lot_numero || ''}"></td>
            <td><input type="number" class="form-control form-control-sm poids-net" step="0.01" value="${detail.qte_nette_controlee || 0}"></td>
            <td><input type="number" class="form-control form-control-sm poids-brut" step="0.01" value="${detail.qte_brute_saisie || 0}"></td>
            <td><input type="number" class="form-control form-control-sm ecart" step="0.01" value="${detail.ecart_controle || 0}"></td>
            <td><input type="number" class="form-control form-control-sm nbr-sacs" step="0.01" value="${detail.nombre_sacs || 0}"></td>
            <td><input type="number" class="form-control form-control-sm reduction" step="0.01" value="${detail.reduction || 0}"></td>
            <td>
                <button class="btn btn-sm btn-danger btn-remove-produit" title="Supprimer">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `;
    $('#tableDetailsAchat tbody').append(row);
    
    // Initialiser Select2 sur les nouveaux éléments
    $('.produit-select').select2({
        theme: 'bootstrap-5',
        placeholder: 'Sélectionner...',
        allowClear: true
    });
    
    // Mettre à jour le cache des détails
    updateAchatDetailsCache();
}

function deleteAchatDetails(achatId) {
    // D'abord, récupérer les IDs des détails à supprimer
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'achat_detail',
        sql: 'SELECT id FROM achat_detail WHERE achat_entete_id = ?',
        params: JSON.stringify([achatId])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const detailIds = response.data.map(detail => detail.id);
            
            // Supprimer les détails en masse
            $.post('../includes/traitement.php', {
                action: 'delete_bulk',
                table: 'achat_detail',
                ids: JSON.stringify(detailIds)
            }).done(function(deleteResponse) {
                if (!deleteResponse.success) {
                    console.error('Erreur lors de la suppression des détails:', deleteResponse.message);
                }
            });
        }
    }).fail(function() {
        console.error('Erreur lors de la récupération des détails à supprimer');
    });
}

function resetAchatForm() {
    $('#formAchatEntete')[0].reset();
    $('#tableDetailsAchat tbody').empty();
    achatDetails = [];
    currentAchatId = null;
    generateReferenceAchat();
    updateTotals();
}

// ===== SUIVI D'ACHAT =====

function showSuiviAchat(id) {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'achat_entete',
        data: JSON.stringify({ id: id })
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const achat = response.data[0];
            displaySuiviAchat(achat);
            $('#modalSuiviAchat').modal('show');
        } else {
            showAlert('Erreur', 'Achat non trouvé', 'error');
        }
    });
}

function displaySuiviAchat(achat) {
    const steps = [
        { key: 'SAISIE', label: 'Saisie', icon: 'fas fa-edit' },
        { key: 'LIVRE', label: 'Livré', icon: 'fas fa-truck' },
        { key: 'CONTROLE', label: 'Contrôlé', icon: 'fas fa-check-circle' },
        { key: 'A_PAYER', label: 'À payer', icon: 'fas fa-credit-card' },
        { key: 'PAYE', label: 'Payé', icon: 'fas fa-check' }
    ];
    
    const currentStepIndex = steps.findIndex(step => step.key === achat.statut);
    
    let html = `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Suivi de l'achat: ${achat.reference_achat}</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Fournisseur:</strong> ${achat.fournisseur_nom || 'N/A'}</p>
                        <p><strong>Date d'achat:</strong> ${achat.date_achat || 'N/A'}</p>
                        <p><strong>Date de livraison:</strong> ${achat.date_livraison || 'N/A'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Statut actuel:</strong> ${getStatutBadge(achat.statut)}</p>
                        <p><strong>Montant total:</strong> ${parseFloat(achat.montant_total || 0).toLocaleString()} Ar</p>
                    </div>
                </div>
                
                <hr>
                
                <h6>Évolution du statut:</h6>
                <div class="timeline">
    `;
    
    steps.forEach((step, index) => {
        let stepClass = 'step-pending';
        if (index < currentStepIndex) {
            stepClass = 'step-completed';
        } else if (index === currentStepIndex) {
            stepClass = 'step-current';
        }
        
        html += `
            <div class="purchase-step">
                <div class="step-icon ${stepClass}">
                    <i class="${step.icon}"></i>
                </div>
                <div>
                    <strong>${step.label}</strong>
                    ${index < currentStepIndex ? '<span class="text-success ms-2">✓ Terminé</span>' : 
                      index === currentStepIndex ? '<span class="text-primary ms-2">● En cours</span>' : 
                      '<span class="text-muted ms-2">○ En attente</span>'}
                </div>
            </div>
        `;
    });
    
    html += `
                </div>
            </div>
        </div>
    `;
    
    $('#suiviContent').html(html);
}

// ===== ACTIONS MULTIPLES =====

function validateAchats(ids) {
    showConfirm('Valider les achats', `Voulez-vous valider ${ids.length} achat(s) sélectionné(s) ?`, 'Oui, valider', 'Annuler')
        .then((result) => {
            if (result.isConfirmed) {
                // Mettre à jour le statut des achats
                updateAchatsStatus(ids, 'CONTROLE');
            }
        });
}

function markAsDelivered(ids) {
    showConfirm('Marquer comme livré', `Voulez-vous marquer ${ids.length} achat(s) comme livré(s) ?`, 'Oui, marquer', 'Annuler')
        .then((result) => {
            if (result.isConfirmed) {
                updateAchatsStatus(ids, 'LIVRE');
            }
        });
}

function cancelAchats(ids) {
    showConfirm('Annuler les achats', `Voulez-vous annuler ${ids.length} achat(s) sélectionné(s) ?`, 'Oui, annuler', 'Annuler')
        .then((result) => {
            if (result.isConfirmed) {
                updateAchatsStatus(ids, 'ANNULE');
            }
        });
}

function updateAchatsStatus(ids, newStatus) {
    // Mettre à jour chaque achat
    let completed = 0;
    ids.forEach(id => {
        $.post('../includes/traitement.php', {
            action: 'update',
            table: 'achat_entete',
            id: id,
            data: JSON.stringify({ statut: newStatus })
        }).done(function(response) {
            completed++;
            if (completed === ids.length) {
                showAlert('Succès', `${ids.length} achat(s) mis à jour`, 'success');
                loadAchats();
            }
        }).fail(function() {
            completed++;
            if (completed === ids.length) {
                showAlert('Erreur', 'Erreur lors de la mise à jour', 'error');
            }
        });
    });
}

// ===== VALIDATION D'ACHAT =====

function validateAchat(id) {
    // Charger les détails de l'achat
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'achat_entete',
        sql: `SELECT 
                ae.id,
                ae.reference_achat,
                p.nom as fournisseur_nom,
                ad.id as detail_id,
                ad.produit_id,
                pr.nom as produit_nom,
                ad.lot_numero,
                ad.qte_brute_saisie as poids_brut,
                ad.qte_nette_controlee as poids_net,
                ad.ecart_controle as ecart,
                ad.nombre_sacs as nbr_sacs,
                ad.reduction
              FROM achat_entete ae
              LEFT JOIN producteurs p ON ae.fournisseur_id = p.id
              LEFT JOIN achat_detail ad ON ae.id = ad.achat_entete_id
              LEFT JOIN produits pr ON ad.produit_id = pr.id
              WHERE ae.id = ?`,
        params: JSON.stringify([id])
    }).done(function(response) {
        if (response.success) {
            displayValidationModal(response.data);
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    });
}

function displayValidationModal(details) {
    let html = `
        <div class="modal fade" id="modalValidation" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Validation de l'achat</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Produit</th>
                                        <th>N° Lot</th>
                                        <th>Poids Net</th>
                                        <th>Poids Brut</th>
                                        <th>Ecart</th>
                                        <th>Nbr. Sacs</th>
                                        <th>Réduction</th>
                                    </tr>
                                </thead>
                                <tbody>
    `;
    
    details.forEach(detail => {
        html += `
            <tr>
                <td>${detail.produit_nom}</td>
                <td><input type="text" class="form-control form-control-sm" value="${detail.lot_numero || ''}" data-field="lot_numero" data-detail-id="${detail.detail_id}"></td>
                <td><input type="number" class="form-control form-control-sm" value="${detail.poids_net || 0}" data-field="poids_net" data-detail-id="${detail.detail_id}" step="0.01"></td>
                <td><input type="number" class="form-control form-control-sm" value="${detail.poids_brut || 0}" data-field="poids_brut" data-detail-id="${detail.detail_id}" step="0.01"></td>
                <td><input type="number" class="form-control form-control-sm" value="${detail.ecart || 0}" data-field="ecart" data-detail-id="${detail.detail_id}" step="0.01"></td>
                <td><input type="number" class="form-control form-control-sm" value="${detail.nbr_sacs || 0}" data-field="nbr_sacs" data-detail-id="${detail.detail_id}" step="0.01"></td>
                <td><input type="number" class="form-control form-control-sm" value="${detail.reduction || 0}" data-field="reduction" data-detail-id="${detail.detail_id}" step="0.01"></td>
                <td><input type="hidden" data-field="produit_id" data-detail-id="${detail.detail_id}" value="${detail.produit_id}"></td>
            </tr>
        `;
    });
    
    html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-success" id="btnConfirmValidation">Valider et alimenter le stock</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Supprimer l'ancien modal s'il existe
    $('#modalValidation').remove();
    
    // Ajouter le nouveau modal
    $('body').append(html);
    
    // Afficher le modal
    $('#modalValidation').modal('show');
    
    // Charger les petits planteurs dans tous les selects
    loadPetitsPlanteursForValidation();
    
    // Event handler pour la validation
    $('#btnConfirmValidation').on('click', function() {
        confirmValidation(details[0].id);
    });
    
}

function confirmValidation(achatId) {
    const updates = [];
    const stockUpdates = [];
    
    // Validation simplifiée sans vérification des petits planteurs
    
    $('#modalValidation tbody tr').each(function() {
        const row = $(this);
        const detailId = row.find('input[data-detail-id]').first().data('detail-id');
        
        if (detailId) {
            const lotNumero = row.find('input[data-field="lot_numero"]').val();
            const poidsNet = parseFloat(row.find('input[data-field="poids_net"]').val()) || 0;
            const poidsBrut = parseFloat(row.find('input[data-field="poids_brut"]').val()) || 0;
            const ecart = parseFloat(row.find('input[data-field="ecart"]').val()) || 0;
            const nbrSacs = parseFloat(row.find('input[data-field="nbr_sacs"]').val()) || 0;
            const reduction = parseFloat(row.find('input[data-field="reduction"]').val()) || 0;
            
            const update = {
                id: detailId,
                lot_numero: lotNumero,
                qte_nette_controlee: poidsNet,
                qte_brute_saisie: poidsBrut,
                ecart_controle: ecart,
                nombre_sacs: nbrSacs,
                reduction: reduction
            };
            updates.push(update);
            
            // Préparer la mise à jour du stock
            // Récupérer les informations du produit depuis la ligne
            const produitId = row.find('input[data-field="produit_id"]').val();
            if (produitId && poidsNet > 0) {
                stockUpdates.push({
                    produit_id: parseInt(produitId),
                    quantite: poidsNet,
                    lot_numero: lotNumero,
                    depot_id: 1, // Dépôt par défaut - à améliorer pour récupérer le vrai dépôt
                    unite_id: 1, // Unité par défaut (kg) - à améliorer pour récupérer l'unité du produit
                    type_mouvement: 'ENTREE',
                    reference_document: `ACH-${achatId}`
                });
            }
        }
    });
    
    // Mettre à jour les détails
    $.post('../includes/traitement.php', {
        action: 'update_bulk',
        table: 'achat_detail',
        data: JSON.stringify(updates)
    }).done(function(response) {
        if (response.success) {
            // Mettre à jour le stock pour chaque produit
            if (stockUpdates.length > 0) {
                updateStockFromValidation(stockUpdates, achatId);
            } else {
                // Pas de mise à jour de stock, juste changer le statut
                updateAchatStatus(achatId, 'CONTROLE');
            }
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    });
}

function updateStockFromValidation(stockUpdates, achatId) {
    // Mettre à jour le stock pour chaque produit
    let completedUpdates = 0;
    const totalUpdates = stockUpdates.length;
    
    stockUpdates.forEach(stockUpdate => {
        // Récupérer les informations du produit pour la conversion
        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'produits',
            sql: `SELECT 
                    p.unite_stock_id,
                    u.libelle as unite_stock_libelle,
                    pr.libelle as presentation_libelle,
                    p.qte_presentation,
                    f.libelle as forme_libelle,
                    p.qte_forme
                  FROM produits p
                  LEFT JOIN unites u ON p.unite_stock_id = u.id
                  LEFT JOIN presentation pr ON p.presentation_id = pr.id
                  LEFT JOIN forme f ON p.forme_id = f.id
                  WHERE p.id = ?`,
            params: JSON.stringify([stockUpdate.produit_id])
        }).done(function(produitResponse) {
            if (produitResponse.success && produitResponse.data.length > 0) {
                const produit = produitResponse.data[0];
                
                // Enrichir les données de stock avec les informations du produit
                stockUpdate.unite_id = produit.unite_stock_id;
                stockUpdate.presentation = produit.presentation_libelle;
                stockUpdate.qte_presentation = produit.qte_presentation || 1;
                stockUpdate.forme = produit.forme_libelle;
                stockUpdate.qte_forme = produit.qte_forme || 67;
                
                // Vérifier si le stock existe déjà pour ce produit et ce lot
                $.post('../includes/traitement.php', {
                    action: 'execute_sql',
                    table: 'produits_stock',
                    sql: `SELECT id, quantite FROM produits_stock 
                          WHERE produit_id = ? AND lot_numero = ?`,
                    params: JSON.stringify([stockUpdate.produit_id, stockUpdate.lot_numero])
                }).done(function(response) {
                    if (response.success) {
                        if (response.data.length > 0) {
                            // Mettre à jour le stock existant
                            const existingStock = response.data[0];
                            const newQuantity = parseFloat(existingStock.quantite) + parseFloat(stockUpdate.quantite);
                            
                            $.post('../includes/traitement.php', {
                                action: 'update',
                                table: 'produits_stock',
                                id: existingStock.id,
                                data: JSON.stringify({ quantite: newQuantity })
                            }).done(function() {
                                // Créer le mouvement de stock avec les bonnes données
                                createMouvementStock(stockUpdate, 'ENTREE', achatId);
                                
                                completedUpdates++;
                                if (completedUpdates === totalUpdates) {
                                    updateAchatStatus(achatId, 'CONTROLE');
                                }
                            });
                        } else {
                            // Créer un nouveau stock
                            $.post('../includes/traitement.php', {
                                action: 'create',
                                table: 'produits_stock',
                                data: JSON.stringify({
                                    produit_id: stockUpdate.produit_id,
                                    depot_id: stockUpdate.depot_id || 1, // Dépôt de l'achat
                                    quantite: stockUpdate.quantite,
                                    unite_stock_id: stockUpdate.unite_id || 1, // Unité du produit
                                    lot_numero: stockUpdate.lot_numero
                                })
                            }).done(function() {
                                // Créer le mouvement de stock avec les bonnes données
                                createMouvementStock(stockUpdate, 'ENTREE', achatId);
                                
                                completedUpdates++;
                                if (completedUpdates === totalUpdates) {
                                    updateAchatStatus(achatId, 'CONTROLE');
                                }
                            });
                        }
                    }
                });
            } else {
                console.error('Produit non trouvé pour ID:', stockUpdate.produit_id);
                completedUpdates++;
                if (completedUpdates === totalUpdates) {
                    updateAchatStatus(achatId, 'CONTROLE');
                }
            }
        }).fail(function() {
            console.error('Erreur lors de la récupération des informations du produit');
            completedUpdates++;
            if (completedUpdates === totalUpdates) {
                updateAchatStatus(achatId, 'CONTROLE');
            }
        });
    });
}

function createMouvementStock(stockUpdate, typeMouvement, achatId) {
    // Calculer la conversion pour l'affichage
    let conversionInfo = '';
    if (stockUpdate.presentation && stockUpdate.forme && stockUpdate.qte_forme) {
        const kgParSac = stockUpdate.qte_forme || 67;
        const sacs = Math.floor(stockUpdate.quantite / kgParSac);
        const resteKg = stockUpdate.quantite % kgParSac;
        
        if (sacs > 0) {
            conversionInfo = ` (${sacs} sac`;
            if (sacs > 1) conversionInfo += 's';
            if (resteKg > 0) {
                conversionInfo += ` + ${resteKg.toFixed(2)} kg`;
            }
            conversionInfo += ')';
        }
    }
    
    const mouvementData = {
        produit_id: stockUpdate.produit_id,
        depot_id: stockUpdate.depot_id || 1, // Utiliser le dépôt de l'achat ou défaut
        type_mouvement: typeMouvement,
        quantite: stockUpdate.quantite,
        unite_id: stockUpdate.unite_id || 1, // Utiliser l'unité du produit ou défaut
        lot_numero: stockUpdate.lot_numero,
        reference_document: `ACH-${achatId}`,
        motif: `Validation achat ${achatId} - ${stockUpdate.quantite} kg${conversionInfo}`,
        date_mouvement: new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0]
    };
    
    console.log('Création mouvement de stock:', mouvementData);
    
    $.post('../includes/traitement.php', {
        action: 'create',
        table: 'mouvements_stock',
        data: JSON.stringify(mouvementData)
    }).done(function(response) {
        if (response.success) {
            console.log('Mouvement de stock créé avec succès');
        } else {
            console.error('Erreur lors de la création du mouvement de stock:', response.message);
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur AJAX lors de la création du mouvement de stock:', error);
    });
}

function updateAchatStatus(achatId, status) {
    $.post('../includes/traitement.php', {
        action: 'update',
        table: 'achat_entete',
        id: achatId,
        data: JSON.stringify({ statut: status })
    }).done(function(response) {
        if (response.success) {
            showAlert('Succès', 'Achat validé et stock alimenté', 'success');
            $('#modalValidation').modal('hide');
            loadAchats();
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    });
}

// ===== NOUVELLES FONCTIONS POUR LES BOUTONS D'ACTION =====

function setPaymentStatus(id) {
    Swal.fire({
        title: 'Passer au paiement',
        text: 'Voulez-vous passer cet achat au statut "À payer" ?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Oui, passer au paiement',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            $.post('../includes/traitement.php', {
                action: 'update',
                table: 'achat_entete',
                id: id,
                data: JSON.stringify({ statut: 'A_PAYER' })
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', 'Achat passé au statut "À payer"', 'success');
                    loadAchats();
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            });
        }
    });
}

function cancelAchat(id) {
    Swal.fire({
        title: 'Annuler l\'achat',
        text: 'Voulez-vous vraiment annuler cet achat ? Le stock sera déduit si l\'achat était validé.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Oui, annuler',
        cancelButtonText: 'Non'
    }).then((result) => {
        if (result.isConfirmed) {
            // D'abord, vérifier si l'achat était validé pour déduire le stock
            $.post('../includes/traitement.php', {
                action: 'execute_sql',
                table: 'achat_entete',
                sql: `SELECT statut FROM achat_entete WHERE id = ?`,
                params: JSON.stringify([id])
            }).done(function(response) {
                if (response.success && response.data.length > 0) {
                    const statut = response.data[0].statut;
                    
                    if (statut === 'CONTROLE' || statut === 'A_PAYER' || statut === 'PAYE') {
                        // Déduire le stock
                        deductStockFromCancellation(id);
                    } else {
                        // Juste changer le statut
                        updateAchatStatusToCancel(id);
                    }
                }
            });
        }
    });
}

function deductStockFromCancellation(achatId) {
    // Récupérer les détails de l'achat pour déduire le stock
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'achat_detail',
        sql: `SELECT produit_id, qte_nette_controlee, lot_numero 
              FROM achat_detail 
              WHERE achat_entete_id = ? AND qte_nette_controlee > 0`,
        params: JSON.stringify([achatId])
    }).done(function(response) {
        if (response.success) {
            let completedDeductions = 0;
            const totalDeductions = response.data.length;
            
            if (totalDeductions === 0) {
                updateAchatStatusToCancel(achatId);
                return;
            }
            
            response.data.forEach(detail => {
                // Récupérer les informations du produit pour la conversion
                $.post('../includes/traitement.php', {
                    action: 'execute_sql',
                    table: 'produits',
                    sql: `SELECT 
                            p.unite_stock_id,
                            u.libelle as unite_stock_libelle,
                            pr.libelle as presentation_libelle,
                            p.qte_presentation,
                            f.libelle as forme_libelle,
                            p.qte_forme
                          FROM produits p
                          LEFT JOIN unites u ON p.unite_stock_id = u.id
                          LEFT JOIN presentation pr ON p.presentation_id = pr.id
                          LEFT JOIN forme f ON p.forme_id = f.id
                          WHERE p.id = ?`,
                    params: JSON.stringify([detail.produit_id])
                }).done(function(produitResponse) {
                    if (produitResponse.success && produitResponse.data.length > 0) {
                        const produit = produitResponse.data[0];
                        
                        // Enrichir les données de stock avec les informations du produit
                        const stockUpdate = {
                            produit_id: detail.produit_id,
                            quantite: detail.qte_nette_controlee,
                            lot_numero: detail.lot_numero,
                            depot_id: 1, // Dépôt par défaut
                            unite_id: produit.unite_stock_id,
                            presentation: produit.presentation_libelle,
                            qte_presentation: produit.qte_presentation || 1,
                            forme: produit.forme_libelle,
                            qte_forme: produit.qte_forme || 67
                        };
                        
                        // Déduire le stock
                        $.post('../includes/traitement.php', {
                            action: 'execute_sql',
                            table: 'produits_stock',
                            sql: `SELECT id, quantite FROM produits_stock 
                                  WHERE produit_id = ? AND lot_numero = ?`,
                            params: JSON.stringify([detail.produit_id, detail.lot_numero])
                        }).done(function(stockResponse) {
                            if (stockResponse.success && stockResponse.data.length > 0) {
                                const existingStock = stockResponse.data[0];
                                const newQuantity = Math.max(0, parseFloat(existingStock.quantite) - parseFloat(detail.qte_nette_controlee));
                                
                                $.post('../includes/traitement.php', {
                                    action: 'update',
                                    table: 'produits_stock',
                                    id: existingStock.id,
                                    data: JSON.stringify({ quantite: newQuantity })
                                }).done(function() {
                                    // Créer le mouvement de stock pour la déduction
                                    createMouvementStock(stockUpdate, 'SORTIE', achatId);
                                    
                                    completedDeductions++;
                                    if (completedDeductions === totalDeductions) {
                                        updateAchatStatusToCancel(achatId);
                                    }
                                });
                            } else {
                                completedDeductions++;
                                if (completedDeductions === totalDeductions) {
                                    updateAchatStatusToCancel(achatId);
                                }
                            }
                        });
                    } else {
                        completedDeductions++;
                        if (completedDeductions === totalDeductions) {
                            updateAchatStatusToCancel(achatId);
                        }
                    }
                });
            });
        } else {
            updateAchatStatusToCancel(achatId);
        }
    });
}

function updateAchatStatusToCancel(achatId) {
    $.post('../includes/traitement.php', {
        action: 'update',
        table: 'achat_entete',
        id: achatId,
        data: JSON.stringify({ statut: 'ANNULE' })
    }).done(function(response) {
        if (response.success) {
            showAlert('Succès', 'Achat annulé et stock déduit', 'success');
            loadAchats();
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    });
}

function sendEmailAchat(id) {
    showAlert('Info', 'Fonctionnalité d\'envoi par email en cours de développement', 'info');
}

function printAchat(id) {
    // Ouvrir une nouvelle fenêtre pour l'impression
    const printUrl = `../includes/generate_achat_pdf.php?id=${id}`;
    window.open(printUrl, '_blank');
}

// ===== GESTION DES FILTRES =====

function applyFilters() {
    if (achatsTable) {
        const status = $('#filterStatus').val();
        const dateFrom = $('#filterDateFrom').val();
        const dateTo = $('#filterDateTo').val();
        const fournisseur = $('#filterFournisseur').val();
        
        // Construire la requête SQL avec les filtres
        let sql = `SELECT 
                ae.id,
                ae.reference_achat,
                p.nom as fournisseur_nom,
                ae.date_achat,
                ae.date_livraison,
                ae.statut,
                ae.frais_transport,
                COALESCE(SUM(ad.montant_ht), 0) as montant_total
              FROM achat_entete ae
              LEFT JOIN producteurs p ON ae.fournisseur_id = p.id
              LEFT JOIN achat_detail ad ON ae.id = ad.achat_entete_id
              WHERE 1=1`;
        
        const params = [];
        
        if (status) {
            sql += ` AND ae.statut = ?`;
            params.push(status);
        }
        
        if (dateFrom) {
            sql += ` AND ae.date_achat >= ?`;
            params.push(dateFrom);
        }
        
        if (dateTo) {
            sql += ` AND ae.date_achat <= ?`;
            params.push(dateTo);
        }
        
        if (fournisseur) {
            sql += ` AND ae.fournisseur_id = ?`;
            params.push(fournisseur);
        }
        
        sql += ` GROUP BY ae.id ORDER BY ae.date_creation DESC`;
        
        // Charger les données filtrées
        $.post('../includes/traitement.php', {
            action: 'execute_sql',
            table: 'produits',
            sql: sql,
            params: JSON.stringify(params)
        }).done(function(response) {
            if (response.success) {
                displayAchatsFiltered(response.data);
            } else {
                showAlert('Erreur', response.message, 'error');
            }
        }).fail(function(xhr, status, error) {
            console.error('Erreur lors du filtrage:', error);
            showAlert('Erreur', 'Impossible d\'appliquer les filtres', 'error');
        });
    }
}

function resetFilters() {
    $('#filterStatus').val('');
    $('#filterDateFrom').val('');
    $('#filterDateTo').val('');
    $('#filterFournisseur').val('');
    
    // Recharger tous les achats
    loadAchatsData();
    
    // Recharger les statistiques
    loadAllStatistics();
}

function loadPetitsPlanteursForValidation() {
    // Charger seulement les producteurs avec classement PETIT PLANTEUR
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'producteurs',
        sql: 'SELECT * FROM producteurs WHERE classement = "PETIT PLANTEUR" ORDER BY nom ASC',
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            const planteurs = response.data;
            
            // Remplir tous les selects de petits planteurs dans le modal de validation
            $('#modalValidation select[data-field="planteur_select"]').each(function() {
                const select = $(this);
                select.empty();
                select.append('<option value="">Sélectionner un planteur</option>');
                
                planteurs.forEach(planteur => {
                    select.append(`<option value="${planteur.id}" data-nom="${planteur.nom}">${planteur.nom}</option>`);
                });
            });
        }
    }).fail(function() {
        console.error('Erreur lors du chargement des petits planteurs');
    });
}

// ===== GESTION DES PLANTEURS AVEC QUANTITÉS =====

function addPlanteurToDetail(detailId) {
    const select = $(`select[data-field="planteur_select"][data-detail-id="${detailId}"]`);
    const qteInput = $(`input[data-field="planteur_qte"][data-detail-id="${detailId}"]`);
    
    const selectedOption = select.find('option:selected');
    const planteurId = selectedOption.val();
    const planteurNom = selectedOption.data('nom');
    const qte = parseFloat(qteInput.val()) || 0;
    
    if (!planteurId) {
        showAlert('Erreur', 'Veuillez sélectionner un planteur', 'warning');
        return;
    }
    
    if (qte <= 0) {
        showAlert('Erreur', 'Veuillez saisir une quantité valide', 'warning');
        return;
    }
    
    // Vérifier si le planteur n'est pas déjà ajouté
    const existingPlanteur = $(`.planteurs-list[data-detail-id="${detailId}"] .planteur-item[data-planteur-id="${planteurId}"]`);
    if (existingPlanteur.length > 0) {
        showAlert('Erreur', 'Ce planteur est déjà ajouté', 'warning');
        return;
    }
    
    // Ajouter le planteur à la liste
    const planteursList = $(`.planteurs-list[data-detail-id="${detailId}"]`);
    const planteurItem = `
        <div class="planteur-item d-flex align-items-center gap-1 mb-1 p-1 border rounded" data-planteur-id="${planteurId}">
            <span class="flex-grow-1">${planteurNom}</span>
            <input type="number" class="form-control form-control-sm planteur-qte-input" 
                   data-detail-id="${detailId}" data-planteur-id="${planteurId}"
                   value="${qte}" step="0.01" style="width: 80px;">
            <button type="button" class="btn btn-sm btn-outline-danger btn-remove-planteur" 
                    data-detail-id="${detailId}" data-planteur-id="${planteurId}" title="Supprimer">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    planteursList.append(planteurItem);
    
    // Réinitialiser les champs
    select.val('');
    qteInput.val('');
    
    // Mettre à jour le total
    updatePlanteursTotal(detailId);
}

function removePlanteurFromDetail(detailId, planteurId) {
    $(`.planteurs-list[data-detail-id="${detailId}"] .planteur-item[data-planteur-id="${planteurId}"]`).remove();
    updatePlanteursTotal(detailId);
}

function updatePlanteursTotal(detailId) {
    let total = 0;
    $(`.planteurs-list[data-detail-id="${detailId}"] .planteur-qte-input`).each(function() {
        const qte = parseFloat($(this).val()) || 0;
        total += qte;
    });
    
    $(`.total-planteurs-qte[data-detail-id="${detailId}"]`).text(total.toFixed(2));
    
    // Vérifier si le total correspond à la quantité du produit
    const produitTotal = parseFloat($(`.produit-total-qte[data-detail-id="${detailId}"]`).text()) || 0;
    const container = $(`.planteurs-container[data-detail-id="${detailId}"]`);
    
    if (Math.abs(total - produitTotal) < 0.01) {
        container.find('small').removeClass('text-danger').addClass('text-success');
    } else {
        container.find('small').removeClass('text-success').addClass('text-danger');
    }
}

// ===== GESTION DES PETITS PLANTEURS =====

function showPlanteursModal(achatId) {
    // Charger les détails de l'achat
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'achat_detail',
        sql: `SELECT 
                ad.*,
                p.nom as produit_nom
              FROM achat_detail ad
              LEFT JOIN produits p ON ad.produit_id = p.id
              WHERE ad.achat_entete_id = ?
              ORDER BY ad.id`,
        params: JSON.stringify([achatId])
    }).done(function(response) {
        if (response.success) {
            displayPlanteursModal(achatId, response.data);
        } else {
            showAlert('Erreur', 'Impossible de charger les détails de l\'achat', 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible de charger les détails de l\'achat', 'error');
    });
}

function displayPlanteursModal(achatId, details) {
    let html = `
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> 
            Sélectionnez les petits planteurs pour chaque produit de cet achat.
        </div>
        
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th>N° Lot</th>
                        <th>Poids Net (kg)</th>
                        <th>Petits Planteurs</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    details.forEach(detail => {
        html += `
            <tr>
                <td>${detail.produit_nom || 'N/A'}</td>
                <td>${detail.lot_numero || 'N/A'}</td>
                <td>${parseFloat(detail.qte_nette_controlee || 0).toFixed(2)}</td>
                <td>
                    <div class="planteurs-container" data-detail-id="${detail.id}">
                        <div class="d-flex gap-1 mb-1">
                            <select class="form-select form-select-sm" data-field="planteur_select" data-detail-id="${detail.id}" style="flex: 1;">
                                <option value="">Sélectionner un planteur</option>
                            </select>
                            <input type="number" class="form-control form-control-sm" data-field="planteur_qte" data-detail-id="${detail.id}" placeholder="Qte" step="0.01" style="width: 80px;">
                            <button type="button" class="btn btn-sm btn-outline-success btn-add-planteur" data-detail-id="${detail.id}" title="Ajouter">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="planteurs-list" data-detail-id="${detail.id}">
                            <!-- Liste des planteurs ajoutés -->
                        </div>
                        <small class="text-muted">
                            Total: <span class="total-planteurs-qte" data-detail-id="${detail.id}">0</span> kg
                            / <span class="produit-total-qte" data-detail-id="${detail.id}">${parseFloat(detail.qte_nette_controlee || 0).toFixed(2)}</span> kg
                        </small>
                    </div>
                </td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
    `;
    
    $('#planteursContent').html(html);
    
    // Charger les petits planteurs
    loadPlanteursForModal();
    
    // Pré-remplir avec les valeurs existantes
    prefillPlanteurs(details);
    
    // Event handlers pour les planteurs avec quantités dans le modal
    $(document).off('click', '.btn-add-planteur').on('click', '.btn-add-planteur', function() {
        const detailId = $(this).data('detail-id');
        addPlanteurToDetail(detailId);
    });
    
    $(document).off('click', '.btn-remove-planteur').on('click', '.btn-remove-planteur', function() {
        const detailId = $(this).data('detail-id');
        const planteurId = $(this).data('planteur-id');
        removePlanteurFromDetail(detailId, planteurId);
    });
    
    $(document).off('input', '.planteur-qte-input').on('input', '.planteur-qte-input', function() {
        const detailId = $(this).data('detail-id');
        updatePlanteursTotal(detailId);
    });
    
    // Event handler pour sauvegarder
    $('#btnSavePlanteurs').off('click').on('click', function() {
        savePlanteurs(achatId);
    });
    
    $('#modalPlanteurs').modal('show');
}

function loadPlanteursForModal() {
    // Charger seulement les producteurs avec classement PETIT PLANTEUR
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'producteurs',
        sql: 'SELECT * FROM producteurs WHERE classement = "PETIT PLANTEUR" ORDER BY nom ASC',
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            const planteurs = response.data;
            
            // Remplir tous les selects de petits planteurs
            $('#modalPlanteurs select[data-field="planteur_select"]').each(function() {
                const select = $(this);
                select.empty();
                select.append('<option value="">Sélectionner un planteur</option>');
                
                planteurs.forEach(planteur => {
                    select.append(`<option value="${planteur.id}" data-nom="${planteur.nom}">${planteur.nom}</option>`);
                });
            });
        }
    }).fail(function() {
        console.error('Erreur lors du chargement des petits planteurs');
    });
}

function prefillPlanteurs(details) {
    details.forEach(detail => {
        if (detail.producteurs_json) {
            try {
                const planteursData = JSON.parse(detail.producteurs_json);
                const container = $(`.planteurs-container[data-detail-id="${detail.id}"]`);
                const planteursList = container.find('.planteurs-list');
                
                planteursData.forEach(planteurData => {
                    const planteurId = planteurData.producteur_id;
                    const qte = planteurData.qte;
                    
                    // Trouver le nom du planteur
                    const select = container.find('select[data-field="planteur_select"]');
                    const planteurOption = select.find(`option[value="${planteurId}"]`);
                    const planteurNom = planteurOption.data('nom') || planteurOption.text();
                    
                    if (planteurNom) {
                        const planteurItem = `
                            <div class="planteur-item d-flex align-items-center gap-1 mb-1 p-1 border rounded" data-planteur-id="${planteurId}">
                                <span class="flex-grow-1">${planteurNom}</span>
                                <input type="number" class="form-control form-control-sm planteur-qte-input" 
                                       data-detail-id="${detail.id}" data-planteur-id="${planteurId}"
                                       value="${qte}" step="0.01" style="width: 80px;">
                                <button type="button" class="btn btn-sm btn-outline-danger btn-remove-planteur" 
                                        data-detail-id="${detail.id}" data-planteur-id="${planteurId}" title="Supprimer">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        `;
                        planteursList.append(planteurItem);
                    }
                });
                
                // Mettre à jour le total
                updatePlanteursTotal(detail.id);
            } catch (e) {
                console.error('Erreur lors du parsing des données planteurs:', e);
            }
        }
    });
}

function savePlanteurs(achatId) {
    const updates = [];
    
    // Vérifier d'abord que tous les produits ont leurs quantités de planteurs correctes
    let validationOk = true;
    $('.planteurs-container').each(function() {
        const container = $(this);
        const detailId = container.data('detail-id');
        const totalPlanteurs = parseFloat(container.find('.total-planteurs-qte').text()) || 0;
        const produitTotal = parseFloat(container.find('.produit-total-qte').text()) || 0;
        
        if (produitTotal > 0 && Math.abs(totalPlanteurs - produitTotal) > 0.01) {
            validationOk = false;
            showAlert('Erreur', `Le total des quantités des planteurs (${totalPlanteurs} kg) ne correspond pas à la quantité du produit (${produitTotal} kg)`, 'error');
            return false;
        }
    });
    
    if (!validationOk) {
        return;
    }
    
    $('.planteurs-container').each(function() {
        const container = $(this);
        const detailId = container.data('detail-id');
        
        // Récupérer les planteurs avec leurs quantités
        const planteursData = [];
        container.find('.planteur-item').each(function() {
            const planteurId = $(this).data('planteur-id');
            const qte = parseFloat($(this).find('.planteur-qte-input').val()) || 0;
            if (planteurId && qte > 0) {
                planteursData.push({
                    producteur_id: parseInt(planteurId),
                    qte: qte
                });
            }
        });
        
        const update = {
            id: detailId,
            producteurs_json: planteursData.length > 0 ? JSON.stringify(planteursData) : null
        };
        updates.push(update);
    });
    
    if (updates.length === 0) {
        showAlert('Information', 'Aucune modification à sauvegarder', 'info');
        return;
    }
    
    // Mettre à jour les détails
    $.post('../includes/traitement.php', {
        action: 'update_bulk',
        table: 'achat_detail',
        data: JSON.stringify(updates)
    }).done(function(response) {
        if (response.success) {
            showAlert('Succès', 'Petits planteurs enregistrés avec succès', 'success');
            $('#modalPlanteurs').modal('hide');
            loadAchats();
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function() {
        showAlert('Erreur', 'Impossible d\'enregistrer les petits planteurs', 'error');
    });
}

// ===== FONCTIONS DE CONVERSION DE STOCK =====

/**
 * Formate l'affichage des quantités avec conversion selon la règle du produit
 * @param {number} stockKg - Stock en kg
 * @param {string} uniteStock - Unité de stock du produit
 * @param {string} presentation - Présentation du produit (ex: SAC)
 * @param {number} qtePresentation - Quantité de présentation (ex: 1)
 * @param {string} forme - Forme du produit (ex: KG)
 * @param {number} qteForme - Quantité de forme (ex: 67)
 * @returns {string} Affichage formaté
 */
function formatStockQuantity(stockKg, uniteStock, presentation = null, qtePresentation = 1, forme = null, qteForme = 67) {
    if (!stockKg || stockKg === 0) return `0 ${uniteStock || 'kg'}`;
    
    // Si l'unité de stock est KG et qu'on a des informations de présentation/forme
    if (uniteStock && uniteStock.toLowerCase().includes('kg') && presentation && forme) {
        // Appliquer la conversion selon la règle : 1 sac = qteForme kg
        const kgParSac = qteForme || 67; // Quantité de forme (kg par sac)
        const sacs = Math.floor(stockKg / kgParSac);
        const resteKg = stockKg % kgParSac;
        
        let display = `${stockKg} kg`;
        if (sacs > 0) {
            display += ` (${sacs} sac`;
            if (sacs > 1) display += 's';
            if (resteKg > 0) {
                display += ` + ${resteKg.toFixed(2)} kg`;
            }
            display += ')';
        }
        return display;
    }
    
    // Si l'unité de stock est SAC, convertir en kg
    if (uniteStock && uniteStock.toLowerCase().includes('sac')) {
        const kgParSac = qteForme || 67;
        const totalKg = stockKg * kgParSac;
        return `${stockKg} sac (${totalKg} kg)`;
    }
    
    return `${stockKg} ${uniteStock || 'kg'}`;
}

// ===== FONCTIONS D'ALERTE =====

function showAlert(title, message, type) {
    const iconMap = { 'success': 'success', 'error': 'error', 'warning': 'warning', 'info': 'info', 'danger': 'error' };
    Swal.fire({
        title: title,
        html: message,
        icon: iconMap[type] || 'info',
        confirmButtonText: 'OK',
        timer: type === 'success' ? 3000 : null,
        timerProgressBar: type === 'success'
    });
}

function showConfirm(title, text, confirmButtonText = 'Oui', cancelButtonText = 'Non') {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText
    });
}

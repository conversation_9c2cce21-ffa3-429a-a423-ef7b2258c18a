// ===== GESTION DES PAIEMENTS =====

let paiementsTable;
let currentPaiementId = null;
let currentUser = 'admin'; // TODO: Récupérer depuis la session

$(document).ready(function() {
    // Initialisation
    initDataTables();
    loadPaiements();
    loadFournisseurs();
    loadAchatsEnAttente();
    
    // Event handlers
    setupEventHandlers();
    
    // Initialiser la date de paiement
    setDefaultDate();
    
    // Initialiser Select2
    initSelect2();
});

// ===== INITIALISATION =====

function initDataTables() {
    if ($('#tablePaiements').length && !$.fn.DataTable.isDataTable('#tablePaiements')) {
        paiementsTable = $('#tablePaiements').DataTable({
            pageLength: 20,
            lengthMenu: [30, 100, 200],
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            columnDefs: [
                { orderable: false, targets: [0, 8] }, // Colonnes checkbox et actions
                { className: "text-center", targets: [0, 4] }, // Mode paiement centré
                { className: "text-end", targets: [6] } // Montant aligné à droite
            ]
        });
    }
}

// ===== CHARGEMENT DES DONNÉES =====

function loadPaiements() {
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                p.id,
                ae.reference_achat,
                pr.nom as fournisseur_nom,
                p.date_paiement,
                p.mode_paiement,
                p.reference_paiement,
                p.montant,
                p.statut,
                p.effectue_par,
                p.commentaires
              FROM operation_caisse p
              LEFT JOIN achat_entete ae ON p.achat_id = ae.id
              LEFT JOIN producteurs pr ON ae.fournisseur_id = pr.id
              ORDER BY p.date_paiement DESC`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            displayPaiements(response.data);
        } else {
            showAlert('Erreur', response.message, 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur lors du chargement des paiements:', error);
        showAlert('Erreur', 'Impossible de charger les paiements', 'error');
    });
}

function displayPaiements(paiements) {
    if (paiementsTable) {
        paiementsTable.clear();
        paiements.forEach(paiement => {
            const modeBadge = getModePaiementBadge(paiement.mode_paiement);
            const statutBadge = getStatutBadge(paiement.statut);
            const montantFormatted = parseFloat(paiement.montant).toLocaleString() + ' Ar';
            
            paiementsTable.row.add([
                `<input type="checkbox" value="${paiement.id}">`,
                paiement.reference_achat || 'N/A',
                paiement.fournisseur_nom || 'N/A',
                paiement.date_paiement || 'N/A',
                modeBadge,
                paiement.reference_paiement || 'N/A',
                montantFormatted,
                statutBadge,
                paiement.effectue_par || 'N/A',
                `<button class="btn btn-sm btn-outline-info btn-view-paiement" data-id="${paiement.id}" title="Voir détails">
                    <i class="fas fa-eye"></i>
                </button> ` +
                `<button class="btn btn-sm btn-outline-success btn-print-receipt" data-id="${paiement.id}" title="Imprimer reçu">
                    <i class="fas fa-print"></i>
                </button>`
            ]);
        });
        paiementsTable.draw();
    }
}

function getModePaiementBadge(mode) {
    const badges = {
        'CHEQUE': '<span class="badge bg-primary status-badge">Chèque</span>',
        'VIREMENT': '<span class="badge bg-info status-badge">Virement</span>',
        'ESPECE': '<span class="badge bg-success status-badge">Espèce</span>'
    };
    return badges[mode] || '<span class="badge bg-secondary status-badge">Inconnu</span>';
}

// ===== CHARGEMENT DES DONNÉES DE RÉFÉRENCE =====

function loadFournisseurs() {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'producteurs'
    }).done(function(response) {
        if (response.success) {
            const select = $('#filterFournisseur');
            select.empty().append('<option value="">Tous les fournisseurs</option>');
            response.data.forEach(fournisseur => {
                select.append(`<option value="${fournisseur.id}">${fournisseur.nom}</option>`);
            });
        }
    });
}

function loadAchatsEnAttente() {
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                ae.id,
                ae.reference_achat,
                pr.nom as fournisseur_nom,
                COALESCE(SUM(ad.montant_ht), 0) as montant_total
              FROM achat_entete ae
              LEFT JOIN producteurs pr ON ae.fournisseur_id = pr.id
              LEFT JOIN achat_detail ad ON ae.id = ad.achat_entete_id
              WHERE ae.statut IN ('CONTROLE', 'A_PAYER')
              GROUP BY ae.id
              ORDER BY ae.date_creation DESC`,
        params: JSON.stringify([])
    }).done(function(response) {
        if (response.success) {
            const select = $('#achatSelect');
            select.empty().append('<option value="">Sélectionner un achat</option>');
            response.data.forEach(achat => {
                select.append(`<option value="${achat.id}" data-montant="${achat.montant_total}" data-fournisseur="${achat.fournisseur_nom}">${achat.reference_achat} - ${achat.fournisseur_nom} (${parseFloat(achat.montant_total).toLocaleString()} Ar)</option>`);
            });
        }
    });
}

// ===== GESTION DES ÉVÉNEMENTS =====

function setupEventHandlers() {
    // Boutons d'action
    $(document).on('click', '.btn-view-paiement', function() {
        const id = $(this).data('id');
        viewPaiement(id);
    });
    
    $(document).on('click', '.btn-print-receipt', function() {
        const id = $(this).data('id');
        printReceipt(id);
    });
    
    // Sauvegarde de paiement
    $('#btnSavePaiement').on('click', function() {
        savePaiement();
    });
    
    // Changement d'achat
    $('#achatSelect').on('change', function() {
        const achatId = $(this).val();
        if (achatId) {
            const option = $(this).find('option:selected');
            const fournisseur = option.data('fournisseur');
            
            // Charger les produits pour la saisie des prix
            loadProduitsForPaiement(achatId);
            
            // Vérifier les avances du fournisseur
            checkAvancesFournisseur(achatId, fournisseur);
        }
    });
    
    // Changement de mode de paiement
    $('#modePaiementSelect').on('change', function() {
        const mode = $(this).val();
        const referenceGroup = $('#referencePaiementGroup');
        const referenceLabel = $('#referencePaiementLabel');
        
        if (mode === 'CHEQUE') {
            referenceGroup.show();
            referenceLabel.text('Numéro de chèque');
            $('#referencePaiement').attr('placeholder', 'Numéro de chèque');
        } else if (mode === 'VIREMENT') {
            referenceGroup.show();
            referenceLabel.text('Référence virement');
            $('#referencePaiement').attr('placeholder', 'Référence virement');
        } else {
            referenceGroup.hide();
        }
    });
    
    // Génération de reçu
    $('#btnGenerateReceipt').on('click', function() {
        const selectedIds = getSelectedPaiementIds();
        if (selectedIds.length === 0) {
            showAlert('Erreur', 'Veuillez sélectionner au moins un paiement', 'warning');
            return;
        }
        // Générer les reçus un par un
        selectedIds.forEach(id => {
            printPaiement(id);
        });
    });
}

// ===== FONCTIONS CRUD =====

function viewPaiement(id) {
    $.post('../includes/traitement.php', {
        action: 'read',
        table: 'operation_caisse',
        data: JSON.stringify({ id: id })
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const paiement = response.data[0];
            displayDetailsPaiement(paiement);
            $('#modalDetailsPaiement').modal('show');
        } else {
            showAlert('Erreur', 'Paiement non trouvé', 'error');
        }
    });
}

function displayDetailsPaiement(paiement) {
    const statutBadge = getStatutBadge(paiement.statut);
    const html = `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Détails du Paiement</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Référence Achat:</strong> ${paiement.reference_achat || 'N/A'}</p>
                        <p><strong>Fournisseur:</strong> ${paiement.fournisseur_nom || 'N/A'}</p>
                        <p><strong>Date de Paiement:</strong> ${paiement.date_paiement || 'N/A'}</p>
                        <p><strong>Mode de Paiement:</strong> ${getModePaiementBadge(paiement.mode_paiement)}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Référence:</strong> ${paiement.reference_paiement || 'N/A'}</p>
                        <p><strong>Montant:</strong> ${parseFloat(paiement.montant).toLocaleString()} Ar</p>
                        <p><strong>Statut:</strong> ${statutBadge}</p>
                        <p><strong>Effectué par:</strong> ${paiement.effectue_par || 'N/A'}</p>
                        <p><strong>Commentaires:</strong> ${paiement.commentaires || 'Aucun'}</p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-warning" onclick="changePaiementStatus(${paiement.id})">
                                <i class="fas fa-edit"></i> Changer le Statut
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="cancelPaiement(${paiement.id})" 
                                    ${paiement.statut === 'ANNULE' ? 'disabled' : ''}>
                                <i class="fas fa-times"></i> Annuler le Paiement
                            </button>
                            <button class="btn btn-sm btn-success" onclick="printPaiement(${paiement.id})">
                                <i class="fas fa-print"></i> Imprimer Reçu
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#detailsPaiementContent').html(html);
}

function savePaiement() {
    const achatId = $('#achatSelect').val();
    const modePaiement = $('#modePaiementSelect').val();
    const datePaiement = $('#datePaiement').val();
    const referencePaiement = $('#referencePaiement').val();
    const statut = $('#statutPaiement').val();
    const commentaires = $('#commentairesPaiement').val();
    
    if (!achatId || !modePaiement || !datePaiement || !statut) {
        showAlert('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');
        return;
    }
    
    if ((modePaiement === 'CHEQUE' || modePaiement === 'VIREMENT') && !referencePaiement) {
        showAlert('Erreur', 'Veuillez saisir la référence de paiement', 'error');
        return;
    }
    
    // Collecter les prix des produits
    const produitsUpdates = [];
    $('#tableProduitsPrix tbody tr').each(function() {
        const produitId = $(this).data('produit-id');
        const prixUnitaire = parseFloat($(this).find('.prix-unitaire').val()) || 0;
        const reduction = parseFloat($(this).find('.reduction').val()) || 0;
        const quantite = parseFloat($(this).find('td:eq(2)').text()) || 0;
        const montantHT = (prixUnitaire * quantite) - reduction;
        
        produitsUpdates.push({
            id: produitId,
            prix_unitaire_net: prixUnitaire,
            reduction: reduction,
            montant_ht: montantHT
        });
    });
    
    // Calculer le montant total
    const montantTotal = produitsUpdates.reduce((sum, produit) => sum + produit.montant_ht, 0);
    
    const paiementData = {
        achat_id: parseInt(achatId),
        mode_paiement: modePaiement,
        date_paiement: datePaiement,
        reference_paiement: referencePaiement,
        montant: montantTotal,
        statut: statut,
        commentaires: commentaires,
        effectue_par: currentUser,
        type_operation: 'PAIEMENT_ACHAT'
    };
    
    // Mettre à jour les prix des produits d'abord
    $.post('../includes/traitement.php', {
        action: 'update_bulk',
        table: 'achat_detail',
        data: JSON.stringify(produitsUpdates)
    }).done(function(response) {
        if (response.success) {
            // Enregistrer le paiement
            $.post('../includes/traitement.php', {
                action: 'create',
                table: 'operation_caisse',
                data: JSON.stringify(paiementData)
            }).done(function(response) {
                if (response.success) {
                    // Mettre à jour le statut de l'achat en "PAYÉ" après enregistrement du paiement
                    updateAchatStatus(achatId, 'PAYE');
                    
                    // Générer la facture si le paiement est validé
                    if (statut === 'VALIDE') {
                        generateFacture(achatId);
                    }
                    
                    showAlert('Succès', 'Paiement enregistré avec succès', 'success');
                    $('#modalPaiement').modal('hide');
                    loadPaiements();
                    loadAchatsEnAttente();
                    resetPaiementForm();
                    
                    // Recharger la liste des achats si on est sur la page de gestion des achats
                    if (typeof window.parent !== 'undefined' && typeof window.parent.loadAchats === 'function') {
                        window.parent.loadAchats();
                    }
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            });
        } else {
            showAlert('Erreur', 'Impossible de mettre à jour les prix des produits', 'error');
        }
    });
}

function updateAchatStatus(achatId, newStatus) {
    $.post('../includes/traitement.php', {
        action: 'update',
        table: 'achat_entete',
        id: achatId,
        data: JSON.stringify({ statut: newStatus })
    }).done(function(response) {
        if (response.success) {
            console.log('Statut de l\'achat mis à jour vers:', newStatus);
            // Recharger la liste des achats pour refléter le changement
            if (typeof loadAchats === 'function') {
                loadAchats();
            }
        } else {
            console.error('Erreur lors de la mise à jour du statut:', response.message);
        }
    }).fail(function(xhr, status, error) {
        console.error('Erreur AJAX lors de la mise à jour du statut:', error);
    });
}

// ===== GESTION DES AVANCES =====

function checkAvancesFournisseur(achatId, fournisseur) {
    // Vérifier si le fournisseur a des avances
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                SUM(montant) as total_avances,
                SUM(CASE WHEN type_operation = 'AVANCE' THEN montant ELSE 0 END) as avances_donnees,
                SUM(CASE WHEN type_operation = 'REMBOURSEMENT' THEN montant ELSE 0 END) as remboursements
              FROM operation_caisse 
              WHERE fournisseur_id = (SELECT fournisseur_id FROM achat_entete WHERE id = ?)`,
        params: JSON.stringify([achatId])
    }).done(function(response) {
        if (response.success && response.data.length > 0) {
            const data = response.data[0];
            const avances = parseFloat(data.avances_donnees || 0);
            const remboursements = parseFloat(data.remboursements || 0);
            const soldeAvance = avances - remboursements;
            
            if (soldeAvance > 0) {
                $('#montantAvance').text(soldeAvance.toLocaleString() + ' Ar');
                $('#resteRembourser').text(soldeAvance.toLocaleString() + ' Ar');
                $('#avanceInfo').show();
            } else {
                $('#avanceInfo').hide();
            }
        }
    });
}

// ===== FONCTIONS UTILITAIRES =====

function setDefaultDate() {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    $('#datePaiement').val(todayStr);
}

function initSelect2() {
    // Initialiser Select2 sur les sélecteurs
    $('#achatSelect, #modePaiementSelect, #filterFournisseur, #filterModePaiement').select2({
        theme: 'bootstrap-5',
        placeholder: 'Sélectionner...',
        allowClear: true
    });
}

function getSelectedPaiementIds() {
    const selectedIds = [];
    $('#tablePaiements input[type="checkbox"]:checked').each(function() {
        const row = $(this).closest('tr');
        const id = row.find('button[data-id]').first().data('id');
        if (id) selectedIds.push(id);
    });
    return selectedIds;
}

function resetPaiementForm() {
    $('#formPaiement')[0].reset();
    $('#avanceInfo').hide();
    setDefaultDate();
}

// ===== GÉNÉRATION DE REÇU =====

function printReceipt(id) {
    printPaiement(id);
}


// ===== FONCTIONS D'ALERTE =====

function showAlert(title, message, type) {
    const iconMap = { 'success': 'success', 'error': 'error', 'warning': 'warning', 'info': 'info', 'danger': 'error' };
    Swal.fire({
        title: title,
        html: message,
        icon: iconMap[type] || 'info',
        confirmButtonText: 'OK',
        timer: type === 'success' ? 3000 : null,
        timerProgressBar: type === 'success'
    });
}

function showConfirm(title, text, confirmButtonText = 'Oui', cancelButtonText = 'Non') {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText
    });
}

// ===== GESTION DES PRIX PAR PRODUIT =====

function loadProduitsForPaiement(achatId) {
    $.post('../includes/traitement.php', {
        action: 'execute_sql',
        table: 'produits',
        sql: `SELECT 
                ad.id,
                ad.produit_id,
                p.nom as produit_nom,
                ad.lot_numero,
                ad.qte_nette_controlee,
                ad.prix_unitaire_net,
                ad.reduction,
                ad.montant_ht,
                ad.producteurs_json
              FROM achat_detail ad
              LEFT JOIN produits p ON ad.produit_id = p.id
              WHERE ad.achat_entete_id = ?
              ORDER BY ad.id`,
        params: JSON.stringify([achatId])
    }).done(function(response) {
        if (response.success) {
            displayProduitsForPaiement(response.data);
        } else {
            console.error('Erreur lors du chargement des produits:', response.message);
        }
    }).fail(function() {
        console.error('Erreur lors du chargement des produits');
    });
}

function displayProduitsForPaiement(produits) {
    const tbody = $('#tableProduitsPrix tbody');
    tbody.empty();
    
    produits.forEach(produit => {
        const row = `
            <tr data-produit-id="${produit.id}">
                <td>${produit.produit_nom || 'N/A'}</td>
                <td>${produit.lot_numero || 'N/A'}</td>
                <td>${parseFloat(produit.qte_nette_controlee || 0).toFixed(2)}</td>
                <td>
                    <input type="number" class="form-control form-control-sm prix-unitaire" 
                           value="${parseFloat(produit.prix_unitaire_net || 0)}" 
                           step="0.01" data-produit-id="${produit.id}">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm reduction" 
                           value="${parseFloat(produit.reduction || 0)}" 
                           step="0.01" data-produit-id="${produit.id}">
                </td>
                <td>
                    <span class="montant-ht" data-produit-id="${produit.id}">
                        ${parseFloat(produit.montant_ht || 0).toLocaleString()}
                    </span> Ar
                </td>
            </tr>
        `;
        tbody.append(row);
    });
    
    // Event handlers pour les calculs automatiques
    $('.prix-unitaire, .reduction').on('input', function() {
        updateMontantProduit($(this).data('produit-id'));
    });
    
    updateTotauxPaiement();
}

function updateMontantProduit(produitId) {
    const row = $(`tr[data-produit-id="${produitId}"]`);
    const prixUnitaire = parseFloat(row.find('.prix-unitaire').val()) || 0;
    const reduction = parseFloat(row.find('.reduction').val()) || 0;
    const quantite = parseFloat(row.find('td:eq(2)').text()) || 0;
    
    const montantHT = (prixUnitaire * quantite) - reduction;
    row.find('.montant-ht').text(montantHT.toLocaleString());
    
    updateTotauxPaiement();
}

function updateTotauxPaiement() {
    let montantHT = 0;
    let montantReduction = 0;
    
    $('#tableProduitsPrix tbody tr').each(function() {
        const prixUnitaire = parseFloat($(this).find('.prix-unitaire').val()) || 0;
        const quantite = parseFloat($(this).find('td:eq(2)').text()) || 0;
        const reduction = parseFloat($(this).find('.reduction').val()) || 0;
        
        const montantProduit = (prixUnitaire * quantite) - reduction;
        montantHT += montantProduit;
        montantReduction += reduction;
    });
    
    // Calculer le TTC (HT + TVA 20%)
    const montantTTC = montantHT * 1.20;
    
    $('#montantHT').text(montantHT.toLocaleString() + ' Ar');
    $('#montantReduction').text(montantReduction.toLocaleString() + ' Ar');
    $('#montantTTC').text(montantTTC.toLocaleString() + ' Ar');
}

// ===== GESTION DES STATUTS ET ANNULATIONS =====

function changePaiementStatus(paiementId) {
    Swal.fire({
        title: 'Changer le statut du paiement',
        input: 'select',
        inputOptions: {
            'EN_ATTENTE': 'En Attente',
            'VALIDE': 'Validé',
            'ANNULE': 'Annulé'
        },
        inputPlaceholder: 'Sélectionner un statut',
        showCancelButton: true,
        confirmButtonText: 'Changer',
        cancelButtonText: 'Annuler',
        inputValidator: (value) => {
            if (!value) {
                return 'Veuillez sélectionner un statut !';
            }
        }
    }).then((result) => {
        if (result.isConfirmed) {
            $.post('../includes/traitement.php', {
                action: 'update',
                table: 'operation_caisse',
                id: paiementId,
                data: JSON.stringify({ 
                    statut: result.value,
                    dernier_modif_par: currentUser
                })
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', 'Statut du paiement mis à jour', 'success');
                    loadPaiements();
                    $('#modalDetailsPaiement').modal('hide');
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Impossible de mettre à jour le statut', 'error');
            });
        }
    });
}

function cancelPaiement(paiementId) {
    showConfirm('Confirmer l\'annulation', 'Êtes-vous sûr de vouloir annuler ce paiement ?', 'Oui, annuler', 'Non')
    .then((result) => {
        if (result.isConfirmed) {
            $.post('../includes/traitement.php', {
                action: 'update',
                table: 'operation_caisse',
                id: paiementId,
                data: JSON.stringify({ 
                    statut: 'ANNULE',
                    dernier_modif_par: currentUser
                })
            }).done(function(response) {
                if (response.success) {
                    showAlert('Succès', 'Paiement annulé', 'success');
                    loadPaiements();
                    $('#modalDetailsPaiement').modal('hide');
                } else {
                    showAlert('Erreur', response.message, 'error');
                }
            }).fail(function() {
                showAlert('Erreur', 'Impossible d\'annuler le paiement', 'error');
            });
        }
    });
}

// ===== GÉNÉRATION DE FACTURE =====

function generateFacture(achatId) {
    $.post('./includes/generate_achat_pdf.php', {
        achat_id: achatId,
        type: 'facture'
    }).done(function(response) {
        if (response.success) {
            // Ouvrir la facture dans une nouvelle fenêtre
            window.open(response.pdf_url, '_blank');
        } else {
            console.error('Erreur lors de la génération de la facture:', response.message);
        }
    }).fail(function() {
        console.error('Erreur lors de la génération de la facture');
    });
}

function printPaiement(paiementId) {
    // Ouvrir le reçu dans un nouvel onglet (pas de PDF, juste HTML)
    const url = `./facture_paiement_pdf.php?id=${paiementId}`;
    window.open(url, '_blank');
}

// ===== GESTION DU STATUT =====

function getStatutBadge(statut) {
    const badges = {
        'EN_ATTENTE': '<span class="badge bg-warning status-badge">En Attente</span>',
        'VALIDE': '<span class="badge bg-success status-badge">Validé</span>',
        'ANNULE': '<span class="badge bg-danger status-badge">Annulé</span>'
    };
    return badges[statut] || '<span class="badge bg-secondary status-badge">Inconnu</span>';
}

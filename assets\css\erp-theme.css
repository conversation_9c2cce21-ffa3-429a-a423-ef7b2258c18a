/* ===================================================================
   ERP3 - Thème Professionnel Unifié
   Inspiré de phpMyAdmin "pmahomme" - Style épuré et professionnel
   =================================================================== */

/* Variables CSS pour la cohérence */
:root {
    /* Couleurs principales */
    --primary-color: #2c3e50;
    --secondary-color: #34495e;
    --accent-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    
    /* Couleurs de fond */
    --light-bg: #f8f9fa;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    
    /* Couleurs de texte */
    --text-color: #2c3e50;
    --text-muted: #6c757d;
    --text-light: #ffffff;
    
    /* Bordures et ombres */
    --border-color: #dee2e6;
    --border-radius: 8px;
    --border-radius-sm: 6px;
    --border-radius-lg: 12px;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    --shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
    --shadow-lg: 0 1rem 3rem rgba(0,0,0,0.175);
    
    /* Espacements */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Typographie */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-xxl: 1.5rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-base: 0.2s ease;
    --transition-slow: 0.3s ease;
}

/* ===================================================================
   RESET ET BASE
   =================================================================== */

* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--light-bg);
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* ===================================================================
   EN-TÊTE PRINCIPAL
   =================================================================== */

.main-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-light);
    padding: var(--spacing-md) 0;
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.main-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.main-header h1 {
    margin: 0;
    font-size: var(--font-size-xxl);
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.main-header .text-light {
    position: relative;
    z-index: 1;
}

/* ===================================================================
   CARTES ET CONTENEURS
   =================================================================== */

.card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    background: var(--white);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
    transition: var(--transition-base);
}

.card:hover {
    box-shadow: var(--shadow);
}

.card-header {
    background: var(--gray-100);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h5,
.card-header h6 {
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: var(--spacing-lg);
}

.card-body.p-0 {
    padding: 0;
}

/* ===================================================================
   BOUTONS
   =================================================================== */

.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    transition: var(--transition-base);
    border: 1px solid transparent;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    font-size: var(--font-size-sm);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-light);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--text-light);
}

.btn-secondary {
    background-color: var(--gray-500);
    border-color: var(--gray-500);
    color: var(--text-light);
}

.btn-secondary:hover {
    background-color: var(--gray-600);
    border-color: var(--gray-600);
    color: var(--text-light);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--text-light);
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
    color: var(--text-light);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--gray-900);
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    color: var(--gray-900);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--text-light);
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: var(--text-light);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: var(--text-light);
}

.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
    color: var(--text-light);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    color: var(--text-light);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-secondary {
    color: var(--gray-500);
    border-color: var(--gray-500);
    background-color: transparent;
}

.btn-outline-secondary:hover {
    color: var(--text-light);
    background-color: var(--gray-500);
    border-color: var(--gray-500);
}

.btn-outline-warning {
    color: var(--warning-color);
    border-color: var(--warning-color);
    background-color: transparent;
}

.btn-outline-warning:hover {
    color: var(--gray-900);
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
    background-color: transparent;
}

.btn-outline-danger:hover {
    color: var(--text-light);
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-outline-info {
    color: var(--info-color);
    border-color: var(--info-color);
    background-color: transparent;
}

.btn-outline-info:hover {
    color: var(--text-light);
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

.btn-group {
    display: inline-flex;
    gap: var(--spacing-xs);
}

/* ===================================================================
   TABLEAUX
   =================================================================== */

.table {
    margin-bottom: 0;
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background-color: var(--gray-100);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--text-color);
    padding: var(--spacing-sm);
    text-align: left;
    vertical-align: middle;
}

.table td {
    padding: var(--spacing-sm);
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
}

.table tbody tr:hover {
    background-color: var(--gray-100);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,0.02);
}

.table-hover tbody tr:hover {
    background-color: var(--gray-100);
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* ===================================================================
   BADGES ET STATUTS
   =================================================================== */

.badge {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    display: inline-block;
}

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--gray-500) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

/* Badges de statut personnalisés */
.status-badge {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-brouillon {
    background-color: var(--gray-500);
    color: var(--text-light);
}

.status-en_cours {
    background-color: var(--warning-color);
    color: var(--gray-900);
}

.status-valide {
    background-color: var(--success-color);
    color: var(--text-light);
}

.status-a_payer {
    background-color: var(--info-color);
    color: var(--text-light);
}

.status-paye {
    background-color: var(--primary-color);
    color: var(--text-light);
}

/* ===================================================================
   FORMULAIRES
   =================================================================== */

.form-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: var(--spacing-xs);
    display: block;
}

.form-control,
.form-select {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    transition: var(--transition-base);
    background-color: var(--white);
    color: var(--text-color);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
    outline: none;
}

.form-control:disabled,
.form-select:disabled {
    background-color: var(--gray-100);
    opacity: 0.6;
    cursor: not-allowed;
}

.form-check-input {
    margin-right: var(--spacing-xs);
}

.form-floating {
    position: relative;
}

.form-floating .form-control {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
}

.form-floating label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem 0.75rem;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: var(--transition-base);
}

/* ===================================================================
   MODALES
   =================================================================== */

.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background: var(--primary-color);
    color: var(--text-light);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: none;
}

.modal-header .modal-title {
    font-weight: 600;
    margin: 0;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--gray-100);
}

/* ===================================================================
   STATISTIQUES
   =================================================================== */

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-light);
    border: none;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.stats-card .card-body {
    padding: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.stats-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin: 0;
}

/* ===================================================================
   NIVEAUX DE STOCK
   =================================================================== */

.stock-level {
    font-weight: 600;
}

.stock-low {
    color: var(--danger-color);
}

.stock-medium {
    color: var(--warning-color);
}

.stock-high {
    color: var(--success-color);
}

/* ===================================================================
   BOUTONS D'ACTION
   =================================================================== */

.action-buttons {
    white-space: nowrap;
    display: flex;
    gap: var(--spacing-xs);
}

.action-buttons .btn {
    margin-right: 0;
}

/* ===================================================================
   ALERTES
   =================================================================== */

.alert {
    border-radius: var(--border-radius-sm);
    border: none;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    position: relative;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* ===================================================================
   UTILITAIRES
   =================================================================== */

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.text-muted { color: var(--text-muted) !important; }
.text-center { text-align: center !important; }
.text-end { text-align: right !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }

.me-1 { margin-right: var(--spacing-xs) !important; }
.me-2 { margin-right: var(--spacing-sm) !important; }

/* ===================================================================
   RESPONSIVE
   =================================================================== */

@media (max-width: 768px) {
    .main-header h1 {
        font-size: var(--font-size-xl);
    }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
    
    .modal-dialog {
        margin: var(--spacing-sm);
    }
    
    .stats-number {
        font-size: 1.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 576px) {
    .main-header {
        padding: var(--spacing-sm) 0;
        margin-bottom: var(--spacing-md);
    }
    
    .main-header h1 {
        font-size: var(--font-size-lg);
    }
    
    .card-header {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
    
    .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        width: 100%;
        justify-content: center;
    }
}

/* ===================================================================
   ANIMATIONS
   =================================================================== */

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn var(--transition-slow);
}

.slide-in {
    animation: slideIn var(--transition-slow);
}

/* ===================================================================
   PRINT STYLES
   =================================================================== */

@media print {
    .main-header,
    .btn,
    .modal,
    .action-buttons {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}

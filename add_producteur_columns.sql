-- ===== AJOUT DE COLONNES À LA TABLE PRODUCTEURS =====
-- Ajouter des colonnes pour avoir des informations plus complètes sur les fournisseurs

-- Ajouter les colonnes si elles n'existent pas
ALTER TABLE `producteurs` 
ADD COLUMN IF NOT EXISTS `adresse` TEXT DEFAULT NULL AFTER `contact`,
ADD COLUMN IF NOT EXISTS `telephone` VARCHAR(30) DEFAULT NULL AFTER `adresse`,
ADD COLUMN IF NOT EXISTS `email` VARCHAR(255) DEFAULT NULL AFTER `telephone`;

-- ===== MISE À JOUR DE LA VUE AVEC LES NOUVELLES COLONNES =====
-- Re<PERSON><PERSON>er la vue avec les colonnes complètes

DROP VIEW IF EXISTS `v_paiements_detaille`;

CREATE VIEW `v_paiements_detaille` AS
SELECT 
    oc.id,
    oc.achat_id,
    ae.reference_achat,
    p.nom as fournisseur_nom,
    p.adresse as fournisseur_adresse,
    p.telephone as fournisseur_telephone,
    p.email as fournisseur_email,
    p.contact as fournisseur_contact,
    p.site as fournisseur_site,
    p.classement as fournisseur_classement,
    oc.type_operation,
    oc.mode_paiement,
    oc.reference_paiement,
    oc.montant,
    oc.date_paiement,
    oc.effectue_par,
    oc.commentaires,
    oc.date_creation,
    oc.cree_par
FROM operation_caisse oc
LEFT JOIN achat_entete ae ON oc.achat_id = ae.id
LEFT JOIN producteurs p ON ae.fournisseur_id = p.id
WHERE oc.type_operation = 'PAIEMENT_ACHAT'
ORDER BY oc.date_paiement DESC;

-- ===== MISE À JOUR DES DONNÉES DE TEST =====
-- Mettre à jour quelques enregistrements avec les nouvelles colonnes

UPDATE `producteurs` 
SET 
    adresse = 'Antsiranana, Madagascar',
    telephone = '032284687524',
    email = '<EMAIL>'
WHERE id = 1;

UPDATE `producteurs` 
SET 
    adresse = 'Ambanja, Madagascar',
    telephone = '0342563989',
    email = '<EMAIL>'
WHERE id = 2;

-- ===== COMMENTAIRES =====
-- Cette approche ajoute les colonnes manquantes à la table producteurs
-- et met à jour la vue pour utiliser ces colonnes
-- 
-- Avantages :
-- - Informations complètes sur les fournisseurs
-- - Compatibilité avec le code existant
-- - Meilleure traçabilité
-- 
-- Inconvénients :
-- - Modification de la structure existante
-- - Nécessite une migration des données
